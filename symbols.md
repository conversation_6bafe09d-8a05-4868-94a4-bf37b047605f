# 命名标识符登记表

## 数据库表名
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| users | 表名 | db | 用户信息表 |
| nodes | 表名 | db | 节点信息表 |
| connections | 表名 | db | 连接信息表 |
| configs | 表名 | db | 配置信息表 |
| policies | 表名 | db | 策略信息表 |
| logs | 表名 | db | 日志信息表 |

## 结构体名称
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| User | 结构体 | models | 用户数据模型 |
| Node | 结构体 | models | 节点数据模型 |
| Connection | 结构体 | models | 连接数据模型 |
| Config | 结构体 | models | 配置数据模型 |
| Policy | 结构体 | models | 策略数据模型 |
| Log | 结构体 | models | 日志数据模型 |
| AppConfig | 结构体 | config | 应用配置 |
| DatabaseConfig | 结构体 | config | 数据库配置 |
| ServerConfig | 结构体 | config | 服务器配置 |
| LogConfig | 结构体 | config | 日志配置 |
| NetworkConfig | 结构体 | config | 网络配置 |
| ConfigManager | 结构体 | config | 配置管理器 |
| ApiResponse | 结构体 | models | API响应结构 |
| PaginationParams | 结构体 | models | 分页参数 |
| PaginatedResponse | 结构体 | models | 分页响应 |
| Claims | 结构体 | models | JWT声明 |
| AppState | 结构体 | main | 应用状态 |
| ApiState | 结构体 | api | API状态 |

## 服务类名称
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| UserService | 服务类 | api::users | 用户服务 |
| NodeService | 服务类 | api::nodes | 节点服务 |
| ConnectionService | 服务类 | api::connections | 连接服务 |
| ConfigService | 服务类 | api::configs | 配置服务 |
| Database | 服务类 | db | 数据库服务 |
| LogManager | 服务类 | log | 日志管理器 |
| NATDetector | 服务类 | net::nat | NAT探测器 |
| TincConnectionManager | 服务类 | net::tinc | Tinc连接管理器 |

## 函数名称
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| create_router | 函数 | api | 创建API路由 |
| auth_middleware | 函数 | api | 认证中间件 |
| health_check | 函数 | main | 健康检查 |
| shutdown_signal | 函数 | main | 优雅关闭 |
| create_user | 函数 | api::users | 创建用户 |
| list_users | 函数 | api::users | 用户列表 |
| get_user | 函数 | api::users | 获取用户 |
| update_user | 函数 | api::users | 更新用户 |
| delete_user | 函数 | api::users | 删除用户 |
| login | 函数 | api::users | 用户登录 |
| create_node | 函数 | api::nodes | 创建节点 |
| list_nodes | 函数 | api::nodes | 节点列表 |
| get_node | 函数 | api::nodes | 获取节点 |
| update_node | 函数 | api::nodes | 更新节点 |
| delete_node | 函数 | api::nodes | 删除节点 |
| detect_nat | 函数 | api::nodes | NAT探测 |

## 常量名称
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| DEFAULT_PORT | 常量 | config | 默认端口号 |
| DEFAULT_MTU | 常量 | config | 默认MTU |
| JWT_SECRET | 常量 | auth | JWT密钥 |
| TOKEN_EXPIRY | 常量 | auth | Token过期时间 |
| MAX_CONNECTIONS | 常量 | db | 最大连接数 |
| HEARTBEAT_INTERVAL | 常量 | net | 心跳间隔 |
| CONNECTION_TIMEOUT | 常量 | net | 连接超时 |

## 枚举名称
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| UserRole | 枚举 | models | 用户角色 |
| NodeType | 枚举 | models | 节点类型 |
| NodeStatus | 枚举 | models | 节点状态 |
| TunnelType | 枚举 | models | 隧道类型 |
| NATType | 枚举 | models | NAT类型 |
| ConnectionMethod | 枚举 | models | 连接方法 |
| ConnectionStatus | 枚举 | models | 连接状态 |
| LogType | 枚举 | models | 日志类型 |

## 错误类型
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| AppError | 错误类型 | error | 应用错误 |
| ConfigError | 错误类型 | error | 配置错误 |
| DatabaseError | 错误类型 | error | 数据库错误 |
| NetworkError | 错误类型 | error | 网络错误 |
| AuthError | 错误类型 | error | 认证错误 |

## API路径
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| /api/login | 路径 | api | 用户登录 |
| /api/profile/me | 路径 | api | 用户信息 |
| /api/nodes | 路径 | api | 节点管理 |
| /api/connections | 路径 | api | 连接管理 |
| /api/configs | 路径 | api | 配置管理 |
| /api/users | 路径 | api | 用户管理 |
| /api/roles | 路径 | api | 角色管理 |
| /api/logs | 路径 | api | 日志查询 |
| /api/events | 路径 | api | 事件流 |
| /metrics | 路径 | api | 监控指标 |

## 配置字段
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| database_url | 字段 | config | 数据库连接URL |
| max_connections | 字段 | config | 最大连接数 |
| connect_timeout | 字段 | config | 连接超时 |
| server_host | 字段 | config | 服务器地址 |
| server_port | 字段 | config | 服务器端口 |
| log_level | 字段 | config | 日志级别 |
| log_file | 字段 | config | 日志文件 |
| max_size | 字段 | config | 最大文件大小 |
| max_files | 字段 | config | 最大文件数 |
| listen_addr | 字段 | config | 监听地址 |
| interface_name | 字段 | config | 接口名称 |
| virtual_network | 字段 | config | 虚拟网络 |
| mtu | 字段 | config | MTU值 |
| enable_nat | 字段 | config | 启用NAT |
| enable_compression | 字段 | config | 启用压缩 |
| enable_encryption | 字段 | config | 启用加密 |
| connection_timeout | 字段 | config | 连接超时 |
| heartbeat_interval | 字段 | config | 心跳间隔 |

## 数据库字段
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| id | 字段 | models | 主键ID |
| username | 字段 | models | 用户名 |
| password_hash | 字段 | models | 密码哈希 |
| email | 字段 | models | 邮箱地址 |
| role | 字段 | models | 用户角色 |
| created_at | 字段 | models | 创建时间 |
| updated_at | 字段 | models | 更新时间 |
| last_login | 字段 | models | 最后登录 |
| name | 字段 | models | 名称 |
| node_type | 字段 | models | 节点类型 |
| ip | 字段 | models | IP地址 |
| mac | 字段 | models | MAC地址 |
| port | 字段 | models | 端口号 |
| public_key | 字段 | models | 公钥 |
| status | 字段 | models | 状态 |
| tunnel_type | 字段 | models | 隧道类型 |
| nat_type | 字段 | models | NAT类型 |
| last_active | 字段 | models | 最后活跃 |
| client_id | 字段 | models | 客户端ID |
| server_id | 字段 | models | 服务端ID |
| method_used | 字段 | models | 连接方法 |
| latency | 字段 | models | 延迟 |
| version | 字段 | models | 版本号 |
| hash | 字段 | models | 哈希值 |
| content | 字段 | models | 内容 |
| description | 字段 | models | 描述 |
| bandwidth_limit | 字段 | models | 带宽限制 |
| allow_ips | 字段 | models | 允许IP |
| allow_ports | 字段 | models | 允许端口 |
| log_type | 字段 | models | 日志类型 |
| message | 字段 | models | 消息内容 |
| source | 字段 | models | 来源 |

## 环境变量
| 名称 | 类型 | 所属模块 | 用途说明 |
|------|------|----------|----------|
| DATABASE_URL | 环境变量 | config | 数据库连接URL |
| JWT_SECRET | 环境变量 | auth | JWT密钥 |
| LOG_LEVEL | 环境变量 | log | 日志级别 |
| SERVER_PORT | 环境变量 | server | 服务器端口 |
| CONFIG_DIR | 环境变量 | config | 配置目录 |
