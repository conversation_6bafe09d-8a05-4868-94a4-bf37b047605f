[package]
name = "tinc_rust"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "基于 Rust 语言重写的 Tinc VPN 系统"
license = "MIT"
repository = "https://github.com/your-username/tinc-rust"
readme = "README.md"
keywords = ["vpn", "tinc", "p2p", "nat", "rust"]
categories = ["network-programming", "security"]

[dependencies]
# 异步运行时
tokio = { version = "1.36", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec"] }

# Web 框架
axum = { version = "0.7", features = ["macros"] }
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["trace", "cors"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "mysql", "uuid", "chrono", "json"] }
uuid = { version = "1.7", features = ["v4", "serde"] }

# 加密
ring = "0.17"
base64 = "0.21"
rand = "0.8"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
log = "0.4"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 工具
chrono = { version = "0.4", features = ["serde"] }
futures = "0.3"
async-trait = "0.1"
bytes = "1.5"
pin-project = "1.1"

# 压缩
flate2 = "1.0"
tar = "0.4"

# 测试
tokio-test = "0.4"
mockall = "0.12"

# 新增依赖
sha256 = "1.1.3"
tempfile = "3.10"  # 用于测试
ipnetwork = "0.20" # 用于CIDR处理
cidr = "0.3"      # 用于CIDR验证
sha2 = "0.10"
bcrypt = "0.17.0"
jsonwebtoken = "9.3.1"
tracing-appender = "0.2.3"

# WebSocket 支持
tokio-tungstenite = "0.21"

# NAT 探测与 STUN
stun = "0.4"

# 配置文件加密
aes-gcm = "0.10"

# Prometheus 监控
prometheus = "0.13"

# 命令行参数
clap = { version = "4.0", features = ["derive"] }

# 系统集成
nix = "0.27"
libc = "0.2"

# HTTP 客户端
reqwest = { version = "0.11", features = ["json"] }

# 序列化
bincode = "1.3"

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.12"
tempfile = "3.10"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true

[profile.test]
opt-level = 0
debug = true

[workspace]
members = [
    "crates/*",
]

[features]
default = []
full = [
    "sqlx/postgres",
    "sqlx/runtime-tokio-rustls",
    "sqlx/uuid",
    "sqlx/chrono",
    "sqlx/json",
] 
