[package]
name = "tinc_rust"
version = "0.1.0"
edition = "2021"
authors = ["Tinc Rust Team"]
description = "A modern Rust implementation of Tinc VPN with advanced NAT traversal"
license = "MIT"
repository = "https://github.com/tinc-rust/tinc-rust"
homepage = "https://github.com/tinc-rust/tinc-rust"
keywords = ["vpn", "tinc", "p2p", "nat", "rust"]
categories = ["network-programming", "security"]

[dependencies]
# 异步运行时
tokio = { version = "1.36", features = ["full"] }

# Web 框架
axum = { version = "0.7", features = ["macros"] }
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["trace", "cors"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# UUID
uuid = { version = "1.7", features = ["v4", "serde"] }

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 时间
chrono = { version = "0.4", features = ["serde"] }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true

[[bin]]
name = "tinc_rust_final"
path = "src/main_final.rs"
