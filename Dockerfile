# 构建阶段
FROM rust:1.75 as builder

WORKDIR /usr/src/tinc_rust

# 安装依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制源代码
COPY . .

# 构建项目
RUN cargo build --release

# 运行阶段
FROM debian:bullseye-slim

WORKDIR /usr/local/bin

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl1.1 \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制二进制文件
COPY --from=builder /usr/src/tinc_rust/target/release/tinc_rust .

# 创建数据目录
RUN mkdir -p /data/configs /data/logs

# 设置环境变量
ENV RUST_LOG=info
ENV DATABASE_URL=************************************

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["./tinc_rust"] 