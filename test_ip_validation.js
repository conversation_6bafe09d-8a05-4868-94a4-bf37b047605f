// 测试 IP 地址验证逻辑

const validateIP = (value) => {
  console.log('IP 验证器，输入值:', value)
  
  // 简单的 IP 地址验证
  const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/
  if (!ipPattern.test(value)) {
    console.log('IP 格式验证失败')
    return new Error('请输入有效的 IP 地址格式')
  }
  
  // 检查每个数字是否在 0-255 范围内
  const parts = value.split('.')
  for (let part of parts) {
    const num = parseInt(part)
    if (num < 0 || num > 255) {
      console.log('IP 范围验证失败')
      return new Error('IP 地址每段应在 0-255 范围内')
    }
  }
  console.log('IP 验证通过')
  return true
}

// 测试用例
const testIPs = [
  '**********',
  '*************',
  '127.0.0.1',
  '***************',
  '0.0.0.0',
  '256.1.1.1',  // 应该失败
  'invalid',    // 应该失败
  '192.168.1'   // 应该失败
]

testIPs.forEach(ip => {
  console.log(`\n测试 IP: ${ip}`)
  const result = validateIP(ip)
  console.log('结果:', result === true ? '通过' : result.message)
})
