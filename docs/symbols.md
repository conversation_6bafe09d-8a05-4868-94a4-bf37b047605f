# 符号定义表

## 网络配置管理模块

| 名称 | 类型 | 所属模块 | 用途说明 | 使用示例 |
|------|------|----------|----------|----------|
| `NetworkConfig` | 结构体 | 网络配置 | 网络配置数据结构 | `let config = NetworkConfig::new()` |
| `NetworkConfigManager` | 结构体 | 网络配置 | 网络配置管理器 | `let manager = NetworkConfigManager::new()` |
| `load_network_config()` | 函数 | 网络配置 | 加载网络配置 | `let config = load_network_config().await?` |
| `save_network_config()` | 函数 | 网络配置 | 保存网络配置 | `save_network_config(&config).await?` |
| `validate_network_config()` | 函数 | 网络配置 | 验证网络配置 | `validate_network_config(&config)?` |
| `NetworkConfigError` | 枚举 | 网络配置 | 网络配置错误类型 | `NetworkConfigError::InvalidConfig` |

## 数据库模块

| 名称 | 类型 | 所属模块 | 用途说明 | 使用示例 |
|------|------|----------|----------|----------|
| `DatabaseConfig` | 结构体 | 数据库 | 数据库配置结构 | `let db_config = DatabaseConfig::new()` |
| `DatabaseManager` | 结构体 | 数据库 | 数据库管理器 | `let db = DatabaseManager::new()` |
| `init_database()` | 函数 | 数据库 | 初始化数据库 | `init_database().await?` |
| `DatabaseError` | 枚举 | 数据库 | 数据库错误类型 | `DatabaseError::ConnectionFailed` |

## 日志模块

| 名称 | 类型 | 所属模块 | 用途说明 | 使用示例 |
|------|------|----------|----------|----------|
| `Logger` | 结构体 | 日志 | 日志管理器 | `let logger = Logger::new()` |
| `LogLevel` | 枚举 | 日志 | 日志级别 | `LogLevel::Info` |
| `LogConfig` | 结构体 | 日志 | 日志配置 | `let log_config = LogConfig::new()` |
| `init_logger()` | 函数 | 日志 | 初始化日志系统 | `init_logger(&config).await?` |

## 配置管理模块

| 名称 | 类型 | 所属模块 | 用途说明 | 使用示例 |
|------|------|----------|----------|----------|
| `ConfigManager` | 结构体 | 配置 | 配置管理器 | `let config = ConfigManager::new()` |
| `load_config()` | 函数 | 配置 | 加载配置 | `let config = load_config().await?` |
| `save_config()` | 函数 | 配置 | 保存配置 | `save_config(&config).await?` |
| `ConfigError` | 枚举 | 配置 | 配置错误类型 | `ConfigError::FileNotFound` |

## 错误处理模块

| 名称 | 类型 | 所属模块 | 用途说明 | 使用示例 |
|------|------|----------|----------|----------|
| `AppError` | 枚举 | 错误处理 | 应用错误类型 | `AppError::ConfigError` |
| `ErrorContext` | 结构体 | 错误处理 | 错误上下文 | `let ctx = ErrorContext::new()` |
| `handle_error()` | 函数 | 错误处理 | 错误处理函数 | `handle_error(error).await` |

## 工具模块

| 名称 | 类型 | 所属模块 | 用途说明 | 使用示例 |
|------|------|----------|----------|----------|
| `utils` | 模块 | 工具 | 工具函数集合 | `use crate::utils` |
| `format_error()` | 函数 | 工具 | 格式化错误信息 | `format_error(error)` |
| `validate_input()` | 函数 | 工具 | 验证输入数据 | `validate_input(data)?` |

## 常量定义

| 名称 | 类型 | 所属模块 | 用途说明 | 使用示例 |
|------|------|----------|----------|----------|
| `DEFAULT_CONFIG_PATH` | 常量 | 配置 | 默认配置文件路径 | `DEFAULT_CONFIG_PATH` |
| `MAX_RETRY_COUNT` | 常量 | 工具 | 最大重试次数 | `MAX_RETRY_COUNT` |
| `DEFAULT_TIMEOUT` | 常量 | 工具 | 默认超时时间 | `DEFAULT_TIMEOUT` | 