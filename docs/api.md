# Tinc VPN API 文档

## 概述

本文档描述了 Tinc VPN 系统的 RESTful API 接口。所有 API 请求都需要在 HTTP 头部包含 `Authorization: Bearer <token>` 进行身份验证。

## 基础 URL

```
http://localhost:3000/api
```

## 响应格式

所有 API 响应都使用以下 JSON 格式：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    }
}
```

## 错误响应

错误响应使用以下格式：

```json
{
    "code": 400,
    "message": "error",
    "error": "错误描述"
}
```

## API 端点

### 用户管理

#### 用户注册

```http
POST /users/register
```

请求体：

```json
{
    "username": "string",
    "password": "string",
    "email": "string"
}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "role": "string",
        "created_at": "datetime",
        "updated_at": "datetime"
    }
}
```

#### 用户登录

```http
POST /users/login
```

请求体：

```json
{
    "username": "string",
    "password": "string"
}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "token": "string",
        "user": {
            "id": "uuid",
            "username": "string",
            "email": "string",
            "role": "string"
        }
    }
}
```

#### 获取用户信息

```http
GET /users/{id}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "role": "string",
        "created_at": "datetime",
        "updated_at": "datetime"
    }
}
```

#### 获取用户列表

```http
GET /users?page=1&page_size=10
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 10,
        "data": [
            {
                "id": "uuid",
                "username": "string",
                "email": "string",
                "role": "string",
                "created_at": "datetime",
                "updated_at": "datetime"
            }
        ]
    }
}
```

### 节点管理

#### 创建节点

```http
POST /nodes
```

请求体：

```json
{
    "name": "string",
    "ip_address": "string",
    "port": 655,
    "public_key": "string"
}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "name": "string",
        "ip_address": "string",
        "port": 655,
        "public_key": "string",
        "status": "string",
        "last_seen": "datetime",
        "created_at": "datetime",
        "updated_at": "datetime"
    }
}
```

#### 获取节点信息

```http
GET /nodes/{id}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "name": "string",
        "ip_address": "string",
        "port": 655,
        "public_key": "string",
        "status": "string",
        "last_seen": "datetime",
        "created_at": "datetime",
        "updated_at": "datetime"
    }
}
```

#### 获取节点列表

```http
GET /nodes?page=1&page_size=10
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 10,
        "data": [
            {
                "id": "uuid",
                "name": "string",
                "ip_address": "string",
                "port": 655,
                "public_key": "string",
                "status": "string",
                "last_seen": "datetime",
                "created_at": "datetime",
                "updated_at": "datetime"
            }
        ]
    }
}
```

#### 更新节点

```http
PUT /nodes/{id}
```

请求体：

```json
{
    "name": "string",
    "ip_address": "string",
    "port": 655,
    "public_key": "string"
}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "name": "string",
        "ip_address": "string",
        "port": 655,
        "public_key": "string",
        "status": "string",
        "last_seen": "datetime",
        "created_at": "datetime",
        "updated_at": "datetime"
    }
}
```

#### 删除节点

```http
DELETE /nodes/{id}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

### 连接管理

#### 获取连接信息

```http
GET /connections/{id}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "client_id": "uuid",
        "server_id": "uuid",
        "method_used": "string",
        "latency": 100,
        "status": "string",
        "updated_at": "datetime"
    }
}
```

#### 获取连接列表

```http
GET /connections?page=1&page_size=10
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 10,
        "data": [
            {
                "id": "uuid",
                "client_id": "uuid",
                "server_id": "uuid",
                "method_used": "string",
                "latency": 100,
                "status": "string",
                "updated_at": "datetime"
            }
        ]
    }
}
```

#### 测试连接

```http
POST /connections/{id}/test
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "latency": 100,
        "status": "string"
    }
}
```

#### 终止连接

```http
POST /connections/{id}/terminate
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

### 配置管理

#### 获取配置

```http
GET /configs/{client_id}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "client_id": "uuid",
        "version": 1,
        "hash": "string",
        "created_at": "datetime",
        "content": "string"
    }
}
```

#### 推送配置

```http
POST /configs/{client_id}
```

请求体：

```json
{
    "content": "string"
}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "client_id": "uuid",
        "version": 1,
        "hash": "string",
        "created_at": "datetime",
        "content": "string"
    }
}
```

#### 获取配置历史

```http
GET /configs/{client_id}/history?page=1&page_size=10
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 10,
        "data": [
            {
                "id": "uuid",
                "client_id": "uuid",
                "version": 1,
                "hash": "string",
                "created_at": "datetime",
                "content": "string"
            }
        ]
    }
}
```

#### 回滚配置

```http
POST /configs/{client_id}/rollback
```

请求体：

```json
{
    "version": 1
}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "client_id": "uuid",
        "version": 2,
        "hash": "string",
        "created_at": "datetime",
        "content": "string"
    }
}
```

#### 打包配置

```http
GET /configs/{client_id}/pack
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": [
        // 压缩后的配置数据
    ]
}
```

#### 解包配置

```http
POST /configs/{client_id}/unpack
```

请求体：

```json
{
    "data": [
        // 压缩后的配置数据
    ]
}
```

响应：

```json
{
    "code": 200,
    "message": "success",
    "data": "string"
}
```

## 错误码

- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

## 注意事项

1. 所有时间戳使用 ISO 8601 格式
2. 所有 UUID 使用标准 UUID v4 格式
3. 分页参数 `page` 和 `page_size` 是可选的，默认值分别为 1 和 10
4. 所有 API 请求都需要在头部包含 `Content-Type: application/json`
5. 所有 API 响应都使用 UTF-8 编码 