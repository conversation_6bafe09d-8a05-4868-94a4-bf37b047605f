# 🔧 真实 API 配置指南

## 📋 概述

所有模拟数据已移除，前端现在配置为使用真实的后端 API。以下是配置和测试指南。

## ⚙️ 环境配置

### 1. 开发环境配置

**文件**: `.env.development`

```env
# API 基础 URL - 请根据您的后端服务器地址修改
VITE_API_BASE_URL=http://localhost:8080

# 应用标题
VITE_APP_TITLE=VPN 管理系统

# 是否启用模拟数据 (已禁用)
VITE_USE_MOCK=false
```

### 2. 生产环境配置

**文件**: `.env.production`

```env
# API 基础 URL - 生产环境后端地址
VITE_API_BASE_URL=/api

# 应用标题
VITE_APP_TITLE=VPN 管理系统

# 是否启用模拟数据
VITE_USE_MOCK=false
```

## 🌐 API 接口规范

### 认证接口

#### 1. 用户登录
- **URL**: `POST /api/login`
- **请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```
- **响应**:
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "name": "系统管理员",
      "email": "<EMAIL>",
      "avatar": null,
      "permissions": ["*"]
    }
  },
  "message": "登录成功"
}
```

#### 2. 获取用户信息
- **URL**: `GET /api/profile/me`
- **Headers**: `Authorization: Bearer <token>`
- **响应**:
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "avatar": null,
    "permissions": ["*"]
  }
}
```

#### 3. 退出登录
- **URL**: `POST /api/logout`
- **Headers**: `Authorization: Bearer <token>`

### 客户端管理接口

#### 1. 获取客户端列表
- **URL**: `GET /api/nodes`
- **查询参数**:
  - `page`: 页码
  - `pageSize`: 每页数量
  - `status`: 状态筛选
  - `search`: 搜索关键词
- **响应**:
```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "client-001",
        "ip": "************",
        "port": 655,
        "status": "online",
        "nodeType": "client",
        "lastSeen": "2024-01-01T12:00:00Z",
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

#### 2. 创建客户端
- **URL**: `POST /api/nodes`
- **请求体**:
```json
{
  "name": "client-new",
  "ip": "*************",
  "port": 655,
  "nodeType": "client"
}
```

#### 3. 更新客户端
- **URL**: `PUT /api/nodes/{id}`

#### 4. 删除客户端
- **URL**: `DELETE /api/nodes/{id}`

### 仪表盘接口

#### 1. 获取统计数据
- **URL**: `GET /api/dashboard/stats`
- **响应**:
```json
{
  "code": 0,
  "data": {
    "totalClients": 156,
    "onlineClients": 89,
    "totalServers": 12,
    "onlineServers": 10,
    "activeConnections": 234,
    "totalTraffic": "1.2TB",
    "avgLatency": 45,
    "systemUptime": 2592000
  }
}
```

#### 2. 获取连接趋势
- **URL**: `GET /api/dashboard/trends`
- **查询参数**: `timeRange` (24h/7d/30d)

### 用户管理接口

#### 1. 获取用户列表
- **URL**: `GET /api/users`
- **响应**:
```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin",
        "status": "active",
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10
  }
}
```

## 🔧 后端要求

### 1. CORS 配置
后端需要配置 CORS 以允许前端访问：

```javascript
// Express.js 示例
app.use(cors({
  origin: ['http://localhost:5173', 'http://**************:5173'],
  credentials: true
}))
```

### 2. JWT 认证
- 使用 Bearer Token 认证
- Token 应包含用户信息和权限
- 支持 token 刷新机制

### 3. 统一响应格式
```json
{
  "code": 0,        // 0: 成功, 非0: 错误
  "data": {},       // 响应数据
  "message": ""     // 响应消息
}
```

### 4. 错误处理
- 401: 未认证
- 403: 无权限
- 404: 资源不存在
- 500: 服务器错误

## 🧪 测试步骤

### 1. 配置后端地址
修改 `.env.development` 中的 `VITE_API_BASE_URL` 为您的后端地址。

### 2. 启动前端
```bash
cd vpn-admin-ui
npm run dev
```

### 3. 测试登录
访问 http://**************:5173/login，尝试登录。

### 4. 检查网络请求
打开浏览器开发者工具，查看 Network 标签页，确认：
- API 请求发送到正确的地址
- 请求头包含正确的 Authorization
- 响应格式符合预期

### 5. 错误处理测试
- 测试无效登录凭据
- 测试网络连接失败
- 测试权限不足的操作

## 🔍 调试技巧

### 1. 查看控制台
检查浏览器控制台的错误信息和网络请求。

### 2. 网络面板
在开发者工具的 Network 面板中查看：
- 请求 URL 是否正确
- 请求方法和参数
- 响应状态码和内容

### 3. 后端日志
检查后端服务器的日志，确认：
- 请求是否到达后端
- 认证是否成功
- 数据库查询是否正常

## 📝 注意事项

1. **API 基础 URL**: 确保后端服务器运行在配置的地址上
2. **CORS 配置**: 后端必须允许前端域名的跨域请求
3. **认证机制**: 确保后端支持 JWT Bearer Token 认证
4. **响应格式**: 后端响应必须符合前端期望的格式
5. **错误处理**: 后端应返回标准的 HTTP 状态码

## 🚀 部署建议

### 开发环境
- 前端: http://**************:5173
- 后端: http://localhost:8080 (可修改)

### 生产环境
- 使用反向代理 (Nginx) 统一处理前后端请求
- 配置 HTTPS
- 设置适当的缓存策略

现在前端已完全配置为使用真实 API，请根据您的后端实现调整相应的接口地址和数据格式。
