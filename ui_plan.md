前端功能规划文档（ui_plan.md）

🎯 项目定位

面向 VPN 网络控制与配置的前端管理界面，主要服务对象为系统管理员与运维人员。前端应以「可控、可视、易用、安全」为核心设计目标，结合现代化界面构建技术（Vue 3 + Vite + Naive UI + TailwindCSS）。

⸻

🧩 功能模块概览

1. 登录与权限系统
	•	登录页（支持账号+密码登录）
	•	JWT 本地缓存 + 登录状态管理
	•	登录后根据角色渲染对应菜单与权限视图
	•	用户信息展示与退出登录功能

⸻

2. 仪表盘（Dashboard）
	•	在线客户端数量统计
	•	当前活跃连接数、打洞成功率图表
	•	NAT 类型分布图
	•	异常连接 / 最近失败事件警告
	•	每日新增节点趋势图（折线图）

⸻

3. 客户端管理（Clients）
	•	客户端列表页：支持查询、分页、排序
	•	显示字段：ID、名称、MAC、IP、状态、最后在线时间、NAT 类型、使用方式
	•	客户端详情页：展示打洞历史、配置版本、在线终端、连接日志
	•	新增/编辑客户端（输入配置字段、指定服务端、推送配置）
	•	一键重推配置 / 强制断线 / 测试连通性

⸻

4. 服务端管理（Servers）
	•	服务端节点列表：IP、端口、隧道数、负载状态
	•	服务端详情：连接数变化趋势、CPU/内存/网卡监控图
	•	添加/编辑/删除服务端节点

⸻

5. 用户与角色管理（RBAC）
	•	用户列表、搜索、编辑、重置密码
	•	分配角色与权限（页面访问、数据操作）
	•	角色列表：每个角色包含的权限项及说明

⸻

6. 配置管理
	•	配置列表：显示客户端对应配置包信息、当前版本号、变更记录
	•	下载配置包（tar.gz）
	•	配置差异比对视图（对比上一个版本）
	•	历史版本回滚

⸻

7. 连接状态监控（实时）
	•	当前连接会话表：客户端 ↔ 服务端，状态、打洞方式、延迟、带宽
	•	会话详情页：通道信息、握手日志、是否重连、上行/下行速率曲线图
	•	会话断开按钮（管理员）

⸻

8. NAT 类型与打洞策略可视化
	•	每个客户端 NAT 类型检测结果展示（颜色标记）
	•	自动尝试打洞过程的步骤流程图（失败链路标识）
	•	手动重新检测按钮 + 打洞策略调优下拉框

⸻

9. 事件与日志中心
	•	系统事件流：上线、下线、打洞失败、配置推送等
	•	日志搜索：按时间范围、关键词过滤
	•	导出 CSV/JSON 按钮

⸻

10. 设置与系统管理
	•	站点设置：系统名、Logo、语言、登录超时等
	•	客户端默认策略配置（NAT 探测频率、策略优先级）
	•	Prometheus 接入信息显示
	•	后台版本信息显示（Git 版本 / 编译时间）

⸻

💡 UI 风格建议
	•	使用 Naive UI 为主框架（轻量、现代）
	•	配合 Tailwind CSS 实现响应式布局
	•	深色模式 + 亮色模式支持
	•	使用 Iconify 图标库（分类清晰，轻量）
	•	页面间保持统一布局：左侧菜单栏 + 顶部导航 + 主内容区
	•	表格建议支持列筛选、固定列、导出
	•	图表建议使用 ECharts（如需复杂交互）或 Naive UI 原生图表组件

⸻

🗂 路由结构建议（Vue Router）

/
├── /login
├── /dashboard
├── /clients
│   └── /clients/:id
├── /servers
│   └── /servers/:id
├── /users
├── /roles
├── /configs
├── /connections
├── /logs
├── /settings


⸻

✅ 输出要求
	•	所有页面组件以组合式 API 开发
	•	按功能模块独立目录存放（views / components / api / utils / composables）
	•	统一 API 封装（Axios）+ 错误提示处理
	•	所有页面支持权限控制（按钮与路由）
	•	与后端接口对齐（参考 /api/ 路由结构）

