.PHONY: build test clean run migrate docker-build docker-run

# 变量定义
CARGO = cargo
RUSTC = rustc
CARGO_TEST = cargo test
CARGO_BUILD = cargo build
CARGO_RUN = cargo run
CARGO_CLEAN = cargo clean
CARGO_DOC = cargo doc

# 默认目标
all: build

# 构建项目
build:
	$(CARGO_BUILD) --release

# 运行测试
test:
	$(CARGO_TEST)

# 清理构建文件
clean:
	$(CARGO_CLEAN)

# 运行项目
run:
	$(CARGO_RUN)

# 运行数据库迁移
migrate:
	$(CARGO_RUN) -- migrate

# 构建 Docker 镜像
docker-build:
	docker build -t tinc-rust .

# 运行 Docker 容器
docker-run:
	docker run -p 3000:3000 tinc-rust

# 安装依赖
deps:
	$(CARGO) update

# 检查代码
check:
	$(CARGO) check
	$(CARGO) clippy
	$(CARGO) fmt -- --check

# 生成文档
doc:
	$(CARGO_DOC) --no-deps

# 安装
install: build
	./install.sh

# 卸载
uninstall:
	systemctl stop tinc-rust
	systemctl disable tinc-rust
	rm -f /etc/systemd/system/tinc-rust.service
	systemctl daemon-reload
	rm -rf /opt/tinc-rust
	rm -rf /var/lib/tinc-rust

# 帮助
help:
	@echo "可用的命令："
	@echo "  make build        - 构建项目"
	@echo "  make test         - 运行测试"
	@echo "  make clean        - 清理构建文件"
	@echo "  make run          - 运行项目"
	@echo "  make migrate      - 运行数据库迁移"
	@echo "  make docker-build - 构建 Docker 镜像"
	@echo "  make docker-run   - 运行 Docker 容器"
	@echo "  make deps         - 安装依赖"
	@echo "  make check        - 检查代码"
	@echo "  make doc          - 生成文档"
	@echo "  make install      - 安装服务"
	@echo "  make uninstall    - 卸载服务" 