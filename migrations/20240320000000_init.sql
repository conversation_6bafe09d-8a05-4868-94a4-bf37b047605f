-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id CHAR(36) PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- 创建节点表
CREATE TABLE IF NOT EXISTS nodes (
    id CHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address VARCHAR(45) NOT NULL,
    port INT NOT NULL DEFAULT 655,
    node_type VARCHAR(50) NOT NULL DEFAULT 'client', -- 'client' or 'server'
    ip VARCHAR(45) NOT NULL,
    mac VARCHAR(17) NOT NULL,
    public_key TEXT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'offline',
    tunnel_type VARCHAR(50) NOT NULL DEFAULT 'tcp',
    nat_type VARCHAR(50) NULL,
    last_active TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建连接表
CREATE TABLE IF NOT EXISTS connections (
    id CHAR(36) PRIMARY KEY,
    client_id CHAR(36) NOT NULL,
    server_id CHAR(36) NOT NULL,
    method_used VARCHAR(50) NOT NULL, -- 'udp', 'tcp', 'relay'
    latency INT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'connecting',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES nodes(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES nodes(id) ON DELETE CASCADE
);

-- 创建配置表
CREATE TABLE IF NOT EXISTS configs (
    id CHAR(36) PRIMARY KEY,
    client_id CHAR(36) NOT NULL,
    version INT NOT NULL,
    hash VARCHAR(64) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES nodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_client_version (client_id, version)
);

-- 创建策略表
CREATE TABLE IF NOT EXISTS policies (
    id CHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    bandwidth_limit INT NULL,
    allow_ips TEXT NOT NULL,
    allow_ports TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建日志表
CREATE TABLE IF NOT EXISTS logs (
    id CHAR(36) PRIMARY KEY,
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    source VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_nodes_name ON nodes(name);
CREATE INDEX idx_nodes_ip ON nodes(ip);
CREATE INDEX idx_nodes_type ON nodes(node_type);
CREATE INDEX idx_connections_client_id ON connections(client_id);
CREATE INDEX idx_connections_server_id ON connections(server_id);
CREATE INDEX idx_connections_status ON connections(status);
CREATE INDEX idx_configs_client_id ON configs(client_id);
CREATE INDEX idx_configs_version ON configs(version);
CREATE INDEX idx_policies_name ON policies(name);
CREATE INDEX idx_logs_type ON logs(log_type);
CREATE INDEX idx_logs_source ON logs(source);
CREATE INDEX idx_logs_created_at ON logs(created_at);