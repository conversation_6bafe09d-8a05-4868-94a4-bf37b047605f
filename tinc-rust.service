[Unit]
Description=Tinc Rust VPN Service
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=simple
User=tinc
Group=tinc
WorkingDirectory=/opt/tinc-rust
ExecStart=/opt/tinc-rust/tinc_rust
Restart=always
RestartSec=10
Environment=RUST_LOG=info
Environment=DATABASE_URL=postgres://postgres:postgres@localhost/tinc
Environment=JWT_SECRET=your-secret-key
Environment=JWT_EXPIRATION=86400
Environment=SERVER_HOST=0.0.0.0
Environment=SERVER_PORT=3000

# 安全设置
NoNewPrivileges=yes
ProtectSystem=full
ProtectHome=yes
PrivateTmp=yes
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_RAW

[Install]
WantedBy=multi-user.target 