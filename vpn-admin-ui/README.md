# VPN 管理系统前端

基于 Vue 3 + Vite + Naive UI + TailwindCSS 构建的现代化 VPN 管理系统前端。

## 🚀 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI 库**: Naive UI
- **样式**: TailwindCSS
- **图标**: Iconify
- **图表**: ECharts
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP 客户端**: Axios
- **工具库**: VueUse, Day.js

## 📁 项目结构

```
vpn-admin-ui/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口封装
│   │   ├── request.js     # Axios 配置
│   │   ├── auth.js        # 认证接口
│   │   ├── dashboard.js   # 仪表盘接口
│   │   └── clients.js     # 客户端接口
│   ├── components/        # 通用组件
│   │   ├── StatCard.vue   # 统计卡片
│   │   └── charts/        # 图表组件
│   ├── layout/            # 布局组件
│   │   ├── MainLayout.vue # 主布局
│   │   └── components/    # 布局子组件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   │   ├── auth.js        # 认证状态
│   │   └── theme.js       # 主题状态
│   ├── style/             # 样式文件
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证页面
│   │   ├── dashboard/     # 仪表盘
│   │   ├── clients/       # 客户端管理
│   │   ├── servers/       # 服务端管理
│   │   ├── users/         # 用户管理
│   │   ├── roles/         # 角色管理
│   │   ├── configs/       # 配置管理
│   │   ├── connections/   # 连接监控
│   │   ├── nat/           # NAT 管理
│   │   ├── logs/          # 日志中心
│   │   ├── settings/      # 系统设置
│   │   └── error/         # 错误页面
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .env                   # 环境变量
├── index.html             # HTML 模板
├── package.json           # 依赖配置
├── tailwind.config.js     # TailwindCSS 配置
├── vite.config.js         # Vite 配置
└── README.md              # 项目说明
```

## 🛠️ 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8 或 yarn >= 1.22

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发运行

```bash
npm run dev
# 或
yarn dev
```

访问 http://localhost:5173

### 构建部署

```bash
npm run build
# 或
yarn build
```

### 代码检查

```bash
npm run lint
# 或
yarn lint
```

## 🔧 配置说明

### 环境变量

在 `.env` 文件中配置：

```env
# API 配置
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=10000

# 应用配置
VITE_APP_TITLE=VPN 管理系统
VITE_APP_VERSION=1.0.0
```

### 后端接口对接

项目已配置代理，开发环境下 `/api` 请求会自动转发到后端服务器。

## 📋 功能模块

### ✅ 已实现

- [x] 登录认证系统
- [x] 主布局框架
- [x] 侧边栏导航
- [x] 主题切换（亮色/深色）
- [x] 仪表盘页面
- [x] 客户端管理（列表、新增、编辑、删除）
- [x] 权限控制
- [x] 错误页面（403、404）

### 🚧 开发中

- [ ] 客户端详情页
- [ ] 服务端管理
- [ ] 用户管理
- [ ] 角色管理
- [ ] 配置管理
- [ ] 连接监控
- [ ] NAT 管理
- [ ] 日志中心
- [ ] 系统设置

## 🎨 设计规范

### 颜色主题

- 主色调：蓝色 (#3b82f6)
- 辅助色：紫色 (#8b5cf6)
- 成功色：绿色 (#10b981)
- 警告色：黄色 (#f59e0b)
- 错误色：红色 (#ef4444)

### 布局规范

- 页面整体居中布局
- 左侧菜单栏固定宽度 240px
- 内容区域最大宽度 1440px
- 响应式设计，支持桌面和平板

### 组件规范

- 统一使用 Naive UI 组件
- 自定义组件使用 Composition API
- 样式使用 TailwindCSS 原子类
- 图标使用 Iconify

## 🔐 权限系统

### 角色权限

- **admin**: 管理员，拥有所有权限
- **operator**: 操作员，拥有基本操作权限
- **viewer**: 查看者，只有查看权限

### 权限控制

- 路由级权限控制
- 页面元素级权限控制
- API 接口权限验证

## 📱 响应式设计

- 桌面端：>= 1280px
- 平板端：768px - 1279px
- 移动端：< 768px

## 🚀 部署说明

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend-server:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📞 技术支持

如有问题，请联系开发团队或提交 Issue。

## 📄 许可证

MIT License
