#!/bin/bash

echo "🚀 启动 VPN 管理系统前端项目"
echo "================================"

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，当前版本: $(node -v)，需要 16+"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装项目依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已存在"
fi

# 启动开发服务器
echo "🌟 启动开发服务器..."
echo "📡 访问地址: http://localhost:5173"
echo "🔑 默认账号: admin / admin123"
echo "================================"

npm run dev
