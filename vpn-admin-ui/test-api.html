<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试</title>
</head>
<body>
    <h1>API 连接测试</h1>
    <div id="result"></div>
    
    <script>
        const API_BASE_URL = 'http://**************:3000';
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('测试 API 连接...');
                
                // 测试获取子网列表
                const response = await fetch(`${API_BASE_URL}/api/subnets`);
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('响应数据:', data);
                
                resultDiv.innerHTML = `
                    <h2>✅ API 连接成功</h2>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>消息:</strong> ${data.message}</p>
                    <p><strong>数据数量:</strong> ${data.data?.length || 0}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // 测试创建子网
                console.log('测试创建子网...');
                const createResponse = await fetch(`${API_BASE_URL}/api/subnets`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'test-from-html',
                        cidr: '***********/24',
                        port: 10099,
                        mtu: 1500,
                        identifier: 'test-html-001',
                        description: '从 HTML 页面创建的测试子网',
                        status: 'active'
                    })
                });
                
                console.log('创建响应状态:', createResponse.status);
                
                if (createResponse.ok) {
                    const createData = await createResponse.json();
                    console.log('创建响应数据:', createData);
                    
                    resultDiv.innerHTML += `
                        <h2>✅ 创建子网成功</h2>
                        <p><strong>新子网ID:</strong> ${createData.data?.id}</p>
                        <p><strong>名称:</strong> ${createData.data?.name}</p>
                    `;
                } else {
                    const errorData = await createResponse.text();
                    console.error('创建失败:', errorData);
                    resultDiv.innerHTML += `
                        <h2>❌ 创建子网失败</h2>
                        <p><strong>状态码:</strong> ${createResponse.status}</p>
                        <p><strong>错误:</strong> ${errorData}</p>
                    `;
                }
                
            } catch (error) {
                console.error('API 测试失败:', error);
                resultDiv.innerHTML = `
                    <h2>❌ API 连接失败</h2>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p>请检查:</p>
                    <ul>
                        <li>后端服务是否启动 (http://**************:3000)</li>
                        <li>CORS 配置是否正确</li>
                        <li>网络连接是否正常</li>
                    </ul>
                `;
            }
        }
        
        // 页面加载后自动测试
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
