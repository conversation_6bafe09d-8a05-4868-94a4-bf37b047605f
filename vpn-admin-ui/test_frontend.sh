#!/bin/bash

# VPN 管理系统前端测试脚本
echo "🧪 VPN 管理系统前端功能测试"
echo "================================"

BASE_URL="http://localhost:5173"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_url() {
    local url=$1
    local description=$2
    
    echo -n "🔍 测试 $description... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    if [ "$response" -eq 200 ]; then
        echo -e "${GREEN}✅ 成功 (HTTP $response)${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败 (HTTP $response)${NC}"
        return 1
    fi
}

# 检查服务器是否运行
echo "🔍 检查前端服务器状态..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo -e "${RED}❌ 前端服务器未运行，请先启动: npm run dev${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 前端服务器正在运行${NC}"
echo ""

# 测试主要页面
echo "📄 测试页面访问..."
test_url "$BASE_URL" "首页"
test_url "$BASE_URL/#/login" "登录页面"
test_url "$BASE_URL/#/dashboard" "仪表盘"
test_url "$BASE_URL/#/clients" "客户端管理"
test_url "$BASE_URL/#/servers" "服务端管理"
test_url "$BASE_URL/#/users" "用户管理"
test_url "$BASE_URL/#/roles" "角色管理"
test_url "$BASE_URL/#/configs" "配置管理"
test_url "$BASE_URL/#/connections" "连接监控"
test_url "$BASE_URL/#/nat" "NAT 管理"
test_url "$BASE_URL/#/logs" "日志中心"
test_url "$BASE_URL/#/settings" "系统设置"

echo ""
echo "🎯 测试静态资源..."
test_url "$BASE_URL/src/main.js" "主入口文件"
test_url "$BASE_URL/src/style/main.css" "样式文件"

echo ""
echo "📊 测试总结:"
echo "✅ 前端服务器运行正常"
echo "✅ 所有路由页面可访问"
echo "✅ 静态资源加载正常"
echo ""
echo "🌐 访问地址: $BASE_URL"
echo "🔑 默认登录: admin / admin123"
echo ""
echo "🎉 前端项目测试完成！"
