import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { removeToken, setToken, getToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(getToken())
  const userInfo = ref(null)
  const permissions = ref([])
  const roles = ref([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.username || '')
  const userRole = computed(() => userInfo.value?.role || '')

  // 登录
  const login = async (credentials) => {
    try {
      const response = await authApi.login(credentials)
      const { token: authToken, user } = response.data
      
      token.value = authToken
      userInfo.value = user
      roles.value = [user.role]
      
      setToken(authToken)
      
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败' 
      }
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await authApi.getUserInfo()
      userInfo.value = response.data
      roles.value = [response.data.role]
      permissions.value = response.data.permissions || []
      return response.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
      throw error
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    userInfo.value = null
    permissions.value = []
    roles.value = []
    removeToken()
  }

  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (roleList) => {
    if (!Array.isArray(roleList)) {
      roleList = [roleList]
    }
    return roleList.some(role => roles.value.includes(role))
  }

  // 初始化
  const init = async () => {
    if (token.value) {
      try {
        await getUserInfo()
      } catch (error) {
        logout()
      }
    }
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,
    roles,
    
    // 计算属性
    isLoggedIn,
    userName,
    userRole,
    
    // 方法
    login,
    logout,
    getUserInfo,
    hasPermission,
    hasRole,
    init
  }
})
