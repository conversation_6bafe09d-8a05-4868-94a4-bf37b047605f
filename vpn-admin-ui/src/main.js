import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 样式导入
import './style/main.css'

// Naive UI
import naive, { createDiscreteApi } from 'naive-ui'

// 设置全局消息实例
import { setMessageInstance } from './api/request'
import naiveUIFix from './plugins/naive-ui-fix'
import { subnetsApi } from './api/subnets'

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(naive)
app.use(naiveUIFix)

// 创建独立的消息 API 实例
const { message } = createDiscreteApi(['message'])
setMessageInstance(message)

// 挂载应用
app.mount('#app')

// 应用挂载后初始化 authStore
import { useAuthStore } from './store/auth'
const authStore = useAuthStore()
authStore.init()

// 添加全局测试函数
window.testSubnetsApi = async () => {
  try {
    console.log('开始测试子网 API...')
    console.log('API 基础地址:', import.meta.env.VITE_API_BASE_URL)

    const response = await subnetsApi.getSubnets()
    console.log('API 调用成功:', response)
    return response
  } catch (error) {
    console.error('API 调用失败:', error)
    throw error
  }
}

window.createTestSubnet = async () => {
  try {
    console.log('开始创建测试子网...')
    const testData = {
      name: 'window-test-subnet',
      cidr: '***********/24',
      port: 10077,
      mtu: 1500,
      identifier: 'window-test-subnet-001',
      description: '从 window 函数创建的测试子网',
      status: 'active'
    }

    const response = await subnetsApi.createSubnet(testData)
    console.log('创建子网成功:', response)
    return response
  } catch (error) {
    console.error('创建子网失败:', error)
    throw error
  }
}
