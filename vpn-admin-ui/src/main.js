import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 样式导入
import './style/main.css'

// Naive UI
import naive, { createDiscreteApi } from 'naive-ui'

// 设置全局消息实例
import { setMessageInstance } from './api/request'
import naiveUIFix from './plugins/naive-ui-fix'

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(naive)
app.use(naiveUIFix)

// 创建独立的消息 API 实例
const { message } = createDiscreteApi(['message'])
setMessageInstance(message)

// 挂载应用
app.mount('#app')

// 应用挂载后初始化 authStore
import { useAuthStore } from './store/auth'
const authStore = useAuthStore()
authStore.init()
