import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 格式化时间
export function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return '-'
  return dayjs(time).format(format)
}

// 相对时间
export function formatRelativeTime(time) {
  if (!time) return '-'
  return dayjs(time).fromNow()
}

// 格式化文件大小
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化带宽
export function formatBandwidth(bps) {
  if (!bps || bps === 0) return '0 bps'
  
  const k = 1000
  const sizes = ['bps', 'Kbps', 'Mbps', 'Gbps']
  const i = Math.floor(Math.log(bps) / Math.log(k))
  
  return parseFloat((bps / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化延迟
export function formatLatency(ms) {
  if (ms === null || ms === undefined) return '-'
  if (ms < 1) return '<1ms'
  return `${Math.round(ms)}ms`
}

// 格式化百分比
export function formatPercent(value, total) {
  if (!total || total === 0) return '0%'
  return `${Math.round((value / total) * 100)}%`
}

// 格式化状态
export function formatStatus(status) {
  const statusMap = {
    online: '在线',
    offline: '离线',
    connecting: '连接中',
    connected: '已连接',
    disconnected: '已断开',
    error: '错误',
    pending: '等待中'
  }
  return statusMap[status] || status
}

// 格式化 NAT 类型
export function formatNatType(type) {
  const natMap = {
    open: '开放网络',
    full_cone: '完全锥形',
    restricted_cone: '受限锥形',
    port_restricted_cone: '端口受限锥形',
    symmetric: '对称型',
    unknown: '未知'
  }
  return natMap[type] || type
}
