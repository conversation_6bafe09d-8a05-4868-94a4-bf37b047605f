<template>
  <div class="card hover:shadow-lg transition-shadow duration-200">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ title }}</p>
        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">{{ value }}</p>
        <div v-if="trend !== undefined" class="flex items-center mt-2">
          <Icon 
            :icon="trend >= 0 ? 'mdi:trending-up' : 'mdi:trending-down'" 
            :class="trend >= 0 ? 'text-green-500' : 'text-red-500'"
            class="text-sm mr-1"
          />
          <span 
            :class="trend >= 0 ? 'text-green-600' : 'text-red-600'"
            class="text-xs font-medium"
          >
            {{ Math.abs(trend) }}%
          </span>
          <span class="text-xs text-gray-500 ml-1">vs 上周</span>
        </div>
      </div>
      
      <div :class="iconBgClass" class="w-12 h-12 rounded-lg flex items-center justify-center">
        <Icon :icon="icon" class="text-xl text-white" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'blue'
  },
  trend: {
    type: Number,
    default: undefined
  }
})

const iconBgClass = computed(() => {
  const colorMap = {
    blue: 'bg-gradient-to-r from-blue-500 to-blue-600',
    green: 'bg-gradient-to-r from-green-500 to-green-600',
    purple: 'bg-gradient-to-r from-purple-500 to-purple-600',
    red: 'bg-gradient-to-r from-red-500 to-red-600',
    yellow: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
    indigo: 'bg-gradient-to-r from-indigo-500 to-indigo-600'
  }
  return colorMap[props.color] || colorMap.blue
})
</script>
