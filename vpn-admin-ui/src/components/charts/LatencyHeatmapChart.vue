<template>
  <div ref="chartRef" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const hours = ['00', '04', '08', '12', '16', '20']
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  
  const option = {
    tooltip: {
      position: 'top',
      formatter: function (params) {
        return `${params.data[3]} ${params.data[4]}<br/>延迟: ${params.data[2].toFixed(1)}ms`
      }
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      },
      axisLabel: {
        fontSize: 12
      }
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      },
      axisLabel: {
        fontSize: 12
      }
    },
    visualMap: {
      min: 0,
      max: 120,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%',
      inRange: {
        color: ['#50f', '#06f', '#0df', '#6f0', '#ff0', '#f60', '#f00']
      },
      text: ['高延迟', '低延迟'],
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '延迟',
        type: 'heatmap',
        data: props.data.map(item => [item.hour, item.day, item.value, item.dayName, item.hourName]),
        label: {
          show: true,
          formatter: function (params) {
            return params.data[2].toFixed(0) + 'ms'
          },
          fontSize: 10
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const updateChart = () => {
  if (!chartInstance) return
  
  const option = {
    series: [
      {
        data: props.data.map(item => [item.hour, item.day, item.value, item.dayName, item.hourName])
      }
    ]
  }
  
  chartInstance.setOption(option)
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  // 响应式处理
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})
</script>
