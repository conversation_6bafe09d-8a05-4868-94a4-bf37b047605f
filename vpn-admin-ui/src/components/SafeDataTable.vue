<template>
  <n-data-table
    v-bind="$attrs"
    :data="safeData"
    :columns="safeColumns"
    @update:page="handlePageUpdate"
    @update:page-size="handlePageSizeUpdate"
    @update:checked-row-keys="handleCheckedRowKeysUpdate"
  >
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </n-data-table>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'update:page',
  'update:page-size', 
  'update:checked-row-keys'
])

// 确保 data 始终是一个有效的数组
const safeData = computed(() => {
  console.log('SafeDataTable: processing data:', props.data)
  
  if (!props.data) {
    console.warn('SafeDataTable: data is null or undefined, using empty array')
    return []
  }
  
  if (!Array.isArray(props.data)) {
    console.warn('SafeDataTable: data is not an array, type:', typeof props.data, 'value:', props.data)
    return []
  }
  
  // 验证每个数据项
  const validData = props.data.filter((item, index) => {
    if (!item || typeof item !== 'object') {
      console.warn(`SafeDataTable: invalid data item at index ${index}:`, item)
      return false
    }
    return true
  })
  
  if (validData.length !== props.data.length) {
    console.warn(`SafeDataTable: filtered ${props.data.length - validData.length} invalid data items`)
  }
  
  console.log('SafeDataTable: valid data count:', validData.length)
  return validData
})

// 确保 columns 始终是一个有效的数组
const safeColumns = computed(() => {
  console.log('SafeDataTable: processing columns:', props.columns)
  
  if (!props.columns) {
    console.warn('SafeDataTable: columns is null or undefined, using empty array')
    return []
  }
  
  if (!Array.isArray(props.columns)) {
    console.warn('SafeDataTable: columns is not an array, type:', typeof props.columns, 'value:', props.columns)
    return []
  }
  
  // 验证每个列配置
  const validColumns = props.columns.filter((column, index) => {
    if (!column || typeof column !== 'object') {
      console.warn(`SafeDataTable: invalid column at index ${index}:`, column)
      return false
    }
    
    // 检查必需的字段
    if (!column.key && !column.type) {
      console.warn(`SafeDataTable: column missing key and type at index ${index}:`, column)
      return false
    }
    
    return true
  })
  
  if (validColumns.length !== props.columns.length) {
    console.warn(`SafeDataTable: filtered ${props.columns.length - validColumns.length} invalid columns`)
  }
  
  console.log('SafeDataTable: valid columns count:', validColumns.length)
  return validColumns
})

// 事件处理
const handlePageUpdate = (page) => {
  emit('update:page', page)
}

const handlePageSizeUpdate = (pageSize) => {
  emit('update:page-size', pageSize)
}

const handleCheckedRowKeysUpdate = (checkedRowKeys) => {
  emit('update:checked-row-keys', checkedRowKeys)
}
</script>
