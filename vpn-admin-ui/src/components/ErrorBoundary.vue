<template>
  <div v-if="hasError" class="min-h-screen flex items-center justify-center bg-gray-100">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
      <div class="text-center">
        <Icon icon="mdi:alert-circle" class="text-6xl text-red-500 mb-4" />
        <h1 class="text-2xl font-bold text-gray-900 mb-2">出现错误</h1>
        <p class="text-gray-600 mb-4">应用遇到了一个错误，请刷新页面重试。</p>
        
        <div class="space-y-3">
          <n-button type="primary" @click="reload" block>
            刷新页面
          </n-button>
          <n-button @click="showDetails = !showDetails" block>
            {{ showDetails ? '隐藏' : '显示' }}错误详情
          </n-button>
        </div>
        
        <div v-if="showDetails" class="mt-4 p-3 bg-gray-100 rounded text-left">
          <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ errorInfo }}</pre>
        </div>
      </div>
    </div>
  </div>
  
  <div v-else>
    <slot />
  </div>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'
import { Icon } from '@iconify/vue'

const hasError = ref(false)
const showDetails = ref(false)
const errorInfo = ref('')

onErrorCaptured((error, instance, info) => {
  console.error('ErrorBoundary 捕获到错误:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)
  
  hasError.value = true
  errorInfo.value = `错误: ${error.message}\n\n堆栈: ${error.stack}\n\n组件信息: ${info}`
  
  // 返回 false 阻止错误继续传播
  return false
})

const reload = () => {
  window.location.reload()
}
</script>
