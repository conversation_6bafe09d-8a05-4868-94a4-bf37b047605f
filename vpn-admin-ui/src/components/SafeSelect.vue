<template>
  <n-select
    v-bind="$attrs"
    :options="safeOptions"
    @update:value="handleUpdate"
  >
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </n-select>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  options: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:value'])

// 确保 options 始终是一个有效的数组
const safeOptions = computed(() => {
  console.log('SafeSelect: processing options:', props.options)

  if (!props.options) {
    console.warn('SafeSelect: options is null or undefined, using empty array')
    return []
  }

  if (!Array.isArray(props.options)) {
    console.warn('SafeSelect: options is not an array, converting to array. Type:', typeof props.options, 'Value:', props.options)
    return []
  }

  // 验证每个选项的结构
  const validOptions = props.options.filter(option => {
    if (!option || typeof option !== 'object') {
      console.warn('SafeSelect: invalid option found:', option)
      return false
    }

    if (!('label' in option) || !('value' in option)) {
      console.warn('SafeSelect: option missing label or value:', option)
      return false
    }

    return true
  })

  console.log('SafeSelect: valid options count:', validOptions.length)
  return validOptions
})

const handleUpdate = (value) => {
  emit('update:value', value)
}
</script>
