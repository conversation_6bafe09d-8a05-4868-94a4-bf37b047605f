// Naive UI 组件修复插件
// 用于解决 Tree 组件相关的错误

import { h, computed } from 'vue'

// 安全的选项验证函数
export function validateOptions(options, componentName = 'Component') {
  if (!options) {
    console.warn(`${componentName}: options is null or undefined, using empty array`)
    return []
  }
  
  if (!Array.isArray(options)) {
    console.warn(`${componentName}: options is not an array, type: ${typeof options}, value:`, options)
    return []
  }
  
  // 验证每个选项的结构
  const validOptions = options.filter((option, index) => {
    if (!option || typeof option !== 'object') {
      console.warn(`${componentName}: invalid option at index ${index}:`, option)
      return false
    }
    
    // 对于选择器组件，检查必需的字段
    if (!('label' in option) || !('value' in option)) {
      console.warn(`${componentName}: option missing label or value at index ${index}:`, option)
      return false
    }
    
    return true
  })
  
  if (validOptions.length !== options.length) {
    console.warn(`${componentName}: filtered ${options.length - validOptions.length} invalid options`)
  }
  
  return validOptions
}

// 创建安全的 n-select 组件
export function createSafeSelect() {
  return {
    name: 'SafeNSelect',
    props: {
      options: {
        type: Array,
        default: () => []
      }
    },
    setup(props, { attrs, slots, emit }) {
      const safeOptions = computed(() => {
        return validateOptions(props.options, 'SafeNSelect')
      })
      
      return () => {
        return h('n-select', {
          ...attrs,
          options: safeOptions.value,
          'onUpdate:value': (value) => emit('update:value', value)
        }, slots)
      }
    }
  }
}

// 创建安全的 n-cascader 组件
export function createSafeCascader() {
  return {
    name: 'SafeNCascader',
    props: {
      options: {
        type: Array,
        default: () => []
      }
    },
    setup(props, { attrs, slots, emit }) {
      const safeOptions = computed(() => {
        return validateOptions(props.options, 'SafeNCascader')
      })
      
      return () => {
        return h('n-cascader', {
          ...attrs,
          options: safeOptions.value,
          'onUpdate:value': (value) => emit('update:value', value)
        }, slots)
      }
    }
  }
}

// 创建安全的 n-tree-select 组件
export function createSafeTreeSelect() {
  return {
    name: 'SafeNTreeSelect',
    props: {
      options: {
        type: Array,
        default: () => []
      }
    },
    setup(props, { attrs, slots, emit }) {
      const safeOptions = computed(() => {
        return validateOptions(props.options, 'SafeNTreeSelect')
      })
      
      return () => {
        return h('n-tree-select', {
          ...attrs,
          options: safeOptions.value,
          'onUpdate:value': (value) => emit('update:value', value)
        }, slots)
      }
    }
  }
}

// 全局错误处理
export function setupGlobalErrorHandling() {
  // 捕获未处理的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // 如果是 Tree 相关错误，阻止默认处理
    if (event.reason && event.reason.message && 
        event.reason.message.includes('rawNodes.forEach is not a function')) {
      console.warn('Prevented Tree component error from crashing the app')
      event.preventDefault()
    }
  })
  
  // 捕获全局错误
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    // 如果是 Tree 相关错误，阻止默认处理
    if (event.error && event.error.message && 
        event.error.message.includes('rawNodes.forEach is not a function')) {
      console.warn('Prevented Tree component error from crashing the app')
      event.preventDefault()
    }
  })
}

// 插件安装函数
export default {
  install(app) {
    // 注册安全组件
    app.component('SafeNSelect', createSafeSelect())
    app.component('SafeNCascader', createSafeCascader())
    app.component('SafeNTreeSelect', createSafeTreeSelect())
    
    // 设置全局错误处理
    setupGlobalErrorHandling()
    
    console.log('Naive UI Fix plugin installed')
  }
}
