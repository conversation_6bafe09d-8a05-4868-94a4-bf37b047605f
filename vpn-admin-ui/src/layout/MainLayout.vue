<template>
  <div class="page-container">
    <div class="layout-wrapper">
      <!-- 侧边栏 - 临时禁用 -->
      <div class="sidebar">
        <!-- <Sidebar /> -->
        <div class="h-full bg-white dark:bg-gray-800 shadow-lg rounded-xl p-4">
          <div class="text-center text-gray-500">侧边栏已禁用</div>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 顶部导航 -->
        <Header />
        
        <!-- 页面内容 -->
        <main class="mt-6">
          <router-view />
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import Sidebar from './components/Sidebar.vue'
import Header from './components/Header.vue'
import { useAuthStore } from '@/store/auth'
import { useThemeStore } from '@/store/theme'

const authStore = useAuthStore()
const themeStore = useThemeStore()

onMounted(async () => {
  // 初始化主题
  themeStore.initTheme()
  
  // 初始化用户信息
  if (authStore.isLoggedIn) {
    try {
      await authStore.init()
    } catch (error) {
      console.error('初始化用户信息失败:', error)
    }
  }
})
</script>
