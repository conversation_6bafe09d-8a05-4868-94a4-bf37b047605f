<template>
  <header class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4">
    <div class="flex items-center justify-between">
      <!-- 左侧：面包屑导航 -->
      <div class="flex items-center space-x-4">
        <n-breadcrumb>
          <n-breadcrumb-item v-for="item in breadcrumbs" :key="item.name">
            <router-link v-if="item.path" :to="item.path" class="hover:text-blue-600">
              {{ item.title }}
            </router-link>
            <span v-else>{{ item.title }}</span>
          </n-breadcrumb-item>
        </n-breadcrumb>
      </div>
      
      <!-- 右侧：用户操作区 -->
      <div class="flex items-center space-x-4">
        <!-- 主题切换 -->
        <n-button quaternary circle @click="toggleTheme">
          <Icon :icon="isDark ? 'mdi:weather-sunny' : 'mdi:weather-night'" class="text-lg" />
        </n-button>
        
        <!-- 通知 -->
        <n-badge :value="notificationCount" :max="99">
          <n-button quaternary circle>
            <Icon icon="mdi:bell" class="text-lg" />
          </n-button>
        </n-badge>
        
        <!-- 用户菜单 -->
        <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
          <div class="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg p-2">
            <n-avatar round size="small">
              <Icon icon="mdi:account" />
            </n-avatar>
            <span class="text-sm font-medium">{{ userName }}</span>
            <Icon icon="mdi:chevron-down" class="text-sm" />
          </div>
        </n-dropdown>
      </div>
    </div>
  </header>
</template>

<script setup>
import { computed, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useThemeStore } from '@/store/theme'
import { useAuthStore } from '@/store/auth'
import { useDialog, useMessage } from 'naive-ui'

const route = useRoute()
const router = useRouter()
const themeStore = useThemeStore()
const authStore = useAuthStore()
const dialog = useDialog()
const message = useMessage()

const isDark = computed(() => themeStore.isDark)
const userName = computed(() => authStore.userName || '管理员')
const notificationCount = computed(() => 0) // TODO: 实现通知功能

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const breadcrumbItems = []
  
  matched.forEach((item, index) => {
    if (index === 0) return // 跳过根路由
    
    breadcrumbItems.push({
      name: item.name,
      title: item.meta.title,
      path: index === matched.length - 1 ? null : item.path
    })
  })
  
  return breadcrumbItems
})

// 用户菜单选项
const userMenuOptions = [
  {
    label: '个人设置',
    key: 'profile',
    icon: () => h(Icon, { icon: 'mdi:account-cog' })
  },
  {
    label: '修改密码',
    key: 'change-password',
    icon: () => h(Icon, { icon: 'mdi:lock-reset' })
  },
  {
    type: 'divider'
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: () => h(Icon, { icon: 'mdi:logout' })
  }
]

// 切换主题
const toggleTheme = () => {
  themeStore.toggleTheme()
}

// 用户菜单选择处理
const handleUserMenuSelect = (key) => {
  switch (key) {
    case 'profile':
      // TODO: 打开个人设置弹窗
      message.info('个人设置功能开发中')
      break
    case 'change-password':
      // TODO: 打开修改密码弹窗
      message.info('修改密码功能开发中')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  dialog.warning({
    title: '确认退出',
    content: '确定要退出登录吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      authStore.logout()
      router.push('/login')
      message.success('已退出登录')
    }
  })
}
</script>
