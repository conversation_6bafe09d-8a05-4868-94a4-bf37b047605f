<template>
  <div class="h-screen bg-white dark:bg-gray-800 shadow-lg rounded-xl overflow-hidden relative">
    <!-- Logo 区域 -->
    <div class="h-16 flex items-center justify-center border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-2">
        <Icon icon="mdi:shield-network" class="text-2xl text-blue-600" />
        <span v-if="!collapsed" class="text-lg font-bold text-gradient">VPN 管理</span>
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="p-4 pb-16">
      <n-menu
        :value="activeKey"
        :collapsed="collapsed"
        :collapsed-width="64"
        :options="menuOptions"
        :render-label="renderMenuLabel"
        :render-icon="renderMenuIcon"
        @update:value="handleMenuSelect"
      />
    </div>

    <!-- 折叠按钮 -->
    <div class="absolute bottom-4 left-4 right-4">
      <n-button
        quaternary
        circle
        @click="toggleCollapse"
        class="w-full"
      >
        <Icon :icon="collapsed ? 'mdi:chevron-right' : 'mdi:chevron-left'" />
      </n-button>
    </div>
  </div>
</template>

<script setup>
import { computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useThemeStore } from '@/store/theme'
import { useAuthStore } from '@/store/auth'

const router = useRouter()
const route = useRoute()
const themeStore = useThemeStore()
const authStore = useAuthStore()

const collapsed = computed(() => themeStore.sidebarCollapsed)
const activeKey = computed(() => route.name)

// 菜单选项
const menuOptions = computed(() => {
  const routes = router.getRoutes()
  const menuItems = []
  
  routes.forEach(route => {
    if (route.children) {
      route.children.forEach(child => {
        if (child.meta && !child.meta.hideInMenu && child.meta.requiresAuth) {
          // 检查权限
          if (child.meta.roles && !authStore.hasRole(child.meta.roles)) {
            return
          }
          
          menuItems.push({
            label: child.meta.title,
            key: child.name,
            icon: child.meta.icon
          })
        }
      })
    }
  })
  
  return menuItems
})

// 渲染菜单标签
const renderMenuLabel = (option) => {
  return option.label
}

// 渲染菜单图标
const renderMenuIcon = (option) => {
  if (option.icon) {
    return h(Icon, { icon: option.icon, class: 'text-lg' })
  }
  return null
}

// 菜单选择处理
const handleMenuSelect = (key) => {
  router.push({ name: key })
}

// 切换折叠状态
const toggleCollapse = () => {
  themeStore.toggleSidebar()
}
</script>
