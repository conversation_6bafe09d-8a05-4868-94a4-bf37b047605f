<template>
  <div class="h-screen bg-white dark:bg-gray-800 shadow-lg rounded-xl overflow-hidden relative">
    <!-- Logo 区域 -->
    <div class="h-16 flex items-center justify-center border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-2">
        <Icon icon="mdi:shield-network" class="text-2xl text-blue-600" />
        <span v-if="!collapsed" class="text-lg font-bold text-gradient">VPN 管理</span>
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="p-4 pb-16">
      <div class="space-y-2">
        <!-- 仪表盘 -->
        <div
          :class="[
            'flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors',
            activeKey === 'Dashboard'
              ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
              : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
          ]"
          @click="handleMenuSelect('Dashboard')"
        >
          <Icon icon="mdi:view-dashboard" class="text-lg" />
          <span v-if="!collapsed" class="ml-3 text-sm font-medium">仪表盘</span>
        </div>

        <!-- VPN 网络管理 -->
        <div v-if="!collapsed" class="pt-4">
          <div class="flex items-center px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            <Icon icon="mdi:network" class="text-sm mr-2" />
            VPN 网络管理
          </div>
          <div class="mt-2 space-y-1">
            <div
              v-for="item in networkMenuItems"
              :key="item.key"
              :class="[
                'flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors ml-4',
                activeKey === item.key
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
              ]"
              @click="handleMenuSelect(item.key)"
            >
              <Icon :icon="item.icon" class="text-lg" />
              <span class="ml-3 text-sm font-medium">{{ item.label }}</span>
              <span v-if="item.step" class="ml-auto text-xs bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300 px-2 py-0.5 rounded-full">
                {{ item.step }}
              </span>
            </div>
          </div>
        </div>

        <!-- 连接监控 -->
        <div
          :class="[
            'flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors',
            activeKey === 'Connections'
              ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
              : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
          ]"
          @click="handleMenuSelect('Connections')"
        >
          <Icon icon="mdi:monitor-dashboard" class="text-lg" />
          <span v-if="!collapsed" class="ml-3 text-sm font-medium">连接监控</span>
        </div>

        <!-- 用户与权限 -->
        <div v-if="!collapsed && hasUserPermission" class="pt-4">
          <div class="flex items-center px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            <Icon icon="mdi:shield-account" class="text-sm mr-2" />
            用户与权限
          </div>
          <div class="mt-2 space-y-1">
            <div
              v-for="item in userMenuItems"
              :key="item.key"
              :class="[
                'flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors ml-4',
                activeKey === item.key
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
              ]"
              @click="handleMenuSelect(item.key)"
            >
              <Icon :icon="item.icon" class="text-lg" />
              <span class="ml-3 text-sm font-medium">{{ item.label }}</span>
            </div>
          </div>
        </div>

        <!-- 其他菜单项 -->
        <div v-if="!collapsed" class="pt-4">
          <div class="space-y-1">
            <div
              v-for="item in otherMenuItems"
              :key="item.key"
              :class="[
                'flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors',
                activeKey === item.key
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
              ]"
              @click="handleMenuSelect(item.key)"
            >
              <Icon :icon="item.icon" class="text-lg" />
              <span class="ml-3 text-sm font-medium">{{ item.label }}</span>
            </div>
          </div>
        </div>

        <!-- 折叠状态下的简化菜单 -->
        <div v-if="collapsed" class="space-y-1">
          <div
            v-for="item in collapsedMenuItems"
            :key="item.key"
            :class="[
              'flex items-center justify-center p-2 rounded-lg cursor-pointer transition-colors',
              activeKey === item.key
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
            ]"
            @click="handleMenuSelect(item.key)"
            :title="item.label"
          >
            <Icon :icon="item.icon" class="text-lg" />
          </div>
        </div>
      </div>
    </div>

    <!-- 折叠按钮 -->
    <div class="absolute bottom-4 left-4 right-4">
      <n-button
        quaternary
        circle
        @click="toggleCollapse"
        class="w-full"
      >
        <Icon :icon="collapsed ? 'mdi:chevron-right' : 'mdi:chevron-left'" />
      </n-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useThemeStore } from '@/store/theme'
import { useAuthStore } from '@/store/auth'

const router = useRouter()
const route = useRoute()
const themeStore = useThemeStore()
const authStore = useAuthStore()

const collapsed = computed(() => themeStore.sidebarCollapsed)
const activeKey = computed(() => route.name)

// VPN 网络管理菜单
const networkMenuItems = [
  {
    key: 'Subnets',
    label: '子网管理',
    icon: 'mdi:map-marker',
    step: '第一步'
  },
  {
    key: 'Servers',
    label: '服务端管理',
    icon: 'mdi:server',
    step: '第二步'
  },
  {
    key: 'Clients',
    label: '客户端管理',
    icon: 'mdi:laptop',
    step: '第三步'
  },
  {
    key: 'NAT',
    label: 'NAT / 打洞策略',
    icon: 'mdi:wrench',
    step: '可选'
  }
]

// 用户与权限菜单
const userMenuItems = [
  {
    key: 'Users',
    label: '用户管理',
    icon: 'mdi:account-group'
  },
  {
    key: 'Roles',
    label: '角色管理',
    icon: 'mdi:shield-account'
  }
]

// 其他菜单项
const otherMenuItems = [
  {
    key: 'Configs',
    label: '配置中心',
    icon: 'mdi:cog'
  },
  {
    key: 'Logs',
    label: '日志中心',
    icon: 'mdi:text-box-search'
  },
  {
    key: 'Settings',
    label: '系统设置',
    icon: 'mdi:settings'
  }
]

// 折叠状态下的菜单项
const collapsedMenuItems = computed(() => {
  const items = [
    { key: 'Dashboard', label: '仪表盘', icon: 'mdi:view-dashboard' },
    ...networkMenuItems,
    { key: 'Connections', label: '连接监控', icon: 'mdi:monitor-dashboard' },
    ...otherMenuItems
  ]

  // 如果有用户权限，添加用户管理菜单
  if (hasUserPermission.value) {
    items.splice(-3, 0, ...userMenuItems)
  }

  return items
})

// 检查用户权限
const hasUserPermission = computed(() => {
  if (!authStore.isLoggedIn) {
    return false
  }
  return authStore.hasRole && authStore.hasRole(['admin'])
})

// 菜单选择处理
const handleMenuSelect = (key) => {
  router.push({ name: key })
}

// 切换折叠状态
const toggleCollapse = () => {
  themeStore.toggleSidebar()
}
</script>
