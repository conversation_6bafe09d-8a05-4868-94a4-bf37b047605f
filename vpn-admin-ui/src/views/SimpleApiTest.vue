<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">简单 API 测试</h1>
    
    <div class="space-y-4">
      <div class="card p-4">
        <h2 class="text-lg font-semibold mb-4">环境信息</h2>
        <div class="bg-gray-100 p-3 rounded text-sm">
          <div>API 基础地址: {{ apiBaseUrl }}</div>
          <div>当前模式: {{ mode }}</div>
          <div>开发模式: {{ isDev }}</div>
        </div>
      </div>
      
      <div class="card p-4">
        <h2 class="text-lg font-semibold mb-4">直接 API 测试</h2>
        <div class="space-x-2 mb-4">
          <n-button @click="testDirectFetch" :loading="loading">直接 Fetch</n-button>
          <n-button @click="testAxiosRequest" :loading="loading">Axios 请求</n-button>
          <n-button @click="testCreateSubnet" :loading="loading">创建子网</n-button>
        </div>
        
        <div v-if="result" class="mt-4">
          <h3 class="font-semibold mb-2">测试结果:</h3>
          <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto">{{ result }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import axios from 'axios'

const message = useMessage()
const loading = ref(false)
const result = ref('')

const apiBaseUrl = computed(() => import.meta.env.VITE_API_BASE_URL)
const mode = computed(() => import.meta.env.MODE)
const isDev = computed(() => import.meta.env.DEV)

const testDirectFetch = async () => {
  loading.value = true
  try {
    console.log('开始直接 fetch 测试...')
    const url = `${apiBaseUrl.value}/api/subnets`
    console.log('请求URL:', url)
    
    const response = await fetch(url)
    console.log('Fetch 响应:', response)
    
    const data = await response.json()
    console.log('Fetch 数据:', data)
    
    result.value = JSON.stringify({
      success: true,
      status: response.status,
      data: data
    }, null, 2)
    
    message.success('直接 Fetch 测试成功')
  } catch (error) {
    console.error('直接 Fetch 测试失败:', error)
    result.value = JSON.stringify({
      success: false,
      error: error.message,
      stack: error.stack
    }, null, 2)
    
    message.error('直接 Fetch 测试失败')
  } finally {
    loading.value = false
  }
}

const testAxiosRequest = async () => {
  loading.value = true
  try {
    console.log('开始 Axios 测试...')
    const url = `${apiBaseUrl.value}/api/subnets`
    console.log('请求URL:', url)
    
    const response = await axios.get(url)
    console.log('Axios 响应:', response)
    
    result.value = JSON.stringify({
      success: true,
      status: response.status,
      data: response.data
    }, null, 2)
    
    message.success('Axios 测试成功')
  } catch (error) {
    console.error('Axios 测试失败:', error)
    result.value = JSON.stringify({
      success: false,
      error: error.message,
      response: error.response?.data,
      status: error.response?.status
    }, null, 2)
    
    message.error('Axios 测试失败')
  } finally {
    loading.value = false
  }
}

const testCreateSubnet = async () => {
  loading.value = true
  try {
    console.log('开始创建子网测试...')
    const url = `${apiBaseUrl.value}/api/subnets`
    const data = {
      name: 'simple-test-subnet',
      cidr: '***********/24',
      port: 10066,
      mtu: 1500,
      identifier: 'simple-test-subnet-001',
      description: '简单测试页面创建的子网',
      status: 'active'
    }
    
    console.log('请求URL:', url)
    console.log('请求数据:', data)
    
    const response = await axios.post(url, data)
    console.log('创建子网响应:', response)
    
    result.value = JSON.stringify({
      success: true,
      status: response.status,
      data: response.data
    }, null, 2)
    
    message.success('创建子网测试成功')
  } catch (error) {
    console.error('创建子网测试失败:', error)
    result.value = JSON.stringify({
      success: false,
      error: error.message,
      response: error.response?.data,
      status: error.response?.status
    }, null, 2)
    
    message.error('创建子网测试失败')
  } finally {
    loading.value = false
  }
}
</script>
