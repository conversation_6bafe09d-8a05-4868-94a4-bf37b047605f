<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑客户端' : '新增客户端'">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="绑定服务端" path="serverId">
        <n-select
          v-model:value="formData.serverId"
          placeholder="请选择服务端"
          :options="serverOptions"
          :loading="serverLoading"
          @update:value="handleServerChange"
        />
      </n-form-item>

      <n-form-item label="客户端名称" path="name">
        <n-input v-model:value="formData.name" placeholder="请输入客户端名称" />
      </n-form-item>

      <n-form-item label="MAC 地址" path="macAddress">
        <n-input v-model:value="formData.macAddress" placeholder="请输入 MAC 地址，如：00:11:22:33:44:55" />
      </n-form-item>

      <n-form-item label="LAN IP" path="lanIp">
        <n-input v-model:value="formData.lanIp" placeholder="请输入局域网 IP 地址" />
      </n-form-item>

      <n-form-item label="静态路由" path="staticRoute">
        <n-input v-model:value="formData.staticRoute" placeholder="请输入静态路由，如：***********/24" />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { clientsApi } from '@/api/clients'
import { serversApi } from '@/api/servers'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  clientData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)
const serverLoading = ref(false)
const serverOptions = ref([])

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.clientData)

// 表单数据
const formData = reactive({
  serverId: '',
  name: '',
  macAddress: '',
  lanIp: '',
  staticRoute: ''
})

// 表单验证规则
const rules = {
  serverId: [
    { required: true, message: '请选择服务端', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入客户端名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  macAddress: [
    { required: true, message: '请输入 MAC 地址', trigger: 'blur' },
    {
      validator: (rule, value) => {
        console.log('MAC 地址验证器，输入值:', value)
        // MAC 地址验证
        const macPattern = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/
        if (!macPattern.test(value)) {
          console.log('MAC 地址格式验证失败')
          return new Error('请输入有效的 MAC 地址格式，如：00:11:22:33:44:55')
        }
        console.log('MAC 地址验证通过')
        return true
      },
      trigger: 'blur'
    }
  ],
  lanIp: [
    { required: true, message: '请输入 LAN IP 地址', trigger: 'blur' },
    {
      validator: (rule, value) => {
        console.log('LAN IP 验证器，输入值:', value)
        // 简单的 IP 地址验证
        const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/
        if (!ipPattern.test(value)) {
          console.log('LAN IP 格式验证失败')
          return new Error('请输入有效的 IP 地址格式')
        }

        // 检查每个数字是否在 0-255 范围内
        const parts = value.split('.')
        for (let part of parts) {
          const num = parseInt(part)
          if (num < 0 || num > 255) {
            console.log('LAN IP 范围验证失败')
            return new Error('IP 地址每段应在 0-255 范围内')
          }
        }
        console.log('LAN IP 验证通过')
        return true
      },
      trigger: 'blur'
    }
  ],
  staticRoute: [
    {
      validator: (rule, value) => {
        // 静态路由是可选的
        if (!value) {
          return true
        }

        console.log('静态路由验证器，输入值:', value)
        // CIDR 格式验证
        const cidrPattern = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/
        if (!cidrPattern.test(value)) {
          console.log('静态路由格式验证失败')
          return new Error('请输入有效的 CIDR 格式，如：***********/24')
        }

        // 检查 IP 地址部分
        const [ip, mask] = value.split('/')
        const parts = ip.split('.')
        for (let part of parts) {
          const num = parseInt(part)
          if (num < 0 || num > 255) {
            console.log('静态路由 IP 范围验证失败')
            return new Error('IP 地址每段应在 0-255 范围内')
          }
        }

        // 检查子网掩码
        const maskNum = parseInt(mask)
        if (maskNum < 8 || maskNum > 30) {
          console.log('静态路由子网掩码验证失败')
          return new Error('子网掩码应在 8-30 范围内')
        }

        console.log('静态路由验证通过')
        return true
      },
      trigger: 'blur'
    }
  ]
}

// 加载可用服务端
const loadAvailableServers = async () => {
  try {
    serverLoading.value = true
    const response = await serversApi.getAvailableServers()
    console.log('可用服务端响应:', response)

    if (response.code === 0 && response.data) {
      serverOptions.value = response.data.map(server => ({
        label: `${server.name} (${server.publicIp}:${server.port})`,
        value: server.id,
        server: server
      }))
    } else {
      serverOptions.value = []
    }
  } catch (error) {
    console.error('加载服务端失败:', error)
    message.error('加载服务端列表失败')
    serverOptions.value = []
  } finally {
    serverLoading.value = false
  }
}

// 服务端选择变化处理
const handleServerChange = (serverId) => {
  const selectedServer = serverOptions.value.find(option => option.value === serverId)
  if (selectedServer && selectedServer.server) {
    console.log('选择的服务端:', selectedServer.server)
    // 可以根据服务端信息自动填充一些字段
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    serverId: '',
    name: '',
    macAddress: '',
    lanIp: '',
    staticRoute: ''
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      serverId: data.serverId || '',
      name: data.name || '',
      macAddress: data.macAddress || '',
      lanIp: data.lanIp || '',
      staticRoute: data.staticRoute || ''
    })
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    console.log('开始验证表单，当前数据:', formData)

    // 手动验证每个字段
    console.log('验证名称:', formData.name)
    console.log('验证IP:', formData.ip)
    console.log('验证端口:', formData.port)

    await formRef.value?.validate()
    console.log('表单验证通过')

    loading.value = true

    // 提交数据
    const submitData = {
      serverId: formData.serverId,
      name: formData.name,
      macAddress: formData.macAddress,
      lanIp: formData.lanIp,
      staticRoute: formData.staticRoute || null
    }

    console.log('提交数据:', submitData)

    if (isEdit.value) {
      await clientsApi.updateClient(props.clientData.id, submitData)
      message.success('更新成功')
    } else {
      await clientsApi.createClient(submitData)
      message.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交错误:', error)
    console.error('错误详情:', JSON.stringify(error, null, 2))

    if (error.errors) {
      // 表单验证错误，显示具体错误信息
      console.log('验证错误详情:', error.errors)
      const errorMessages = []

      // 遍历所有字段的错误
      Object.keys(error.errors).forEach(fieldName => {
        const fieldErrors = error.errors[fieldName]
        console.log(`字段 ${fieldName} 的错误:`, fieldErrors)

        if (Array.isArray(fieldErrors)) {
          fieldErrors.forEach(err => {
            console.log('单个错误:', err)
            if (err && err.message) {
              errorMessages.push(`${fieldName}: ${err.message}`)
            } else if (typeof err === 'string') {
              errorMessages.push(`${fieldName}: ${err}`)
            }
          })
        }
      })

      if (errorMessages.length > 0) {
        message.error(`验证失败: ${errorMessages.join(', ')}`)
      } else {
        message.error('表单验证失败，请检查输入')
      }
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    loadAvailableServers()
    if (props.clientData) {
      fillFormData(props.clientData)
    } else {
      resetForm()
    }
  }
})

// 组件挂载时加载服务端
onMounted(() => {
  loadAvailableServers()
})
</script>
