<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑客户端' : '新增客户端'">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="客户端名称" path="name">
        <n-input v-model:value="formData.name" placeholder="请输入客户端名称" />
      </n-form-item>
      
      <n-form-item label="IP 地址" path="ip">
        <n-input v-model:value="formData.ip" placeholder="请输入 IP 地址" />
      </n-form-item>
      
      <n-form-item label="端口" path="port">
        <n-input-number v-model:value="formData.port" :min="1" :max="65535" placeholder="请输入端口" />
      </n-form-item>
      
      <n-form-item label="节点类型" path="node_type">
        <n-select
          v-model:value="formData.node_type"
          placeholder="请选择节点类型"
          :options="nodeTypeOptions"
        />
      </n-form-item>
      
      <n-form-item label="隧道类型" path="tunnel_type">
        <n-select
          v-model:value="formData.tunnel_type"
          placeholder="请选择隧道类型"
          :options="tunnelTypeOptions"
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { clientsApi } from '@/api/clients'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  clientData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.clientData)

// 表单数据
const formData = reactive({
  name: '',
  ip: '',
  port: 655,
  node_type: 'client',
  tunnel_type: 'tcp'
})

// 节点类型选项
const nodeTypeOptions = [
  { label: '客户端', value: 'client' },
  { label: '服务端', value: 'server' }
]

// 隧道类型选项
const tunnelTypeOptions = [
  { label: 'TCP', value: 'tcp' },
  { label: 'UDP', value: 'udp' }
]

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入客户端名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  ip: [
    { required: true, message: '请输入 IP 地址', trigger: 'blur' },
    { 
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: '请输入有效的 IP 地址',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围 1-65535', trigger: 'blur' }
  ],
  node_type: [
    { required: true, message: '请选择节点类型', trigger: 'change' }
  ],
  tunnel_type: [
    { required: true, message: '请选择隧道类型', trigger: 'change' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    ip: '',
    port: 655,
    node_type: 'client',
    tunnel_type: 'tcp'
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      name: data.name || '',
      ip: data.ip || '',
      port: data.port || 655,
      node_type: data.node_type || 'client',
      tunnel_type: data.tunnel_type || 'tcp'
    })
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    if (isEdit.value) {
      await clientsApi.updateClient(props.clientData.id, formData)
      message.success('更新成功')
    } else {
      await clientsApi.createClient(formData)
      message.success('创建成功')
    }
    
    emit('success')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    if (props.clientData) {
      fillFormData(props.clientData)
    } else {
      resetForm()
    }
  }
})
</script>
