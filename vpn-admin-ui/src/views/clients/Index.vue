<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">客户端管理</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <Icon icon="mdi:plus" />
          </template>
          新增客户端
        </n-button>
      </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <n-input
          v-model:value="searchForm.keyword"
          placeholder="搜索客户端名称、IP"
          clearable
        >
          <template #prefix>
            <Icon icon="mdi:magnify" />
          </template>
        </n-input>
        
        <SafeSelect
          v-model:value="searchForm.status"
          placeholder="状态筛选"
          clearable
          :options="statusOptions"
        />

        <SafeSelect
          v-model:value="searchForm.natType"
          placeholder="NAT 类型"
          clearable
          :options="natTypeOptions"
        />
        
        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <Icon icon="mdi:magnify" />
          </template>
          搜索
        </n-button>
      </div>
    </div>
    
    <!-- 客户端列表 -->
    <div class="card">
      <SafeDataTable
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>
    
    <!-- 新增/编辑客户端弹窗 -->
    <ClientModal
      v-model:show="showCreateModal"
      :client-data="editingClient"
      @success="handleModalSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'
import { formatTime, formatStatus, formatNatType } from '@/utils/format'
import { clientsApi } from '@/api/clients'
import SafeSelect from '@/components/SafeSelect.vue'
import SafeDataTable from '@/components/SafeDataTable.vue'
import ClientModal from './components/ClientModal.vue'

const router = useRouter()
const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const showCreateModal = ref(false)
const editingClient = ref(null)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null,
  natType: null
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 状态选项
const statusOptions = [
  { label: '在线', value: 'online' },
  { label: '离线', value: 'offline' },
  { label: '连接中', value: 'connecting' }
]

// NAT 类型选项
const natTypeOptions = [
  { label: '完全锥形', value: 'full_cone' },
  { label: '受限锥形', value: 'restricted_cone' },
  { label: '端口受限', value: 'port_restricted_cone' },
  { label: '对称型', value: 'symmetric' },
  { label: '未知', value: 'unknown' }
]

// 表格列配置
const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    ellipsis: true
  },
  {
    title: '名称',
    key: 'name',
    width: 120
  },
  {
    title: 'IP 地址',
    key: 'ip',
    width: 120
  },
  {
    title: '端口',
    key: 'port',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        online: 'success',
        offline: 'error',
        connecting: 'warning'
      }
      return h('n-tag', {
        type: statusMap[row.status] || 'default',
        size: 'small'
      }, formatStatus(row.status))
    }
  },
  {
    title: 'NAT 类型',
    key: 'nat_type',
    width: 120,
    render: (row) => formatNatType(row.nat_type)
  },
  {
    title: '最后活跃',
    key: 'last_active',
    width: 150,
    render: (row) => formatTime(row.last_active)
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          onClick: () => viewClient(row.id)
        }, '详情'),
        h('n-button', {
          size: 'small',
          type: 'primary',
          onClick: () => editClient(row)
        }, '编辑'),
        h('n-button', {
          size: 'small',
          type: 'warning',
          onClick: () => testConnection(row.id)
        }, '测试'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => deleteClient(row)
        }, '删除')
      ])
    }
  }
]

// 加载客户端列表
const loadClients = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await clientsApi.getClients(params)
    console.log('API response:', response)

    // 处理 API 响应数据结构
    if (response.data && response.data.items) {
      // 如果响应包含 items 数组
      tableData.value = response.data.items || []
      pagination.itemCount = response.data.total || 0
    } else if (Array.isArray(response.data)) {
      // 如果响应直接是数组
      tableData.value = response.data
      pagination.itemCount = response.data.length
    } else {
      // 降级处理
      console.warn('Unexpected API response structure:', response)
      tableData.value = []
      pagination.itemCount = 0
    }
  } catch (error) {
    message.error('加载客户端列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadClients()
}

// 刷新数据
const refreshData = () => {
  loadClients()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  loadClients()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadClients()
}

// 查看客户端详情
const viewClient = (id) => {
  router.push(`/clients/${id}`)
}

// 编辑客户端
const editClient = (client) => {
  editingClient.value = client
  showCreateModal.value = true
}

// 测试连接
const testConnection = async (id) => {
  try {
    await clientsApi.testConnection(id)
    message.success('连接测试成功')
  } catch (error) {
    message.error('连接测试失败')
  }
}

// 删除客户端
const deleteClient = (client) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除客户端 "${client.name}" 吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await clientsApi.deleteClient(client.id)
        message.success('删除成功')
        loadClients()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 弹窗成功回调
const handleModalSuccess = () => {
  showCreateModal.value = false
  editingClient.value = null
  loadClients()
}

onMounted(() => {
  loadClients()
})
</script>
