<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <n-button @click="goBack" quaternary circle>
          <Icon icon="mdi:arrow-left" />
        </n-button>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">客户端详情</h1>
      </div>
    </div>
    
    <div class="card">
      <p class="text-center text-gray-500">客户端详情页面开发中...</p>
      <p class="text-center text-sm text-gray-400 mt-2">客户端 ID: {{ $route.params.id }}</p>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}
</script>
