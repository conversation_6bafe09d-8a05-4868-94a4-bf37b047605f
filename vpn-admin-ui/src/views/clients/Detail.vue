<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <n-button @click="goBack" quaternary circle>
          <Icon icon="mdi:arrow-left" />
        </n-button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">客户端详情</h1>
          <p class="text-sm text-gray-500 mt-1">{{ clientData?.name || '加载中...' }}</p>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showEditModal = true" :disabled="!clientData">
          <template #icon>
            <Icon icon="mdi:pencil" />
          </template>
          编辑
        </n-button>
        <n-dropdown :options="actionOptions" @select="handleAction">
          <n-button>
            <template #icon>
              <Icon icon="mdi:dots-vertical" />
            </template>
            更多操作
          </n-button>
        </n-dropdown>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && !clientData" class="card">
      <div class="flex items-center justify-center py-12">
        <n-spin size="large" />
      </div>
    </div>

    <!-- 客户端信息 -->
    <div v-else-if="clientData" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 基本信息 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 状态卡片 -->
        <div class="card">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">连接状态</h3>
            <n-tag :type="getStatusType(clientData.status)" size="large">
              <template #icon>
                <Icon :icon="getStatusIcon(clientData.status)" />
              </template>
              {{ formatStatus(clientData.status) }}
            </n-tag>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <p class="text-2xl font-bold text-blue-600">{{ connectionStats.uptime }}</p>
              <p class="text-sm text-gray-500">在线时长</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-green-600">{{ connectionStats.bytesIn }}</p>
              <p class="text-sm text-gray-500">下载流量</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-purple-600">{{ connectionStats.bytesOut }}</p>
              <p class="text-sm text-gray-500">上传流量</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-orange-600">{{ connectionStats.latency }}</p>
              <p class="text-sm text-gray-500">延迟</p>
            </div>
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="card">
          <h3 class="text-lg font-semibold mb-4">基本信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">客户端名称</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ clientData.name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">IP 地址</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ clientData.ip }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">端口</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ clientData.port }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">MAC 地址</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ clientData.mac || '-' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">节点类型</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ clientData.node_type }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">隧道类型</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ clientData.tunnel_type }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">NAT 类型</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatNatType(clientData.nat_type) }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">最后活跃</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatTime(clientData.last_active) }}</p>
            </div>
          </div>
        </div>

        <!-- 连接历史 -->
        <div class="card">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">连接历史</h3>
            <n-button size="small" @click="loadConnectionHistory">
              <template #icon>
                <Icon icon="mdi:refresh" />
              </template>
              刷新
            </n-button>
          </div>

          <n-data-table
            :columns="historyColumns"
            :data="connectionHistory"
            :loading="historyLoading"
            size="small"
            :pagination="{ pageSize: 5 }"
          />
        </div>
      </div>

      <!-- 右侧信息 -->
      <div class="space-y-6">
        <!-- 快速操作 -->
        <div class="card">
          <h3 class="text-lg font-semibold mb-4">快速操作</h3>
          <div class="space-y-3">
            <n-button block @click="testConnection" :loading="testing">
              <template #icon>
                <Icon icon="mdi:network-outline" />
              </template>
              测试连接
            </n-button>
            <n-button block @click="pushConfig" :loading="pushing">
              <template #icon>
                <Icon icon="mdi:upload" />
              </template>
              重推配置
            </n-button>
            <n-button block @click="forceDisconnect" type="warning">
              <template #icon>
                <Icon icon="mdi:connection" />
              </template>
              强制断线
            </n-button>
            <n-button block @click="detectNat" :loading="detecting">
              <template #icon>
                <Icon icon="mdi:radar" />
              </template>
              NAT 探测
            </n-button>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="card">
          <h3 class="text-lg font-semibold mb-4">系统信息</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">操作系统</span>
              <span class="text-sm">{{ systemInfo.os || '-' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">客户端版本</span>
              <span class="text-sm">{{ systemInfo.version || '-' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">CPU 使用率</span>
              <span class="text-sm">{{ systemInfo.cpu || '-' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">内存使用</span>
              <span class="text-sm">{{ systemInfo.memory || '-' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">网络延迟</span>
              <span class="text-sm">{{ systemInfo.ping || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 配置信息 -->
        <div class="card">
          <h3 class="text-lg font-semibold mb-4">配置信息</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">配置版本</span>
              <span class="text-sm">{{ configInfo.version || '-' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">最后更新</span>
              <span class="text-sm">{{ formatTime(configInfo.lastUpdate) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">配置状态</span>
              <n-tag size="small" :type="configInfo.status === 'synced' ? 'success' : 'warning'">
                {{ configInfo.status === 'synced' ? '已同步' : '待同步' }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="card">
      <div class="text-center py-12">
        <Icon icon="mdi:alert-circle" class="text-4xl text-red-500 mb-4" />
        <h3 class="text-lg font-semibold mb-2">加载失败</h3>
        <p class="text-gray-500 mb-4">无法加载客户端信息</p>
        <n-button @click="refreshData">重试</n-button>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <ClientModal
      v-model:show="showEditModal"
      :client-data="clientData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'
import { formatTime, formatStatus, formatNatType, formatFileSize } from '@/utils/format'
import { clientsApi } from '@/api/clients'
import ClientModal from './components/ClientModal.vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const historyLoading = ref(false)
const testing = ref(false)
const pushing = ref(false)
const detecting = ref(false)
const showEditModal = ref(false)

const clientData = ref(null)
const connectionHistory = ref([])

// 连接统计
const connectionStats = reactive({
  uptime: '0h 0m',
  bytesIn: '0 B',
  bytesOut: '0 B',
  latency: '0ms'
})

// 系统信息
const systemInfo = reactive({
  os: 'Linux Ubuntu 20.04',
  version: 'v1.2.3',
  cpu: '15%',
  memory: '256MB / 1GB',
  ping: '25ms'
})

// 配置信息
const configInfo = reactive({
  version: 'v1.0.1',
  lastUpdate: new Date(),
  status: 'synced'
})

// 操作选项
const actionOptions = [
  {
    label: '查看日志',
    key: 'logs',
    icon: () => h(Icon, { icon: 'mdi:text-box-search' })
  },
  {
    label: '下载配置',
    key: 'download',
    icon: () => h(Icon, { icon: 'mdi:download' })
  },
  {
    type: 'divider'
  },
  {
    label: '删除客户端',
    key: 'delete',
    icon: () => h(Icon, { icon: 'mdi:delete' })
  }
]

// 连接历史表格列
const historyColumns = [
  {
    title: '连接时间',
    key: 'connectTime',
    width: 150,
    render: (row) => formatTime(row.connectTime)
  },
  {
    title: '断开时间',
    key: 'disconnectTime',
    width: 150,
    render: (row) => formatTime(row.disconnectTime)
  },
  {
    title: '持续时间',
    key: 'duration',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row) => {
      const statusMap = {
        success: 'success',
        failed: 'error',
        timeout: 'warning'
      }
      return h('n-tag', {
        type: statusMap[row.status] || 'default',
        size: 'small'
      }, formatStatus(row.status))
    }
  }
]

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    online: 'success',
    offline: 'error',
    connecting: 'warning'
  }
  return statusMap[status] || 'default'
}

// 获取状态图标
const getStatusIcon = (status) => {
  const iconMap = {
    online: 'mdi:check-circle',
    offline: 'mdi:close-circle',
    connecting: 'mdi:loading'
  }
  return iconMap[status] || 'mdi:help-circle'
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 刷新数据
const refreshData = async () => {
  await loadClientData()
  await loadConnectionHistory()
  message.success('数据刷新成功')
}

// 加载客户端数据
const loadClientData = async () => {
  loading.value = true
  try {
    const response = await clientsApi.getClient(route.params.id)
    clientData.value = response.data

    // 更新连接统计（模拟数据）
    connectionStats.uptime = '2h 35m'
    connectionStats.bytesIn = formatFileSize(1024 * 1024 * 150) // 150MB
    connectionStats.bytesOut = formatFileSize(1024 * 1024 * 80)  // 80MB
    connectionStats.latency = '25ms'

  } catch (error) {
    message.error('加载客户端信息失败')
  } finally {
    loading.value = false
  }
}

// 加载连接历史
const loadConnectionHistory = async () => {
  historyLoading.value = true
  try {
    // 模拟连接历史数据
    connectionHistory.value = [
      {
        id: 1,
        connectTime: new Date(Date.now() - 3600000),
        disconnectTime: new Date(),
        duration: '1h 0m',
        status: 'success'
      },
      {
        id: 2,
        connectTime: new Date(Date.now() - 7200000),
        disconnectTime: new Date(Date.now() - 3600000),
        duration: '1h 0m',
        status: 'success'
      },
      {
        id: 3,
        connectTime: new Date(Date.now() - 10800000),
        disconnectTime: new Date(Date.now() - 7200000),
        duration: '45m',
        status: 'timeout'
      }
    ]
  } catch (error) {
    message.error('加载连接历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 测试连接
const testConnection = async () => {
  testing.value = true
  try {
    await clientsApi.testConnection(route.params.id)
    message.success('连接测试成功')
  } catch (error) {
    message.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

// 重推配置
const pushConfig = async () => {
  pushing.value = true
  try {
    await clientsApi.pushConfig(route.params.id)
    message.success('配置推送成功')
    configInfo.status = 'synced'
    configInfo.lastUpdate = new Date()
  } catch (error) {
    message.error('配置推送失败')
  } finally {
    pushing.value = false
  }
}

// 强制断线
const forceDisconnect = () => {
  dialog.warning({
    title: '确认断线',
    content: '确定要强制断开此客户端的连接吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await clientsApi.disconnect(route.params.id)
        message.success('已强制断线')
        clientData.value.status = 'offline'
      } catch (error) {
        message.error('断线操作失败')
      }
    }
  })
}

// NAT 探测
const detectNat = async () => {
  detecting.value = true
  try {
    // 模拟 NAT 探测
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('NAT 探测完成')
    clientData.value.nat_type = 'full_cone'
  } catch (error) {
    message.error('NAT 探测失败')
  } finally {
    detecting.value = false
  }
}

// 处理操作选择
const handleAction = (key) => {
  switch (key) {
    case 'logs':
      router.push(`/logs?client=${route.params.id}`)
      break
    case 'download':
      message.info('配置下载功能开发中')
      break
    case 'delete':
      handleDelete()
      break
  }
}

// 删除客户端
const handleDelete = () => {
  dialog.error({
    title: '确认删除',
    content: `确定要删除客户端 "${clientData.value?.name}" 吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await clientsApi.deleteClient(route.params.id)
        message.success('删除成功')
        router.push('/clients')
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 编辑成功回调
const handleEditSuccess = () => {
  showEditModal.value = false
  loadClientData()
}

onMounted(() => {
  loadClientData()
  loadConnectionHistory()
})
</script>
