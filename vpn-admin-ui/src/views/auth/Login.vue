<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- 登录卡片 -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 animate-fade-in">
        <!-- Logo 和标题 -->
        <div class="text-center mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mb-4">
            <Icon icon="mdi:shield-network" class="text-2xl text-white" />
          </div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">VPN 管理系统</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400">管理员后台登录</p>
        </div>
        
        <!-- 登录表单 -->
        <n-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          @submit.prevent="handleLogin"
        >
          <n-form-item path="username" :show-label="false">
            <n-input
              v-model:value="formData.username"
              placeholder="用户名"
              size="large"
              :input-props="{ autocomplete: 'username' }"
            >
              <template #prefix>
                <Icon icon="mdi:account" class="text-gray-400" />
              </template>
            </n-input>
          </n-form-item>
          
          <n-form-item path="password" :show-label="false">
            <n-input
              v-model:value="formData.password"
              type="password"
              placeholder="密码"
              size="large"
              show-password-on="mousedown"
              :input-props="{ autocomplete: 'current-password' }"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <Icon icon="mdi:lock" class="text-gray-400" />
              </template>
            </n-input>
          </n-form-item>
          
          <div class="flex items-center justify-between mb-6">
            <n-checkbox v-model:checked="rememberMe">
              记住密码
            </n-checkbox>
            <n-button text type="primary" size="small">
              忘记密码？
            </n-button>
          </div>
          
          <n-button
            type="primary"
            size="large"
            block
            :loading="loading"
            @click="handleLogin"
            class="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
          >
            登录
          </n-button>
        </n-form>
        
        <!-- 底部信息 -->
        <div class="mt-8 text-center">
          <p class="text-xs text-gray-400">
            © 2024 VPN 管理系统. 版本 {{ version }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/store/auth'

const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()

const formRef = ref(null)
const loading = ref(false)
const rememberMe = ref(false)
const version = import.meta.env.VITE_APP_VERSION || '1.0.0'

// 表单数据
const formData = reactive({
  username: 'admin',
  password: 'admin123'
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    const result = await authStore.login({
      username: formData.username,
      password: formData.password
    })
    
    if (result.success) {
      message.success('登录成功')
      router.push('/dashboard')
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
