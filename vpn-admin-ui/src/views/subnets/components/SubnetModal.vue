<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑子网' : '新增子网'">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <!-- 只在新增时显示子网数量 -->
      <n-form-item v-if="!isEdit" label="子网数量" path="count">
        <n-input-number
          v-model:value="formData.count"
          :min="1"
          :max="10"
          placeholder="要创建的子网数量"
          @update:value="handleCountChange"
        />
        <template #suffix>
          <span class="text-gray-500 text-sm ml-2">默认值：1</span>
        </template>
      </n-form-item>

      <n-form-item label="子网名称" path="name">
        <n-input v-model:value="formData.name" placeholder="subnet001" />
        <template #suffix>
          <span class="text-gray-500 text-sm ml-2">自动生成：subnet001, subnet002...</span>
        </template>
      </n-form-item>
      
      <n-form-item label="网段 (CIDR)" path="cidr">
        <n-input v-model:value="formData.cidr" placeholder="***********/24" />
        <template #suffix>
          <span class="text-gray-500 text-sm ml-2">自动生成：***********/24, 100.64.12.0/24...</span>
        </template>
      </n-form-item>

      <n-form-item label="监听端口" path="port">
        <n-input-number v-model:value="formData.port" :min="1024" :max="65535" placeholder="10001" />
        <template #suffix>
          <span class="text-gray-500 text-sm ml-2">自动生成：10001, 10002, 10003...</span>
        </template>
      </n-form-item>
      
      <n-form-item label="MTU" path="mtu">
        <n-input-number v-model:value="formData.mtu" :min="576" :max="9000" placeholder="最大传输单元" />
      </n-form-item>
      
      <n-form-item label="标识码" path="identifier">
        <n-input v-model:value="formData.identifier" placeholder="subnet-001" />
        <template #suffix>
          <span class="text-gray-500 text-sm ml-2">自动生成：subnet-001, subnet-002...</span>
        </template>
      </n-form-item>
      
      <n-form-item label="描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入子网描述"
          :rows="3"
        />
      </n-form-item>
      
      <n-form-item label="状态" path="status">
        <n-select
          v-model:value="formData.status"
          placeholder="请选择状态"
          :options="statusOptions"
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { subnetsApi } from '@/api/subnets'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  subnetData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.subnetData)

// 表单数据
const formData = reactive({
  count: 1, // 新增：子网数量
  name: 'subnet001',
  cidr: '***********/24',
  port: 10001,
  mtu: 1500,
  identifier: 'subnet-001',
  description: '',
  status: 'active'
})

// 状态选项
const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '停用', value: 'inactive' }
]

// 表单验证规则
const rules = {
  count: [
    { required: true, message: '请输入子网数量', trigger: 'blur' },
    {
      validator: (rule, value) => {
        const count = Number(value)
        if (isNaN(count) || count < 1 || count > 10) {
          return new Error('子网数量范围 1-10')
        }
        return true
      },
      trigger: 'blur'
    }
  ],
  name: [
    { required: true, message: '请输入子网名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  cidr: [
    { required: true, message: '请输入网段', trigger: 'blur' },
    {
      validator: (rule, value) => {
        console.log('CIDR 验证器，输入值:', value)
        // CIDR 格式验证
        const cidrPattern = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/
        if (!cidrPattern.test(value)) {
          console.log('CIDR 格式验证失败')
          return new Error('请输入有效的 CIDR 格式，如：*********/24')
        }
        
        // 检查 IP 地址部分
        const [ip, mask] = value.split('/')
        const parts = ip.split('.')
        for (let part of parts) {
          const num = parseInt(part)
          if (num < 0 || num > 255) {
            console.log('IP 范围验证失败')
            return new Error('IP 地址每段应在 0-255 范围内')
          }
        }
        
        // 检查子网掩码
        const maskNum = parseInt(mask)
        if (maskNum < 8 || maskNum > 30) {
          console.log('子网掩码验证失败')
          return new Error('子网掩码应在 8-30 范围内')
        }
        
        console.log('CIDR 验证通过')
        return true
      },
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入监听端口', trigger: 'blur' },
    {
      validator: (rule, value) => {
        const port = Number(value)
        if (isNaN(port) || port < 1024 || port > 65535) {
          return new Error('端口范围 1024-65535')
        }
        return true
      },
      trigger: 'blur'
    }
  ],
  mtu: [
    { required: true, message: '请输入 MTU', trigger: 'blur' },
    {
      validator: (rule, value) => {
        const mtu = Number(value)
        if (isNaN(mtu) || mtu < 576 || mtu > 9000) {
          return new Error('MTU 范围 576-9000')
        }
        return true
      },
      trigger: 'blur'
    }
  ],
  identifier: [
    { required: true, message: '请输入标识码', trigger: 'blur' },
    { min: 2, max: 30, message: '标识码长度在 2 到 30 个字符', trigger: 'blur' },
    {
      validator: (rule, value) => {
        // 标识码只能包含字母、数字、连字符和下划线
        const identifierPattern = /^[a-zA-Z0-9_-]+$/
        if (!identifierPattern.test(value)) {
          return new Error('标识码只能包含字母、数字、连字符和下划线')
        }
        return true
      },
      trigger: 'blur'
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 处理子网数量变化
const handleCountChange = (count) => {
  if (count && count >= 1) {
    // 根据数量更新默认值
    updateDefaultValues(count)
  }
}

// 更新默认值
const updateDefaultValues = (count = 1) => {
  // 更新名称
  formData.name = `subnet${String(1).padStart(3, '0')}`

  // 更新网段 - 从 ***********/24 开始
  formData.cidr = '***********/24'

  // 更新端口
  formData.port = 10001

  // 更新标识码
  formData.identifier = 'subnet-001'

  // 如果数量大于1，在描述中显示将要创建的所有子网信息
  if (count > 1) {
    const names = []
    const cidrs = []
    const ports = []
    const identifiers = []

    for (let i = 0; i < count; i++) {
      names.push(`subnet${String(i + 1).padStart(3, '0')}`)
      cidrs.push(`100.64.${11 + i}.0/24`)
      ports.push(10001 + i)
      identifiers.push(`subnet-${String(i + 1).padStart(3, '0')}`)
    }

    formData.description = `将创建 ${count} 个子网：
名称：${names.join(', ')}
网段：${cidrs.join(', ')}
端口：${ports.join(', ')}
标识：${identifiers.join(', ')}`
  } else {
    formData.description = ''
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    count: 1,
    name: 'subnet001',
    cidr: '***********/24',
    port: 10001,
    mtu: 1500,
    identifier: 'subnet-001',
    description: '',
    status: 'active'
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      name: data.name || '',
      cidr: data.cidr || '',
      port: data.port || 10001,
      mtu: data.mtu || 1500,
      identifier: data.identifier || '',
      description: data.description || '',
      status: data.status || 'active'
    })
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    console.log('开始验证表单，当前数据:', formData)
    await formRef.value?.validate()
    console.log('表单验证通过')

    loading.value = true

    if (isEdit.value) {
      // 编辑模式：单个子网更新
      const submitData = {
        name: formData.name,
        cidr: formData.cidr,
        port: Number(formData.port),
        mtu: Number(formData.mtu),
        identifier: formData.identifier,
        description: formData.description,
        status: formData.status
      }

      console.log('更新数据:', submitData)
      await subnetsApi.updateSubnet(props.subnetData.id, submitData)
      message.success('更新成功')
    } else {
      // 新增模式：支持批量创建
      const count = Number(formData.count) || 1
      const subnetsToCreate = []

      for (let i = 0; i < count; i++) {
        const subnetData = {
          name: `subnet${String(i + 1).padStart(3, '0')}`,
          cidr: `100.64.${11 + i}.0/24`,
          port: 10001 + i,
          mtu: Number(formData.mtu),
          identifier: `subnet-${String(i + 1).padStart(3, '0')}`,
          description: count > 1 ? `批量创建的第 ${i + 1} 个子网` : formData.description,
          status: formData.status
        }
        subnetsToCreate.push(subnetData)
      }

      console.log('批量创建数据:', subnetsToCreate)

      // 逐个创建子网
      const results = []
      for (let i = 0; i < subnetsToCreate.length; i++) {
        try {
          const result = await subnetsApi.createSubnet(subnetsToCreate[i])
          results.push({ success: true, data: result, subnet: subnetsToCreate[i] })
        } catch (err) {
          console.error(`创建第 ${i + 1} 个子网失败:`, err)
          results.push({ success: false, error: err, subnet: subnetsToCreate[i] })
        }
      }

      // 统计结果
      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      if (successCount > 0) {
        message.success(`成功创建 ${successCount} 个子网${failCount > 0 ? `，${failCount} 个失败` : ''}`)
      } else {
        message.error('所有子网创建失败')
        return
      }
    }

    emit('success')
  } catch (error) {
    console.error('提交错误:', error)

    // 检查是否是数组错误（批量创建时的错误）
    if (Array.isArray(error)) {
      console.error('批量创建错误数组:', error)
      message.error(`批量创建失败，共 ${error.length} 个错误`)
      return
    }

    if (error.errors) {
      // 表单验证错误
      console.log('验证错误详情:', error.errors)
      const errorMessages = []
      
      Object.keys(error.errors).forEach(fieldName => {
        const fieldErrors = error.errors[fieldName]
        if (Array.isArray(fieldErrors)) {
          fieldErrors.forEach(err => {
            if (err && err.message) {
              errorMessages.push(`${fieldName}: ${err.message}`)
            }
          })
        }
      })
      
      if (errorMessages.length > 0) {
        message.error(`验证失败: ${errorMessages.join(', ')}`)
      } else {
        message.error('表单验证失败，请检查输入')
      }
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    if (props.subnetData) {
      fillFormData(props.subnetData)
    } else {
      resetForm()
    }
  }
})
</script>
