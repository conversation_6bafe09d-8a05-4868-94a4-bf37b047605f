<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">子网管理</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">管理 VPN 子网配置，为服务端提供网络基础</p>
      </div>
      <div class="flex space-x-2">
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <Icon icon="mdi:plus" />
          </template>
          新增子网
        </n-button>
        <n-button type="info" @click="testApiCall">
          <template #icon>
            <Icon icon="mdi:test-tube" />
          </template>
          测试 API
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <StatCard
        title="总子网数"
        :value="subnetStats.total"
        icon="mdi:network"
        color="blue"
      />
      <StatCard
        title="活跃子网"
        :value="subnetStats.active"
        icon="mdi:network-strength-4"
        color="green"
      />
      <StatCard
        title="服务端总数"
        :value="subnetStats.totalServers"
        icon="mdi:server"
        color="purple"
      />
      <StatCard
        title="客户端总数"
        :value="subnetStats.totalClients"
        icon="mdi:laptop"
        color="orange"
      />
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">子网列表</h3>
        <div class="flex items-center space-x-3">
          <n-input
            v-model:value="searchForm.keyword"
            placeholder="搜索子网名称或网段"
            clearable
            class="w-64"
          >
            <template #prefix>
              <Icon icon="mdi:magnify" />
            </template>
          </n-input>
          <n-select
            v-model:value="searchForm.status"
            placeholder="状态筛选"
            clearable
            :options="statusOptions"
            class="w-32"
          />
          <n-button @click="handleSearch">
            <template #icon>
              <Icon icon="mdi:magnify" />
            </template>
            搜索
          </n-button>
          <n-button @click="refreshData">
            <template #icon>
              <Icon icon="mdi:refresh" />
            </template>
            刷新
          </n-button>
        </div>
      </div>

      <!-- 子网表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="row => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- 新增/编辑子网弹窗 -->
    <SubnetModal
      v-model:show="showCreateModal"
      :subnet-data="editingSubnet"
      @success="handleModalSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { Icon } from '@iconify/vue'
import { formatTime } from '@/utils/format'
import { subnetsApi } from '@/api/subnets'
import StatCard from '@/components/StatCard.vue'
import SubnetModal from './components/SubnetModal.vue'

const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const showCreateModal = ref(false)
const editingSubnet = ref(null)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null
})

// 子网统计
const subnetStats = reactive({
  total: 0,
  active: 0,
  totalServers: 0,
  totalClients: 0
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 状态选项
const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '停用', value: 'inactive' }
]

// 表格列配置
const columns = [
  {
    title: '子网名称',
    key: 'name',
    width: 150,
    render: (row) => h('div', { class: 'flex items-center space-x-2' }, [
      h('div', {
        class: `w-2 h-2 rounded-full ${getStatusColor(row.status)}`
      }),
      h('span', { class: 'font-medium' }, row.name)
    ])
  },
  {
    title: '网段',
    key: 'cidr',
    width: 140,
    render: (row) => h('code', { class: 'text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded' }, row.cidr)
  },
  {
    title: '监听端口',
    key: 'port',
    width: 100
  },
  {
    title: 'MTU',
    key: 'mtu',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        active: { type: 'success', label: '活跃' },
        inactive: { type: 'error', label: '停用' }
      }
      const statusInfo = statusMap[row.status] || { type: 'default', label: row.status }
      return h('n-tag', {
        type: statusInfo.type,
        size: 'small'
      }, statusInfo.label)
    }
  },
  {
    title: '服务端数',
    key: 'serverCount',
    width: 100,
    render: (row) => h('span', { class: 'text-blue-600' }, row.serverCount || 0)
  },
  {
    title: '客户端数',
    key: 'clientCount',
    width: 100,
    render: (row) => h('span', { class: 'text-green-600' }, row.clientCount || 0)
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 150,
    render: (row) => formatTime(row.createdAt)
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          type: 'primary',
          onClick: () => editSubnet(row)
        }, '编辑'),
        h('n-button', {
          size: 'small',
          type: row.status === 'active' ? 'warning' : 'success',
          onClick: () => toggleSubnetStatus(row)
        }, row.status === 'active' ? '停用' : '启用'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => deleteSubnet(row),
          disabled: (row.serverCount || 0) > 0
        }, '删除')
      ])
    }
  }
]

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    active: 'bg-green-500',
    inactive: 'bg-red-500'
  }
  return colorMap[status] || 'bg-gray-500'
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await subnetsApi.getSubnets(params)
    console.log('子网列表响应:', response)
    
    if (response.code === 0 && response.data) {
      let subnets = []
      if (response.data.items) {
        subnets = response.data.items
        pagination.itemCount = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        subnets = response.data
        pagination.itemCount = response.data.length
      }
      
      tableData.value = subnets
      
      // 更新统计数据
      subnetStats.total = subnets.length
      subnetStats.active = subnets.filter(s => s.status === 'active').length
      subnetStats.totalServers = subnets.reduce((sum, s) => sum + (s.serverCount || 0), 0)
      subnetStats.totalClients = subnets.reduce((sum, s) => sum + (s.clientCount || 0), 0)
    } else {
      tableData.value = []
      pagination.itemCount = 0
      subnetStats.total = 0
      subnetStats.active = 0
      subnetStats.totalServers = 0
      subnetStats.totalClients = 0
    }

  } catch (error) {
    console.error('加载子网数据失败:', error)
    message.error('加载数据失败')
    tableData.value = []
    pagination.itemCount = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

// 编辑子网
const editSubnet = (subnet) => {
  editingSubnet.value = subnet
  showCreateModal.value = true
}

// 切换子网状态
const toggleSubnetStatus = (subnet) => {
  const action = subnet.status === 'active' ? '停用' : '启用'
  dialog.warning({
    title: `确认${action}`,
    content: `确定要${action}子网 "${subnet.name}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await subnetsApi.updateSubnetStatus(subnet.id, {
          status: subnet.status === 'active' ? 'inactive' : 'active'
        })
        message.success(`${action}成功`)
        loadData()
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 删除子网
const deleteSubnet = (subnet) => {
  if ((subnet.serverCount || 0) > 0) {
    message.error('该子网下还有服务端，无法删除')
    return
  }

  dialog.error({
    title: '确认删除',
    content: `确定要删除子网 "${subnet.name}" 吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await subnetsApi.deleteSubnet(subnet.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 弹窗成功回调
const handleModalSuccess = () => {
  showCreateModal.value = false
  editingSubnet.value = null
  loadData()
}

// 测试 API 调用
const testApiCall = async () => {
  try {
    console.log('开始测试 API 调用...')
    console.log('API 基础地址:', import.meta.env.VITE_API_BASE_URL)

    const response = await subnetsApi.getSubnets()
    console.log('API 调用成功:', response)
    message.success(`API 调用成功，获取到 ${response.data?.length || 0} 个子网`)
  } catch (error) {
    console.error('API 调用失败:', error)
    message.error(`API 调用失败: ${error.message}`)
  }
}

onMounted(() => {
  console.log('子网管理页面已挂载')
  console.log('环境变量:', import.meta.env)
  loadData()
})
</script>
