<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">配置管理</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button @click="showUploadModal = true">
          <template #icon>
            <Icon icon="mdi:upload" />
          </template>
          上传配置
        </n-button>
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <Icon icon="mdi:plus" />
          </template>
          新建配置
        </n-button>
      </div>
    </div>

    <!-- 配置统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <StatCard
        title="配置总数"
        :value="configStats.total"
        icon="mdi:file-cog"
        color="blue"
      />
      <StatCard
        title="活跃配置"
        :value="configStats.active"
        icon="mdi:check-circle"
        color="green"
      />
      <StatCard
        title="待同步"
        :value="configStats.pending"
        icon="mdi:clock-outline"
        color="orange"
      />
      <StatCard
        title="版本数"
        :value="configStats.versions"
        icon="mdi:source-branch"
        color="purple"
      />
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <n-input
          v-model:value="searchForm.keyword"
          placeholder="搜索配置名称"
          clearable
        >
          <template #prefix>
            <Icon icon="mdi:magnify" />
          </template>
        </n-input>

        <n-select
          v-model:value="searchForm.type"
          placeholder="配置类型"
          clearable
          :options="typeOptions"
        />

        <n-select
          v-model:value="searchForm.status"
          placeholder="状态筛选"
          clearable
          :options="statusOptions"
        />

        <n-select
          v-model:value="searchForm.client"
          placeholder="关联客户端"
          clearable
          :options="clientOptions"
        />

        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <Icon icon="mdi:magnify" />
          </template>
          搜索
        </n-button>
      </div>
    </div>

    <!-- 配置列表 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">配置列表</h3>
        <div class="flex items-center space-x-3">
          <n-button size="small" @click="batchPush" :disabled="!selectedConfigs.length">
            <template #icon>
              <Icon icon="mdi:upload" />
            </template>
            批量推送 ({{ selectedConfigs.length }})
          </n-button>
          <n-button size="small" @click="batchDownload" :disabled="!selectedConfigs.length">
            <template #icon>
              <Icon icon="mdi:download" />
            </template>
            批量下载 ({{ selectedConfigs.length }})
          </n-button>
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        v-model:checked-row-keys="selectedConfigs"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- 新建配置弹窗 -->
    <ConfigModal
      v-model:show="showCreateModal"
      :config-data="editingConfig"
      @success="handleModalSuccess"
    />

    <!-- 上传配置弹窗 -->
    <UploadModal
      v-model:show="showUploadModal"
      @success="handleUploadSuccess"
    />

    <!-- 版本历史弹窗 -->
    <VersionHistoryModal
      v-model:show="showVersionModal"
      :config-data="selectedConfig"
      @rollback="handleRollback"
    />

    <!-- 配置对比弹窗 -->
    <ConfigDiffModal
      v-model:show="showDiffModal"
      :config-data="selectedConfig"
      :version-data="selectedVersion"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'
import { formatTime, formatFileSize } from '@/utils/format'
import { configsApi } from '@/api/configs'
import StatCard from '@/components/StatCard.vue'
import ConfigModal from './components/ConfigModal.vue'
import UploadModal from './components/UploadModal.vue'
import VersionHistoryModal from './components/VersionHistoryModal.vue'
import ConfigDiffModal from './components/ConfigDiffModal.vue'

const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const showCreateModal = ref(false)
const showUploadModal = ref(false)
const showVersionModal = ref(false)
const showDiffModal = ref(false)
const editingConfig = ref(null)
const selectedConfig = ref(null)
const selectedVersion = ref(null)
const selectedConfigs = ref([])
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: null,
  status: null,
  client: null
})

// 配置统计
const configStats = reactive({
  total: 0,
  active: 0,
  pending: 0,
  versions: 0
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 配置类型选项
const typeOptions = [
  { label: '客户端配置', value: 'client' },
  { label: '服务端配置', value: 'server' },
  { label: '网络配置', value: 'network' },
  { label: '安全配置', value: 'security' }
]

// 状态选项
const statusOptions = [
  { label: '已同步', value: 'synced' },
  { label: '待同步', value: 'pending' },
  { label: '同步失败', value: 'failed' },
  { label: '已禁用', value: 'disabled' }
]

// 客户端选项
const clientOptions = [
  { label: 'client-001', value: 'client-001' },
  { label: 'client-002', value: 'client-002' },
  { label: 'client-003', value: 'client-003' }
]

// 表格列配置
const columns = [
  {
    type: 'selection'
  },
  {
    title: '配置名称',
    key: 'name',
    width: 200,
    render: (row) => h('div', { class: 'flex items-center space-x-2' }, [
      h(Icon, {
        icon: getConfigIcon(row.type),
        class: 'text-lg text-blue-600'
      }),
      h('span', { class: 'font-medium' }, row.name)
    ])
  },
  {
    title: '类型',
    key: 'type',
    width: 120,
    render: (row) => {
      const typeMap = {
        client: { type: 'info', label: '客户端' },
        server: { type: 'success', label: '服务端' },
        network: { type: 'warning', label: '网络' },
        security: { type: 'error', label: '安全' }
      }
      const typeInfo = typeMap[row.type] || { type: 'default', label: row.type }
      return h('n-tag', {
        type: typeInfo.type,
        size: 'small'
      }, typeInfo.label)
    }
  },
  {
    title: '版本',
    key: 'version',
    width: 100,
    render: (row) => h('span', { class: 'font-mono text-sm' }, `v${row.version}`)
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        synced: { type: 'success', label: '已同步' },
        pending: { type: 'warning', label: '待同步' },
        failed: { type: 'error', label: '同步失败' },
        disabled: { type: 'default', label: '已禁用' }
      }
      const statusInfo = statusMap[row.status] || { type: 'default', label: row.status }
      return h('n-tag', {
        type: statusInfo.type,
        size: 'small'
      }, statusInfo.label)
    }
  },
  {
    title: '关联客户端',
    key: 'clients',
    width: 150,
    render: (row) => {
      if (!row.clients || row.clients.length === 0) {
        return h('span', { class: 'text-gray-400' }, '-')
      }
      return h('div', { class: 'flex flex-wrap gap-1' },
        row.clients.slice(0, 2).map(client =>
          h('n-tag', { size: 'small' }, client)
        ).concat(
          row.clients.length > 2 ? [h('span', { class: 'text-xs text-gray-500' }, `+${row.clients.length - 2}`)] : []
        )
      )
    }
  },
  {
    title: '文件大小',
    key: 'size',
    width: 100,
    render: (row) => formatFileSize(row.size)
  },
  {
    title: '更新时间',
    key: 'updatedAt',
    width: 150,
    render: (row) => formatTime(row.updatedAt)
  },
  {
    title: '操作',
    key: 'actions',
    width: 250,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          onClick: () => viewConfig(row)
        }, '查看'),
        h('n-button', {
          size: 'small',
          type: 'primary',
          onClick: () => editConfig(row)
        }, '编辑'),
        h('n-button', {
          size: 'small',
          type: 'warning',
          onClick: () => pushConfig(row)
        }, '推送'),
        h('n-button', {
          size: 'small',
          onClick: () => viewVersions(row)
        }, '版本'),
        h('n-button', {
          size: 'small',
          onClick: () => downloadConfig(row)
        }, '下载'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => deleteConfig(row)
        }, '删除')
      ])
    }
  }
]

// 获取配置图标
const getConfigIcon = (type) => {
  const iconMap = {
    client: 'mdi:laptop',
    server: 'mdi:server',
    network: 'mdi:network',
    security: 'mdi:shield-check'
  }
  return iconMap[type] || 'mdi:file-cog'
}

// 加载配置列表
const loadConfigs = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    // 调用真实 API
    const response = await configsApi.getConfigs(params)
    console.log('配置列表响应:', response)

    if (response.code === 0 && response.data) {
      let configs = []
      if (response.data.items) {
        configs = response.data.items
        pagination.itemCount = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        configs = response.data
        pagination.itemCount = response.data.length
      }

      tableData.value = configs

      // 更新统计数据
      configStats.total = configs.length
      configStats.active = configs.filter(c => c.status === 'synced').length
      configStats.pending = configs.filter(c => c.status === 'pending').length
      configStats.versions = configs.reduce((acc, c) => acc + (c.versions?.length || 1), 0)
    } else {
      tableData.value = []
      pagination.itemCount = 0
      configStats.total = 0
      configStats.active = 0
      configStats.pending = 0
      configStats.versions = 0
    }

  } catch (error) {
    message.error('加载配置列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadConfigs()
}

// 刷新数据
const refreshData = () => {
  loadConfigs()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  loadConfigs()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadConfigs()
}

// 查看配置
const viewConfig = (config) => {
  selectedConfig.value = config
  message.info(`查看配置: ${config.name}`)
}

// 编辑配置
const editConfig = (config) => {
  editingConfig.value = config
  showCreateModal.value = true
}

// 推送配置
const pushConfig = async (config) => {
  try {
    await configsApi.pushConfig(config.id)
    config.status = 'synced'
    message.success('配置推送成功')
  } catch (error) {
    message.error('配置推送失败')
  }
}

// 查看版本历史
const viewVersions = (config) => {
  selectedConfig.value = config
  showVersionModal.value = true
}

// 下载配置
const downloadConfig = (config) => {
  // 创建下载链接
  const blob = new Blob([config.content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${config.name}.conf`
  a.click()
  URL.revokeObjectURL(url)
  message.success('配置下载成功')
}

// 删除配置
const deleteConfig = (config) => {
  dialog.error({
    title: '确认删除',
    content: `确定要删除配置 "${config.name}" 吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const index = tableData.value.findIndex(c => c.id === config.id)
        if (index > -1) {
          tableData.value.splice(index, 1)
          pagination.itemCount--
        }
        message.success('删除成功')
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 批量推送
const batchPush = async () => {
  try {
    for (const configId of selectedConfigs.value) {
      const config = tableData.value.find(c => c.id === configId)
      if (config) {
        config.status = 'synced'
      }
    }
    selectedConfigs.value = []
    message.success('批量推送成功')
  } catch (error) {
    message.error('批量推送失败')
  }
}

// 批量下载
const batchDownload = () => {
  message.info('批量下载功能开发中')
}

// 版本回滚
const handleRollback = (version) => {
  selectedVersion.value = version
  showDiffModal.value = true
}

// 弹窗成功回调
const handleModalSuccess = () => {
  showCreateModal.value = false
  editingConfig.value = null
  loadConfigs()
}

const handleUploadSuccess = () => {
  showUploadModal.value = false
  loadConfigs()
}

onMounted(() => {
  loadConfigs()
})
</script>
