<template>
  <n-modal v-model:show="showModal" preset="dialog" title="版本历史" style="width: 800px">
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h4 class="font-medium">{{ configData?.name }} 的版本历史</h4>
        <n-button size="small" @click="loadVersions">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
      </div>
      
      <n-data-table
        :columns="columns"
        :data="versions"
        :loading="loading"
        size="small"
        :pagination="{ pageSize: 10 }"
      />
    </div>
    
    <template #action>
      <div class="flex justify-end">
        <n-button @click="handleClose">关闭</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { formatTime } from '@/utils/format'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  configData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'rollback'])

const message = useMessage()
const loading = ref(false)
const versions = ref([])

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const columns = [
  {
    title: '版本',
    key: 'version',
    width: 100,
    render: (row) => h('span', { class: 'font-mono' }, `v${row.version}`)
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 150,
    render: (row) => formatTime(row.createdAt)
  },
  {
    title: '创建者',
    key: 'creator',
    width: 100
  },
  {
    title: '变更说明',
    key: 'changelog',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          onClick: () => viewDiff(row)
        }, '对比'),
        h('n-button', {
          size: 'small',
          type: 'warning',
          onClick: () => rollback(row),
          disabled: row.isCurrent
        }, row.isCurrent ? '当前版本' : '回滚')
      ])
    }
  }
]

const loadVersions = async () => {
  if (!props.configData) return
  
  loading.value = true
  try {
    // 模拟版本数据
    versions.value = [
      {
        id: 1,
        version: '1.2.0',
        createdAt: new Date(),
        creator: 'admin',
        changelog: '更新网络配置，优化连接性能',
        isCurrent: true
      },
      {
        id: 2,
        version: '1.1.0',
        createdAt: new Date(Date.now() - 86400000),
        creator: 'admin',
        changelog: '修复安全配置问题',
        isCurrent: false
      },
      {
        id: 3,
        version: '1.0.0',
        createdAt: new Date(Date.now() - 172800000),
        creator: 'admin',
        changelog: '初始版本',
        isCurrent: false
      }
    ]
  } catch (error) {
    message.error('加载版本历史失败')
  } finally {
    loading.value = false
  }
}

const viewDiff = (version) => {
  message.info(`查看版本 ${version.version} 的差异`)
}

const rollback = (version) => {
  emit('rollback', version)
  message.success(`回滚到版本 ${version.version}`)
}

const handleClose = () => {
  showModal.value = false
}

watch(() => props.show, (show) => {
  if (show) {
    loadVersions()
  }
})
</script>
