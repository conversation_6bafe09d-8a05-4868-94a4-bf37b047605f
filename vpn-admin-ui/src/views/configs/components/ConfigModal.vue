<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑配置' : '新建配置'" style="width: 800px">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="配置名称" path="name">
        <n-input v-model:value="formData.name" placeholder="请输入配置名称" />
      </n-form-item>
      
      <n-form-item label="配置类型" path="type">
        <n-select
          v-model:value="formData.type"
          placeholder="请选择配置类型"
          :options="typeOptions"
        />
      </n-form-item>
      
      <n-form-item label="关联客户端" path="clients">
        <n-select
          v-model:value="formData.clients"
          placeholder="请选择关联客户端"
          multiple
          :options="clientOptions"
        />
      </n-form-item>
      
      <n-form-item label="配置内容" path="content">
        <n-input
          v-model:value="formData.content"
          type="textarea"
          placeholder="请输入配置内容"
          :rows="15"
          style="font-family: 'Courier New', monospace"
        />
      </n-form-item>
      
      <n-form-item label="描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入配置描述"
          :rows="3"
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-between">
        <div class="flex items-center space-x-3">
          <n-button @click="validateConfig" :loading="validating">
            <template #icon>
              <Icon icon="mdi:check-circle" />
            </template>
            验证配置
          </n-button>
          <n-button @click="useTemplate">
            <template #icon>
              <Icon icon="mdi:file-document" />
            </template>
            使用模板
          </n-button>
        </div>
        <div class="flex items-center space-x-3">
          <n-button @click="handleCancel">取消</n-button>
          <n-button type="primary" :loading="loading" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </n-button>
        </div>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { configsApi } from '@/api/configs'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  configData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)
const validating = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.configData)

// 表单数据
const formData = reactive({
  name: '',
  type: 'client',
  clients: [],
  content: '',
  description: ''
})

// 配置类型选项
const typeOptions = [
  { label: '客户端配置', value: 'client' },
  { label: '服务端配置', value: 'server' },
  { label: '网络配置', value: 'network' },
  { label: '安全配置', value: 'security' }
]

// 客户端选项
const clientOptions = [
  { label: 'client-001', value: 'client-001' },
  { label: 'client-002', value: 'client-002' },
  { label: 'client-003', value: 'client-003' },
  { label: 'client-004', value: 'client-004' },
  { label: 'client-005', value: 'client-005' }
]

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '配置名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入配置内容', trigger: 'blur' },
    { min: 10, message: '配置内容不能少于 10 个字符', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: 'client',
    clients: [],
    content: '',
    description: ''
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      name: data.name || '',
      type: data.type || 'client',
      clients: data.clients || [],
      content: data.content || '',
      description: data.description || ''
    })
  }
}

// 验证配置
const validateConfig = async () => {
  if (!formData.content.trim()) {
    message.warning('请先输入配置内容')
    return
  }
  
  validating.value = true
  try {
    await configsApi.validateConfig(formData.content, formData.type)
    message.success('配置验证通过')
  } catch (error) {
    message.error('配置验证失败: ' + (error.response?.data?.message || '格式错误'))
  } finally {
    validating.value = false
  }
}

// 使用模板
const useTemplate = () => {
  const templates = {
    client: `# 客户端配置模板
Name = client-001
ConnectTo = server-001
Port = 655
Device = /dev/net/tun
DeviceType = tun

# 网络配置
Subnet = ***********/24
Address = ************

# 安全配置
Cipher = aes-256-cbc
Digest = sha256
Compression = 9`,
    server: `# 服务端配置模板
Name = server-001
Port = 655
Device = /dev/net/tun
DeviceType = tun

# 网络配置
Subnet = ***********/24
Address = ***********

# 安全配置
Cipher = aes-256-cbc
Digest = sha256
Compression = 9

# 服务端特定配置
Mode = switch
Forwarding = kernel`,
    network: `# 网络配置模板
# 路由配置
Route = 10.0.0.0/8
Route = **********/12
Route = ***********/16

# DNS 配置
DNS = *******
DNS = *******

# MTU 配置
MTU = 1500`,
    security: `# 安全配置模板
# 加密算法
Cipher = aes-256-gcm
Digest = sha256

# 密钥交换
KeyExpire = 3600
PingInterval = 60
PingTimeout = 5

# 访问控制
StrictSubnets = yes
TunnelServer = yes`
  }
  
  formData.content = templates[formData.type] || templates.client
  message.success('模板应用成功')
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    if (isEdit.value) {
      await configsApi.updateConfig(props.configData.id, formData)
      message.success('更新成功')
    } else {
      await configsApi.createConfig(formData)
      message.success('创建成功')
    }
    
    emit('success')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    if (props.configData) {
      fillFormData(props.configData)
    } else {
      resetForm()
    }
  }
})
</script>
