<template>
  <n-modal v-model:show="showModal" preset="dialog" title="上传配置">
    <div class="space-y-4">
      <n-upload
        ref="uploadRef"
        :file-list="fileList"
        :max="1"
        accept=".conf,.txt,.cfg"
        @change="handleFileChange"
        @remove="handleFileRemove"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px">
            <Icon icon="mdi:cloud-upload" style="font-size: 48px; color: #0e7a0d" />
          </div>
          <n-text style="font-size: 16px">
            点击或者拖动文件到该区域来上传
          </n-text>
          <n-p depth="3" style="margin: 8px 0 0 0">
            支持 .conf、.txt、.cfg 格式的配置文件
          </n-p>
        </n-upload-dragger>
      </n-upload>
      
      <n-form v-if="fileList.length > 0" :model="formData" label-placement="left">
        <n-form-item label="配置名称">
          <n-input v-model:value="formData.name" placeholder="请输入配置名称" />
        </n-form-item>
        <n-form-item label="配置类型">
          <n-select v-model:value="formData.type" :options="typeOptions" />
        </n-form-item>
        <n-form-item label="描述">
          <n-input v-model:value="formData.description" type="textarea" :rows="3" />
        </n-form-item>
      </n-form>
    </div>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" :disabled="!fileList.length" @click="handleUpload">
          上传
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const loading = ref(false)
const fileList = ref([])

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const formData = reactive({
  name: '',
  type: 'client',
  description: ''
})

const typeOptions = [
  { label: '客户端配置', value: 'client' },
  { label: '服务端配置', value: 'server' },
  { label: '网络配置', value: 'network' },
  { label: '安全配置', value: 'security' }
]

const handleFileChange = (options) => {
  fileList.value = options.fileList
  if (options.file && options.file.name) {
    formData.name = options.file.name.replace(/\.[^/.]+$/, '')
  }
}

const handleFileRemove = () => {
  fileList.value = []
  formData.name = ''
}

const handleUpload = async () => {
  if (!fileList.value.length) {
    message.warning('请选择要上传的文件')
    return
  }
  
  loading.value = true
  try {
    // 模拟上传
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('上传成功')
    emit('success')
  } catch (error) {
    message.error('上传失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  showModal.value = false
  fileList.value = []
  Object.assign(formData, { name: '', type: 'client', description: '' })
}
</script>
