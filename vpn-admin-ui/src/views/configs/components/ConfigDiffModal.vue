<template>
  <n-modal v-model:show="showModal" preset="dialog" title="配置对比" style="width: 1000px">
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h4 class="font-medium">配置差异对比</h4>
        <div class="flex items-center space-x-2">
          <n-tag type="error">当前版本</n-tag>
          <span>vs</span>
          <n-tag type="warning">{{ versionData?.version }}</n-tag>
        </div>
      </div>
      
      <div class="grid grid-cols-2 gap-4 h-96">
        <div>
          <h5 class="text-sm font-medium mb-2 text-red-600">当前版本</h5>
          <div class="h-full border rounded bg-red-50 dark:bg-red-900/20 p-3 overflow-auto">
            <pre class="text-xs font-mono">{{ currentContent }}</pre>
          </div>
        </div>
        <div>
          <h5 class="text-sm font-medium mb-2 text-orange-600">版本 {{ versionData?.version }}</h5>
          <div class="h-full border rounded bg-orange-50 dark:bg-orange-900/20 p-3 overflow-auto">
            <pre class="text-xs font-mono">{{ versionContent }}</pre>
          </div>
        </div>
      </div>
      
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded p-3">
        <h5 class="text-sm font-medium mb-2 text-blue-600">变更摘要</h5>
        <ul class="text-sm space-y-1">
          <li class="text-green-600">+ 新增 3 行配置</li>
          <li class="text-red-600">- 删除 1 行配置</li>
          <li class="text-orange-600">~ 修改 2 行配置</li>
        </ul>
      </div>
    </div>
    
    <template #action>
      <div class="flex justify-between">
        <n-button type="warning" @click="confirmRollback">
          <template #icon>
            <Icon icon="mdi:backup-restore" />
          </template>
          确认回滚到此版本
        </n-button>
        <n-button @click="handleClose">关闭</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  configData: {
    type: Object,
    default: null
  },
  versionData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show'])

const message = useMessage()
const dialog = useDialog()

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const currentContent = computed(() => {
  return props.configData?.content || '# 当前配置内容\nName = client-001\nPort = 655\nAddress = ************'
})

const versionContent = computed(() => {
  return `# 历史版本配置内容
Name = client-001
Port = 656
Address = ************
# 新增的配置项
Compression = 9`
})

const confirmRollback = () => {
  dialog.warning({
    title: '确认回滚',
    content: `确定要回滚到版本 ${props.versionData?.version} 吗？当前配置将被覆盖。`,
    positiveText: '确认回滚',
    negativeText: '取消',
    onPositiveClick: () => {
      message.success('配置回滚成功')
      showModal.value = false
    }
  })
}

const handleClose = () => {
  showModal.value = false
}
</script>
