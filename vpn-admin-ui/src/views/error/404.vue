<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="text-center">
      <div class="mb-8">
        <Icon icon="mdi:file-question" class="text-8xl text-gray-400 mx-auto" />
      </div>
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
      <h2 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-4">页面不存在</h2>
      <p class="text-gray-500 dark:text-gray-400 mb-8">抱歉，您访问的页面不存在或已被删除</p>
      <div class="space-x-4">
        <n-button @click="goBack">返回上页</n-button>
        <n-button type="primary" @click="goHome">回到首页</n-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/dashboard')
}
</script>
