<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="text-center">
      <div class="mb-8">
        <Icon icon="mdi:shield-lock" class="text-8xl text-red-500 mx-auto" />
      </div>
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">403</h1>
      <h2 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-4">无权限访问</h2>
      <p class="text-gray-500 dark:text-gray-400 mb-8">抱歉，您没有权限访问此页面</p>
      <div class="space-x-4">
        <n-button @click="goBack">返回上页</n-button>
        <n-button type="primary" @click="goHome">回到首页</n-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/dashboard')
}
</script>
