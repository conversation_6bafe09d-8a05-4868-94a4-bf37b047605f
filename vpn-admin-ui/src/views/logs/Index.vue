<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">日志中心</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="refreshLogs" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button @click="clearLogs">
          <template #icon>
            <Icon icon="mdi:delete-sweep" />
          </template>
          清空日志
        </n-button>
        <n-button type="primary" @click="exportLogs">
          <template #icon>
            <Icon icon="mdi:download" />
          </template>
          导出日志
        </n-button>
      </div>
    </div>

    <!-- 日志统计 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
      <StatCard
        title="总日志数"
        :value="logStats.total"
        icon="mdi:file-document"
        color="blue"
      />
      <StatCard
        title="错误日志"
        :value="logStats.error"
        icon="mdi:alert-circle"
        color="red"
      />
      <StatCard
        title="警告日志"
        :value="logStats.warning"
        icon="mdi:alert"
        color="orange"
      />
      <StatCard
        title="信息日志"
        :value="logStats.info"
        icon="mdi:information"
        color="green"
      />
      <StatCard
        title="调试日志"
        :value="logStats.debug"
        icon="mdi:bug"
        color="purple"
      />
    </div>

    <!-- 日志级别分布图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="card">
        <h3 class="text-lg font-semibold mb-4">日志级别分布</h3>
        <div class="h-64">
          <LogLevelChart :data="levelChartData" />
        </div>
      </div>

      <div class="card">
        <h3 class="text-lg font-semibold mb-4">日志趋势 (24小时)</h3>
        <div class="h-64">
          <LogTrendChart :data="trendChartData" />
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
        <n-input
          v-model:value="searchForm.keyword"
          placeholder="搜索日志内容"
          clearable
        >
          <template #prefix>
            <Icon icon="mdi:magnify" />
          </template>
        </n-input>

        <n-select
          v-model:value="searchForm.level"
          placeholder="日志级别"
          clearable
          :options="levelOptions"
        />

        <n-select
          v-model:value="searchForm.source"
          placeholder="日志来源"
          clearable
          :options="sourceOptions"
        />

        <n-date-picker
          v-model:value="searchForm.startTime"
          type="datetime"
          placeholder="开始时间"
          clearable
        />

        <n-date-picker
          v-model:value="searchForm.endTime"
          type="datetime"
          placeholder="结束时间"
          clearable
        />

        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <Icon icon="mdi:magnify" />
          </template>
          搜索
        </n-button>
      </div>
    </div>

    <!-- 实时日志开关 -->
    <div class="card">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <h3 class="text-lg font-semibold">日志列表</h3>
          <n-switch v-model:value="realTimeEnabled" @update:value="toggleRealTime">
            <template #checked>实时刷新</template>
            <template #unchecked>手动刷新</template>
          </n-switch>
        </div>

        <div class="flex items-center space-x-3">
          <span class="text-sm text-gray-500">显示最近 {{ pagination.pageSize }} 条</span>
          <n-button size="small" @click="scrollToBottom">
            <template #icon>
              <Icon icon="mdi:arrow-down" />
            </template>
            滚动到底部
          </n-button>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="card">
      <div ref="logContainer" class="max-h-96 overflow-y-auto">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :loading="loading"
          :pagination="pagination"
          size="small"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 日志详情弹窗 -->
    <LogDetailModal
      v-model:show="showDetailModal"
      :log-data="selectedLog"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, h, nextTick } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'
import { formatTime } from '@/utils/format'
import { logsApi } from '@/api/logs'
import StatCard from '@/components/StatCard.vue'
import LogLevelChart from '@/components/charts/LogLevelChart.vue'
import LogTrendChart from '@/components/charts/LogTrendChart.vue'
import LogDetailModal from './components/LogDetailModal.vue'

const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const realTimeEnabled = ref(false)
const showDetailModal = ref(false)
const selectedLog = ref(null)
const tableData = ref([])
const logContainer = ref(null)
const tableRef = ref(null)
let realTimeTimer = null

// 搜索表单
const searchForm = reactive({
  keyword: '',
  level: null,
  source: null,
  startTime: null,
  endTime: null
})

// 日志统计
const logStats = reactive({
  total: 0,
  error: 0,
  warning: 0,
  info: 0,
  debug: 0
})

// 图表数据
const levelChartData = ref([])
const trendChartData = ref([])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 50,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [20, 50, 100, 200]
})

// 日志级别选项
const levelOptions = [
  { label: '错误', value: 'error' },
  { label: '警告', value: 'warning' },
  { label: '信息', value: 'info' },
  { label: '调试', value: 'debug' }
]

// 日志来源选项
const sourceOptions = [
  { label: '服务端', value: 'server' },
  { label: '客户端', value: 'client' },
  { label: '系统', value: 'system' },
  { label: '网络', value: 'network' },
  { label: '认证', value: 'auth' }
]

// 表格列配置
const columns = [
  {
    title: '时间',
    key: 'timestamp',
    width: 150,
    render: (row) => formatTime(row.timestamp)
  },
  {
    title: '级别',
    key: 'level',
    width: 80,
    render: (row) => {
      const levelMap = {
        error: { type: 'error', label: '错误' },
        warning: { type: 'warning', label: '警告' },
        info: { type: 'info', label: '信息' },
        debug: { type: 'default', label: '调试' }
      }
      const levelInfo = levelMap[row.level] || { type: 'default', label: row.level }
      return h('n-tag', {
        type: levelInfo.type,
        size: 'small'
      }, levelInfo.label)
    }
  },
  {
    title: '来源',
    key: 'source',
    width: 100,
    render: (row) => {
      const sourceMap = {
        server: '服务端',
        client: '客户端',
        system: '系统',
        network: '网络',
        auth: '认证'
      }
      return sourceMap[row.source] || row.source
    }
  },
  {
    title: '模块',
    key: 'module',
    width: 120
  },
  {
    title: '消息',
    key: 'message',
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      const maxLength = 100
      const message = row.message || ''
      if (message.length > maxLength) {
        return h('span', {
          class: 'cursor-pointer hover:text-blue-600',
          onClick: () => viewLogDetail(row)
        }, message.substring(0, maxLength) + '...')
      }
      return message
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    render: (row) => {
      return h('n-button', {
        size: 'small',
        onClick: () => viewLogDetail(row)
      }, '详情')
    }
  }
]

// 生成模拟日志数据
const generateMockLogs = (count = 50) => {
  const levels = ['error', 'warning', 'info', 'debug']
  const sources = ['server', 'client', 'system', 'network', 'auth']
  const modules = ['connection', 'authentication', 'routing', 'encryption', 'config']

  const messages = {
    error: [
      '连接建立失败，目标主机不可达',
      '认证失败，用户名或密码错误',
      '配置文件解析错误',
      '网络接口初始化失败',
      '内存分配失败'
    ],
    warning: [
      '连接超时，正在重试',
      '证书即将过期',
      '磁盘空间不足',
      'CPU 使用率过高',
      '网络延迟较高'
    ],
    info: [
      '用户登录成功',
      '配置更新完成',
      '连接建立成功',
      '服务启动完成',
      '数据同步完成'
    ],
    debug: [
      '处理数据包',
      '执行路由查找',
      '更新连接状态',
      '清理过期连接',
      '刷新配置缓存'
    ]
  }

  const logs = []
  for (let i = 0; i < count; i++) {
    const level = levels[Math.floor(Math.random() * levels.length)]
    const source = sources[Math.floor(Math.random() * sources.length)]
    const module = modules[Math.floor(Math.random() * modules.length)]
    const messageList = messages[level]
    const message = messageList[Math.floor(Math.random() * messageList.length)]

    logs.push({
      id: Date.now() + i,
      timestamp: new Date(Date.now() - Math.random() * 24 * 3600000),
      level,
      source,
      module,
      message,
      details: `详细信息: ${message}\n\n堆栈跟踪:\n  at module.${module}()\n  at process.main()\n  at startup()`
    })
  }

  return logs.sort((a, b) => b.timestamp - a.timestamp)
}

// 加载日志数据
const loadLogs = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    const response = await logsApi.getLogs(params)
    console.log('日志列表响应:', response)

    if (response.code === 0 && response.data) {
      let logs = []
      if (response.data.items) {
        logs = response.data.items
        pagination.itemCount = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        logs = response.data
        pagination.itemCount = response.data.length
      }

      tableData.value = logs

      // 更新统计数据
      logStats.total = logs.length
      logStats.error = logs.filter(log => log.level === 'error').length
      logStats.warning = logs.filter(log => log.level === 'warning').length
      logStats.info = logs.filter(log => log.level === 'info').length
      logStats.debug = logs.filter(log => log.level === 'debug').length

      // 更新图表数据
      levelChartData.value = [
        { name: '错误', value: logStats.error },
        { name: '警告', value: logStats.warning },
        { name: '信息', value: logStats.info },
        { name: '调试', value: logStats.debug }
      ]

      // 获取日志趋势数据
      const trendResponse = await logsApi.getLogTrends()
      if (trendResponse.code === 0 && trendResponse.data) {
        trendChartData.value = trendResponse.data
      } else {
        // 使用默认趋势数据
        trendChartData.value = Array.from({ length: 24 }, (_, i) => ({
          time: `${String(i).padStart(2, '0')}:00`,
          count: 0
        }))
      }
    } else {
      tableData.value = []
      pagination.itemCount = 0
      Object.assign(logStats, { total: 0, error: 0, warning: 0, info: 0, debug: 0 })
      levelChartData.value = []
      trendChartData.value = []
    }

  } catch (error) {
    console.error('加载日志失败:', error)
    message.error('加载日志失败')
    // 使用默认值
    tableData.value = []
    pagination.itemCount = 0
    Object.assign(logStats, { total: 0, error: 0, warning: 0, info: 0, debug: 0 })
    levelChartData.value = []
    trendChartData.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadLogs()
}

// 刷新日志
const refreshLogs = () => {
  loadLogs()
}

// 清空日志
const clearLogs = () => {
  dialog.warning({
    title: '确认清空',
    content: '确定要清空所有日志吗？此操作不可恢复。',
    positiveText: '确定清空',
    negativeText: '取消',
    onPositiveClick: () => {
      tableData.value = []
      pagination.itemCount = 0
      Object.assign(logStats, { total: 0, error: 0, warning: 0, info: 0, debug: 0 })
      levelChartData.value = []
      message.success('日志已清空')
    }
  })
}

// 导出日志
const exportLogs = () => {
  const csvContent = [
    ['时间', '级别', '来源', '模块', '消息'].join(','),
    ...tableData.value.map(log => [
      formatTime(log.timestamp),
      log.level,
      log.source,
      log.module,
      `"${log.message.replace(/"/g, '""')}"`
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `logs_${new Date().toISOString().split('T')[0]}.csv`
  link.click()

  message.success('日志导出成功')
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
}

// 查看日志详情
const viewLogDetail = (log) => {
  selectedLog.value = log
  showDetailModal.value = true
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

// 切换实时刷新
const toggleRealTime = (enabled) => {
  if (enabled) {
    realTimeTimer = setInterval(() => {
      // 模拟新日志
      const newLog = generateMockLogs(1)[0]
      tableData.value.unshift(newLog)

      // 保持最大数量
      if (tableData.value.length > 1000) {
        tableData.value = tableData.value.slice(0, 1000)
      }

      // 更新统计
      logStats.total = tableData.value.length
      logStats[newLog.level]++

      // 自动滚动到顶部显示新日志
      nextTick(() => {
        if (logContainer.value) {
          logContainer.value.scrollTop = 0
        }
      })
    }, 2000) // 每2秒添加一条新日志

    message.success('实时刷新已开启')
  } else {
    if (realTimeTimer) {
      clearInterval(realTimeTimer)
      realTimeTimer = null
    }
    message.info('实时刷新已关闭')
  }
}

onMounted(() => {
  loadLogs()
})

onUnmounted(() => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer)
  }
})
</script>
