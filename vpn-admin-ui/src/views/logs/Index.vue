<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">日志中心</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="exportLogs">
          <template #icon>
            <Icon icon="mdi:download" />
          </template>
          导出日志
        </n-button>
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
      </div>
    </div>
    
    <div class="card">
      <p class="text-center text-gray-500">日志中心页面开发中...</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'

const message = useMessage()
const loading = ref(false)

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const exportLogs = () => {
  message.info('导出功能开发中')
}
</script>
