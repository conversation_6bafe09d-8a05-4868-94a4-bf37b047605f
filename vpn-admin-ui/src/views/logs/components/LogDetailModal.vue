<template>
  <n-modal v-model:show="showModal" preset="dialog" title="日志详情" style="width: 800px">
    <div v-if="logData" class="space-y-4">
      <!-- 基本信息 -->
      <div class="grid grid-cols-2 gap-6">
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">时间</span>
            <span class="text-sm font-medium">{{ formatTime(logData.timestamp) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">级别</span>
            <n-tag :type="getLevelColor(logData.level)" size="small">
              {{ getLevelLabel(logData.level) }}
            </n-tag>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">来源</span>
            <span class="text-sm font-medium">{{ getSourceLabel(logData.source) }}</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">模块</span>
            <span class="text-sm font-medium">{{ logData.module }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">日志ID</span>
            <span class="text-sm font-mono">{{ logData.id }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">线程</span>
            <span class="text-sm font-mono">{{ logData.thread || 'main' }}</span>
          </div>
        </div>
      </div>

      <!-- 消息内容 -->
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">消息内容</h4>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <p class="text-sm whitespace-pre-wrap">{{ logData.message }}</p>
        </div>
      </div>

      <!-- 详细信息 -->
      <div v-if="logData.details">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">详细信息</h4>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 max-h-48 overflow-y-auto">
          <pre class="text-xs font-mono whitespace-pre-wrap">{{ logData.details }}</pre>
        </div>
      </div>

      <!-- 上下文信息 -->
      <div v-if="contextLogs.length > 0">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">上下文日志</h4>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 max-h-32 overflow-y-auto">
          <div v-for="log in contextLogs" :key="log.id" class="text-xs mb-1">
            <span class="text-gray-500">{{ formatTime(log.timestamp) }}</span>
            <span :class="getLevelTextColor(log.level)" class="ml-2">{{ log.level.toUpperCase() }}</span>
            <span class="ml-2">{{ log.message }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center space-x-3">
        <n-button @click="copyToClipboard">
          <template #icon>
            <Icon icon="mdi:content-copy" />
          </template>
          复制内容
        </n-button>
        <n-button type="primary" @click="downloadLog">
          <template #icon>
            <Icon icon="mdi:download" />
          </template>
          下载日志
        </n-button>
        <n-button type="warning" @click="reportIssue">
          <template #icon>
            <Icon icon="mdi:bug-report" />
          </template>
          报告问题
        </n-button>
      </div>
    </div>
    
    <template #action>
      <div class="flex justify-end">
        <n-button @click="handleClose">关闭</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { formatTime } from '@/utils/format'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  logData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show'])

const message = useMessage()

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 模拟上下文日志
const contextLogs = ref([
  {
    id: 1,
    timestamp: new Date(Date.now() - 5000),
    level: 'info',
    message: '开始处理请求'
  },
  {
    id: 2,
    timestamp: new Date(Date.now() - 3000),
    level: 'debug',
    message: '验证用户权限'
  },
  {
    id: 3,
    timestamp: new Date(Date.now() - 1000),
    level: 'warning',
    message: '检测到异常情况'
  }
])

// 获取级别颜色
const getLevelColor = (level) => {
  const colorMap = {
    error: 'error',
    warning: 'warning',
    info: 'info',
    debug: 'default'
  }
  return colorMap[level] || 'default'
}

// 获取级别标签
const getLevelLabel = (level) => {
  const labelMap = {
    error: '错误',
    warning: '警告',
    info: '信息',
    debug: '调试'
  }
  return labelMap[level] || level
}

// 获取来源标签
const getSourceLabel = (source) => {
  const sourceMap = {
    server: '服务端',
    client: '客户端',
    system: '系统',
    network: '网络',
    auth: '认证'
  }
  return sourceMap[source] || source
}

// 获取级别文本颜色
const getLevelTextColor = (level) => {
  const colorMap = {
    error: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-blue-600',
    debug: 'text-gray-600'
  }
  return colorMap[level] || 'text-gray-600'
}

// 复制到剪贴板
const copyToClipboard = async () => {
  if (!props.logData) return
  
  const content = `时间: ${formatTime(props.logData.timestamp)}
级别: ${getLevelLabel(props.logData.level)}
来源: ${getSourceLabel(props.logData.source)}
模块: ${props.logData.module}
消息: ${props.logData.message}
${props.logData.details ? `\n详细信息:\n${props.logData.details}` : ''}`

  try {
    await navigator.clipboard.writeText(content)
    message.success('内容已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 下载日志
const downloadLog = () => {
  if (!props.logData) return
  
  const content = `时间: ${formatTime(props.logData.timestamp)}
级别: ${getLevelLabel(props.logData.level)}
来源: ${getSourceLabel(props.logData.source)}
模块: ${props.logData.module}
消息: ${props.logData.message}
${props.logData.details ? `\n详细信息:\n${props.logData.details}` : ''}`

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `log_${props.logData.id}_${new Date().toISOString().split('T')[0]}.txt`
  link.click()
  
  message.success('日志下载成功')
}

// 报告问题
const reportIssue = () => {
  message.info('问题报告功能开发中')
}

// 关闭弹窗
const handleClose = () => {
  showModal.value = false
}
</script>
