<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">仪表盘</h1>
      <n-button @click="refreshData" :loading="loading">
        <template #icon>
          <Icon icon="mdi:refresh" />
        </template>
        刷新数据
      </n-button>
    </div>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="在线客户端"
        :value="stats.onlineClients"
        icon="mdi:laptop"
        color="blue"
        :trend="stats.clientsTrend"
      />
      <StatCard
        title="活跃连接"
        :value="stats.activeConnections"
        icon="mdi:connection"
        color="green"
        :trend="stats.connectionsTrend"
      />
      <StatCard
        title="打洞成功率"
        :value="`${stats.holePunchSuccessRate}%`"
        icon="mdi:network"
        color="purple"
        :trend="stats.successRateTrend"
      />
      <StatCard
        title="异常事件"
        :value="stats.errorEvents"
        icon="mdi:alert-circle"
        color="red"
        :trend="stats.errorTrend"
      />
    </div>
    
    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 连接趋势图 -->
      <div class="card">
        <h3 class="text-lg font-semibold mb-4">连接趋势</h3>
        <div class="h-64">
          <ConnectionTrendChart :data="trendData" />
        </div>
      </div>
      
      <!-- NAT 类型分布 -->
      <div class="card">
        <h3 class="text-lg font-semibold mb-4">NAT 类型分布</h3>
        <div class="h-64">
          <NatDistributionChart :data="natData" />
        </div>
      </div>
    </div>
    
    <!-- 最近事件 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">最近事件</h3>
        <router-link to="/logs">
          <n-button text type="primary">查看全部</n-button>
        </router-link>
      </div>
      
      <n-table :bordered="false" :single-line="false">
        <thead>
          <tr>
            <th>时间</th>
            <th>类型</th>
            <th>描述</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="event in recentEvents" :key="event.id">
            <td>{{ formatTime(event.timestamp) }}</td>
            <td>
              <n-tag :type="getEventTypeColor(event.type)" size="small">
                {{ event.type }}
              </n-tag>
            </td>
            <td>{{ event.description }}</td>
            <td>
              <n-tag :type="getEventStatusColor(event.status)" size="small">
                {{ event.status }}
              </n-tag>
            </td>
          </tr>
          <tr v-if="recentEvents.length === 0">
            <td colspan="4" class="text-center text-gray-400">暂无数据</td>
          </tr>
        </tbody>
      </n-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { formatTime } from '@/utils/format'
import StatCard from '@/components/StatCard.vue'
import ConnectionTrendChart from '@/components/charts/ConnectionTrendChart.vue'
import NatDistributionChart from '@/components/charts/NatDistributionChart.vue'

const message = useMessage()
const loading = ref(false)

// 统计数据
const stats = ref({
  onlineClients: 0,
  activeConnections: 0,
  holePunchSuccessRate: 0,
  errorEvents: 0,
  clientsTrend: 0,
  connectionsTrend: 0,
  successRateTrend: 0,
  errorTrend: 0
})

// 趋势数据
const trendData = ref([])

// NAT 分布数据
const natData = ref([])

// 最近事件
const recentEvents = ref([])

// 获取事件类型颜色
const getEventTypeColor = (type) => {
  const colorMap = {
    'connection': 'info',
    'disconnect': 'warning',
    'error': 'error',
    'config': 'success'
  }
  return colorMap[type] || 'default'
}

// 获取事件状态颜色
const getEventStatusColor = (status) => {
  const colorMap = {
    'success': 'success',
    'failed': 'error',
    'pending': 'warning'
  }
  return colorMap[status] || 'default'
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await loadDashboardData()
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  // 模拟数据，实际应该调用 API
  stats.value = {
    onlineClients: 24,
    activeConnections: 18,
    holePunchSuccessRate: 85,
    errorEvents: 3,
    clientsTrend: 12,
    connectionsTrend: 8,
    successRateTrend: 5,
    errorTrend: -2
  }
  
  trendData.value = [
    { time: '00:00', connections: 10 },
    { time: '04:00', connections: 8 },
    { time: '08:00', connections: 15 },
    { time: '12:00', connections: 20 },
    { time: '16:00', connections: 18 },
    { time: '20:00', connections: 22 }
  ]
  
  natData.value = [
    { name: '完全锥形', value: 45 },
    { name: '受限锥形', value: 25 },
    { name: '端口受限', value: 15 },
    { name: '对称型', value: 10 },
    { name: '未知', value: 5 }
  ]
  
  recentEvents.value = [
    {
      id: 1,
      timestamp: new Date(),
      type: 'connection',
      description: '客户端 client-001 连接成功',
      status: 'success'
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 300000),
      type: 'disconnect',
      description: '客户端 client-002 断开连接',
      status: 'success'
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 600000),
      type: 'error',
      description: 'NAT 打洞失败',
      status: 'failed'
    }
  ]
}

onMounted(() => {
  loadDashboardData()
})
</script>
