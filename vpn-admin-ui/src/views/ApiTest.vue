<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">API 测试页面</h1>
    
    <div class="space-y-4">
      <div class="card p-4">
        <h2 class="text-lg font-semibold mb-4">环境变量</h2>
        <pre class="bg-gray-100 p-3 rounded text-sm">{{ envInfo }}</pre>
      </div>
      
      <div class="card p-4">
        <h2 class="text-lg font-semibold mb-4">API 测试</h2>
        <div class="space-x-2 mb-4">
          <n-button @click="testGetSubnets" :loading="loading">获取子网列表</n-button>
          <n-button @click="testCreateSubnet" :loading="loading">创建测试子网</n-button>
          <n-button @click="clearResults">清空结果</n-button>
        </div>
        
        <div v-if="results.length > 0" class="space-y-2">
          <div 
            v-for="(result, index) in results" 
            :key="index"
            :class="[
              'p-3 rounded border',
              result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            ]"
          >
            <div class="font-medium">{{ result.title }}</div>
            <pre class="text-sm mt-2 whitespace-pre-wrap">{{ result.content }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { subnetsApi } from '@/api/subnets'

const message = useMessage()
const loading = ref(false)
const results = ref([])

const envInfo = computed(() => {
  return JSON.stringify({
    VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
    VITE_API_TIMEOUT: import.meta.env.VITE_API_TIMEOUT,
    MODE: import.meta.env.MODE,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD
  }, null, 2)
})

const addResult = (title, content, success = true) => {
  results.value.unshift({
    title,
    content: typeof content === 'object' ? JSON.stringify(content, null, 2) : content,
    success,
    timestamp: new Date().toLocaleTimeString()
  })
}

const testGetSubnets = async () => {
  loading.value = true
  try {
    console.log('开始测试获取子网列表...')
    const response = await subnetsApi.getSubnets()
    console.log('获取子网列表响应:', response)
    
    addResult('✅ 获取子网列表成功', {
      message: response.message,
      dataCount: response.data?.length || 0,
      data: response.data
    })
    
    message.success('获取子网列表成功')
  } catch (error) {
    console.error('获取子网列表失败:', error)
    addResult('❌ 获取子网列表失败', {
      message: error.message,
      response: error.response?.data,
      stack: error.stack
    }, false)
    
    message.error('获取子网列表失败')
  } finally {
    loading.value = false
  }
}

const testCreateSubnet = async () => {
  loading.value = true
  try {
    const testData = {
      name: 'api-test-subnet',
      cidr: '***********/24',
      port: 10088,
      mtu: 1500,
      identifier: 'api-test-subnet-001',
      description: 'API 测试页面创建的子网',
      status: 'active'
    }
    
    console.log('开始测试创建子网...', testData)
    const response = await subnetsApi.createSubnet(testData)
    console.log('创建子网响应:', response)
    
    addResult('✅ 创建子网成功', {
      message: response.message,
      data: response.data
    })
    
    message.success('创建子网成功')
  } catch (error) {
    console.error('创建子网失败:', error)
    addResult('❌ 创建子网失败', {
      message: error.message,
      response: error.response?.data,
      stack: error.stack
    }, false)
    
    message.error('创建子网失败')
  } finally {
    loading.value = false
  }
}

const clearResults = () => {
  results.value = []
}

onMounted(() => {
  console.log('API 测试页面已加载')
  console.log('环境变量:', import.meta.env)
})
</script>
