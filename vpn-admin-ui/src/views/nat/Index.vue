<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">NAT 管理</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="batchDetect">
          <template #icon>
            <Icon icon="mdi:radar" />
          </template>
          批量检测
        </n-button>
      </div>
    </div>

    <!-- NAT 统计 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
      <StatCard
        title="总客户端"
        :value="natStats.total"
        icon="mdi:laptop"
        color="blue"
      />
      <StatCard
        title="开放网络"
        :value="natStats.open"
        icon="mdi:earth"
        color="green"
      />
      <StatCard
        title="完全锥形"
        :value="natStats.fullCone"
        icon="mdi:cone"
        color="purple"
      />
      <StatCard
        title="对称型"
        :value="natStats.symmetric"
        icon="mdi:swap-horizontal"
        color="orange"
      />
      <StatCard
        title="未知类型"
        :value="natStats.unknown"
        icon="mdi:help-circle"
        color="red"
      />
    </div>

    <!-- NAT 类型分布图 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="card">
        <h3 class="text-lg font-semibold mb-4">NAT 类型分布</h3>
        <div class="h-64">
          <NatDistributionChart :data="distributionData" />
        </div>
      </div>

      <div class="card">
        <h3 class="text-lg font-semibold mb-4">打洞成功率趋势</h3>
        <div class="h-64">
          <HolePunchTrendChart :data="trendData" />
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <n-input
          v-model:value="searchForm.keyword"
          placeholder="搜索客户端名称"
          clearable
        >
          <template #prefix>
            <Icon icon="mdi:magnify" />
          </template>
        </n-input>

        <n-select
          v-model:value="searchForm.natType"
          placeholder="NAT 类型筛选"
          clearable
          :options="natTypeOptions"
        />

        <n-select
          v-model:value="searchForm.status"
          placeholder="检测状态"
          clearable
          :options="statusOptions"
        />

        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <Icon icon="mdi:magnify" />
          </template>
          搜索
        </n-button>
      </div>
    </div>

    <!-- NAT 检测列表 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">NAT 检测结果</h3>
        <div class="flex items-center space-x-3">
          <n-button size="small" @click="exportData">
            <template #icon>
              <Icon icon="mdi:download" />
            </template>
            导出
          </n-button>
          <n-button size="small" @click="batchDetectSelected" :disabled="!selectedClients.length">
            <template #icon>
              <Icon icon="mdi:radar" />
            </template>
            批量检测 ({{ selectedClients.length }})
          </n-button>
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        v-model:checked-row-keys="selectedClients"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- NAT 详情弹窗 -->
    <NatDetailModal
      v-model:show="showDetailModal"
      :client-data="selectedClient"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { formatTime, formatNatType } from '@/utils/format'
import StatCard from '@/components/StatCard.vue'
import NatDistributionChart from '@/components/charts/NatDistributionChart.vue'
import HolePunchTrendChart from '@/components/charts/HolePunchTrendChart.vue'
import NatDetailModal from './components/NatDetailModal.vue'

const message = useMessage()
const loading = ref(false)
const showDetailModal = ref(false)
const selectedClient = ref(null)
const selectedClients = ref([])
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  natType: null,
  status: null
})

// NAT 统计
const natStats = reactive({
  total: 0,
  open: 0,
  fullCone: 0,
  symmetric: 0,
  unknown: 0
})

// 图表数据
const distributionData = ref([])
const trendData = ref([])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// NAT 类型选项
const natTypeOptions = [
  { label: '开放网络', value: 'open' },
  { label: '完全锥形', value: 'full_cone' },
  { label: '受限锥形', value: 'restricted_cone' },
  { label: '端口受限锥形', value: 'port_restricted_cone' },
  { label: '对称型', value: 'symmetric' },
  { label: '未知', value: 'unknown' }
]

// 状态选项
const statusOptions = [
  { label: '检测成功', value: 'success' },
  { label: '检测中', value: 'detecting' },
  { label: '检测失败', value: 'failed' },
  { label: '未检测', value: 'pending' }
]

// 表格列配置
const columns = [
  {
    type: 'selection'
  },
  {
    title: '客户端名称',
    key: 'name',
    width: 150,
    render: (row) => h('div', { class: 'flex items-center space-x-2' }, [
      h('div', {
        class: `w-2 h-2 rounded-full ${getStatusColor(row.detectStatus)}`
      }),
      h('span', { class: 'font-medium' }, row.name)
    ])
  },
  {
    title: 'IP 地址',
    key: 'ip',
    width: 120
  },
  {
    title: 'NAT 类型',
    key: 'natType',
    width: 120,
    render: (row) => {
      const typeMap = {
        open: { type: 'success', label: '开放网络' },
        full_cone: { type: 'info', label: '完全锥形' },
        restricted_cone: { type: 'warning', label: '受限锥形' },
        port_restricted_cone: { type: 'warning', label: '端口受限' },
        symmetric: { type: 'error', label: '对称型' },
        unknown: { type: 'default', label: '未知' }
      }
      const typeInfo = typeMap[row.natType] || { type: 'default', label: row.natType }
      return h('n-tag', {
        type: typeInfo.type,
        size: 'small'
      }, typeInfo.label)
    }
  },
  {
    title: '检测状态',
    key: 'detectStatus',
    width: 100,
    render: (row) => {
      const statusMap = {
        success: { type: 'success', label: '成功' },
        detecting: { type: 'warning', label: '检测中' },
        failed: { type: 'error', label: '失败' },
        pending: { type: 'default', label: '待检测' }
      }
      const statusInfo = statusMap[row.detectStatus] || { type: 'default', label: row.detectStatus }
      return h('n-tag', {
        type: statusInfo.type,
        size: 'small'
      }, statusInfo.label)
    }
  },
  {
    title: '外部 IP',
    key: 'externalIp',
    width: 120,
    render: (row) => row.externalIp || '-'
  },
  {
    title: '外部端口',
    key: 'externalPort',
    width: 100,
    render: (row) => row.externalPort || '-'
  },
  {
    title: '打洞成功率',
    key: 'holePunchRate',
    width: 120,
    render: (row) => {
      const rate = row.holePunchRate || 0
      const color = rate >= 80 ? 'text-green-600' : rate >= 50 ? 'text-yellow-600' : 'text-red-600'
      return h('span', { class: color }, `${rate}%`)
    }
  },
  {
    title: '最后检测',
    key: 'lastDetected',
    width: 150,
    render: (row) => formatTime(row.lastDetected)
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          onClick: () => detectNat(row),
          loading: row.detecting
        }, '检测'),
        h('n-button', {
          size: 'small',
          type: 'primary',
          onClick: () => viewDetail(row)
        }, '详情'),
        h('n-button', {
          size: 'small',
          type: 'warning',
          onClick: () => testHolePunch(row)
        }, '测试打洞')
      ])
    }
  }
]

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    success: 'bg-green-500',
    detecting: 'bg-yellow-500',
    failed: 'bg-red-500',
    pending: 'bg-gray-500'
  }
  return colorMap[status] || 'bg-gray-500'
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟 NAT 检测数据
    const mockData = []
    const natTypes = ['open', 'full_cone', 'restricted_cone', 'port_restricted_cone', 'symmetric', 'unknown']
    const statuses = ['success', 'detecting', 'failed', 'pending']

    for (let i = 0; i < 25; i++) {
      const natType = natTypes[Math.floor(Math.random() * natTypes.length)]
      const status = statuses[Math.floor(Math.random() * statuses.length)]

      mockData.push({
        id: i + 1,
        name: `client-${String(i + 1).padStart(3, '0')}`,
        ip: `192.168.1.${i + 10}`,
        natType,
        detectStatus: status,
        externalIp: status === 'success' ? `203.0.113.${Math.floor(Math.random() * 255)}` : null,
        externalPort: status === 'success' ? Math.floor(Math.random() * 65535) + 1024 : null,
        holePunchRate: status === 'success' ? Math.floor(Math.random() * 100) : 0,
        lastDetected: status === 'success' ? new Date(Date.now() - Math.random() * 24 * 3600000) : null,
        detecting: false
      })
    }

    tableData.value = mockData
    pagination.itemCount = mockData.length

    // 更新统计数据
    natStats.total = mockData.length
    natStats.open = mockData.filter(d => d.natType === 'open').length
    natStats.fullCone = mockData.filter(d => d.natType === 'full_cone').length
    natStats.symmetric = mockData.filter(d => d.natType === 'symmetric').length
    natStats.unknown = mockData.filter(d => d.natType === 'unknown').length

    // 更新图表数据
    distributionData.value = [
      { name: '开放网络', value: natStats.open },
      { name: '完全锥形', value: natStats.fullCone },
      { name: '受限锥形', value: mockData.filter(d => d.natType === 'restricted_cone').length },
      { name: '端口受限', value: mockData.filter(d => d.natType === 'port_restricted_cone').length },
      { name: '对称型', value: natStats.symmetric },
      { name: '未知', value: natStats.unknown }
    ]

    // 打洞成功率趋势
    trendData.value = Array.from({ length: 24 }, (_, i) => ({
      time: `${String(i).padStart(2, '0')}:00`,
      rate: Math.floor(Math.random() * 30) + 60 // 60-90%
    }))

  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

// 检测 NAT
const detectNat = async (client) => {
  client.detecting = true
  client.detectStatus = 'detecting'

  try {
    // 模拟检测过程
    await new Promise(resolve => setTimeout(resolve, 3000))

    client.detectStatus = 'success'
    client.natType = ['open', 'full_cone', 'restricted_cone', 'symmetric'][Math.floor(Math.random() * 4)]
    client.externalIp = `203.0.113.${Math.floor(Math.random() * 255)}`
    client.externalPort = Math.floor(Math.random() * 65535) + 1024
    client.holePunchRate = Math.floor(Math.random() * 100)
    client.lastDetected = new Date()

    message.success(`${client.name} NAT 检测完成`)
  } catch (error) {
    client.detectStatus = 'failed'
    message.error(`${client.name} NAT 检测失败`)
  } finally {
    client.detecting = false
  }
}

// 批量检测
const batchDetect = async () => {
  const pendingClients = tableData.value.filter(c => c.detectStatus === 'pending')
  if (pendingClients.length === 0) {
    message.warning('没有待检测的客户端')
    return
  }

  message.info(`开始批量检测 ${pendingClients.length} 个客户端`)

  for (const client of pendingClients) {
    await detectNat(client)
    await new Promise(resolve => setTimeout(resolve, 500)) // 间隔500ms
  }

  message.success('批量检测完成')
}

// 批量检测选中的客户端
const batchDetectSelected = async () => {
  const clients = tableData.value.filter(c => selectedClients.value.includes(c.id))

  for (const client of clients) {
    await detectNat(client)
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  selectedClients.value = []
  message.success('批量检测完成')
}

// 查看详情
const viewDetail = (client) => {
  selectedClient.value = client
  showDetailModal.value = true
}

// 测试打洞
const testHolePunch = async (client) => {
  message.info(`开始测试 ${client.name} 的打洞功能`)
  // 模拟测试
  await new Promise(resolve => setTimeout(resolve, 2000))
  const success = Math.random() > 0.3
  if (success) {
    message.success(`${client.name} 打洞测试成功`)
  } else {
    message.error(`${client.name} 打洞测试失败`)
  }
}

// 导出数据
const exportData = () => {
  message.info('导出功能开发中')
}

onMounted(() => {
  loadData()
})
</script>
