<template>
  <n-modal v-model:show="showModal" preset="dialog" title="NAT 详情" style="width: 800px">
    <div v-if="clientData" class="space-y-6">
      <!-- 基本信息 -->
      <div class="grid grid-cols-2 gap-6">
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">基本信息</h4>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">客户端名称</span>
              <span class="text-sm font-medium">{{ clientData.name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">内部 IP</span>
              <span class="text-sm font-medium">{{ clientData.ip }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">外部 IP</span>
              <span class="text-sm font-medium">{{ clientData.externalIp || '-' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">外部端口</span>
              <span class="text-sm font-medium">{{ clientData.externalPort || '-' }}</span>
            </div>
          </div>
        </div>
        
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">NAT 信息</h4>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">NAT 类型</span>
              <n-tag :type="getNatTypeColor(clientData.natType)" size="small">
                {{ getNatTypeLabel(clientData.natType) }}
              </n-tag>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">检测状态</span>
              <n-tag :type="getStatusColor(clientData.detectStatus)" size="small">
                {{ getStatusLabel(clientData.detectStatus) }}
              </n-tag>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">打洞成功率</span>
              <span class="text-sm font-medium" :class="getSuccessRateColor(clientData.holePunchRate)">
                {{ clientData.holePunchRate || 0 }}%
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">最后检测</span>
              <span class="text-sm font-medium">{{ formatTime(clientData.lastDetected) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- NAT 检测历史 -->
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">检测历史</h4>
        <n-data-table
          :columns="historyColumns"
          :data="detectionHistory"
          size="small"
          :pagination="{ pageSize: 5 }"
        />
      </div>

      <!-- 网络拓扑 -->
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">网络拓扑</h4>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-2">
                <Icon icon="mdi:laptop" class="text-white text-xl" />
              </div>
              <div class="text-xs">{{ clientData.name }}</div>
              <div class="text-xs text-gray-500">{{ clientData.ip }}</div>
            </div>
            
            <div class="flex-1 flex items-center justify-center">
              <div class="flex items-center space-x-2">
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <Icon icon="mdi:router-wireless" class="text-gray-400" />
                <div class="w-8 h-0.5 bg-gray-300"></div>
              </div>
            </div>
            
            <div class="text-center">
              <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-2">
                <Icon icon="mdi:earth" class="text-white text-xl" />
              </div>
              <div class="text-xs">外部网络</div>
              <div class="text-xs text-gray-500">{{ clientData.externalIp || 'Unknown' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center space-x-3">
        <n-button @click="detectNat" :loading="detecting">
          <template #icon>
            <Icon icon="mdi:radar" />
          </template>
          重新检测
        </n-button>
        <n-button type="primary" @click="testConnection">
          <template #icon>
            <Icon icon="mdi:connection" />
          </template>
          测试连接
        </n-button>
        <n-button type="warning" @click="testHolePunch">
          <template #icon>
            <Icon icon="mdi:target" />
          </template>
          测试打洞
        </n-button>
      </div>
    </div>
    
    <template #action>
      <div class="flex justify-end">
        <n-button @click="handleClose">关闭</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { formatTime } from '@/utils/format'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  clientData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show'])

const message = useMessage()
const detecting = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 模拟检测历史数据
const detectionHistory = ref([
  {
    id: 1,
    time: new Date(Date.now() - 3600000),
    natType: 'full_cone',
    externalIp: '*************',
    externalPort: 12345,
    status: 'success'
  },
  {
    id: 2,
    time: new Date(Date.now() - 7200000),
    natType: 'symmetric',
    externalIp: '*************',
    externalPort: 12346,
    status: 'success'
  },
  {
    id: 3,
    time: new Date(Date.now() - 10800000),
    natType: 'unknown',
    externalIp: null,
    externalPort: null,
    status: 'failed'
  }
])

// 历史记录表格列
const historyColumns = [
  {
    title: '检测时间',
    key: 'time',
    render: (row) => formatTime(row.time)
  },
  {
    title: 'NAT 类型',
    key: 'natType',
    render: (row) => h('n-tag', {
      type: getNatTypeColor(row.natType),
      size: 'small'
    }, getNatTypeLabel(row.natType))
  },
  {
    title: '外部 IP',
    key: 'externalIp',
    render: (row) => row.externalIp || '-'
  },
  {
    title: '外部端口',
    key: 'externalPort',
    render: (row) => row.externalPort || '-'
  },
  {
    title: '状态',
    key: 'status',
    render: (row) => h('n-tag', {
      type: getStatusColor(row.status),
      size: 'small'
    }, getStatusLabel(row.status))
  }
]

// 获取 NAT 类型颜色
const getNatTypeColor = (type) => {
  const colorMap = {
    open: 'success',
    full_cone: 'info',
    restricted_cone: 'warning',
    port_restricted_cone: 'warning',
    symmetric: 'error',
    unknown: 'default'
  }
  return colorMap[type] || 'default'
}

// 获取 NAT 类型标签
const getNatTypeLabel = (type) => {
  const labelMap = {
    open: '开放网络',
    full_cone: '完全锥形',
    restricted_cone: '受限锥形',
    port_restricted_cone: '端口受限',
    symmetric: '对称型',
    unknown: '未知'
  }
  return labelMap[type] || type
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    success: 'success',
    detecting: 'warning',
    failed: 'error',
    pending: 'default'
  }
  return colorMap[status] || 'default'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const labelMap = {
    success: '成功',
    detecting: '检测中',
    failed: '失败',
    pending: '待检测'
  }
  return labelMap[status] || status
}

// 获取成功率颜色
const getSuccessRateColor = (rate) => {
  if (rate >= 80) return 'text-green-600'
  if (rate >= 50) return 'text-yellow-600'
  return 'text-red-600'
}

// 重新检测 NAT
const detectNat = async () => {
  detecting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('NAT 检测完成')
  } catch (error) {
    message.error('NAT 检测失败')
  } finally {
    detecting.value = false
  }
}

// 测试连接
const testConnection = async () => {
  message.info('开始测试连接...')
  await new Promise(resolve => setTimeout(resolve, 1500))
  message.success('连接测试成功')
}

// 测试打洞
const testHolePunch = async () => {
  message.info('开始测试打洞...')
  await new Promise(resolve => setTimeout(resolve, 2000))
  const success = Math.random() > 0.3
  if (success) {
    message.success('打洞测试成功')
  } else {
    message.error('打洞测试失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  showModal.value = false
}
</script>
