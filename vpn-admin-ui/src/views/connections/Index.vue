<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">连接监控</h1>
      <n-button @click="refreshData" :loading="loading">
        <template #icon>
          <Icon icon="mdi:refresh" />
        </template>
        刷新
      </n-button>
    </div>
    
    <div class="card">
      <p class="text-center text-gray-500">连接监控页面开发中...</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Icon } from '@iconify/vue'

const loading = ref(false)

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}
</script>
