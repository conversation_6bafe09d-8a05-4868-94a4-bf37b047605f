<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">连接监控</h1>
      <div class="flex items-center space-x-3">
        <n-switch v-model:value="autoRefresh" @update:value="toggleAutoRefresh">
          <template #checked>自动刷新</template>
          <template #unchecked>手动刷新</template>
        </n-switch>
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button @click="exportData">
          <template #icon>
            <Icon icon="mdi:download" />
          </template>
          导出
        </n-button>
      </div>
    </div>

    <!-- 实时统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="活跃连接"
        :value="stats.activeConnections"
        icon="mdi:connection"
        color="blue"
        :trend="stats.connectionsTrend"
      />
      <StatCard
        title="总流量"
        :value="stats.totalTraffic"
        icon="mdi:chart-line"
        color="green"
        :trend="stats.trafficTrend"
      />
      <StatCard
        title="平均延迟"
        :value="stats.avgLatency"
        icon="mdi:speedometer"
        color="purple"
        :trend="stats.latencyTrend"
      />
      <StatCard
        title="异常连接"
        :value="stats.errorConnections"
        icon="mdi:alert-circle"
        color="red"
        :trend="stats.errorTrend"
      />
    </div>

    <!-- 实时图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 连接数趋势 -->
      <div class="card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">连接数趋势</h3>
          <n-select
            v-model:value="timeRange"
            :options="timeRangeOptions"
            size="small"
            style="width: 120px"
            @update:value="updateChartData"
          />
        </div>
        <div class="h-64">
          <ConnectionTrendChart :data="trendData" />
        </div>
      </div>

      <!-- 流量分布 -->
      <div class="card">
        <h3 class="text-lg font-semibold mb-4">流量分布</h3>
        <div class="h-64">
          <TrafficDistributionChart :data="trafficData" />
        </div>
      </div>
    </div>

    <!-- 延迟热力图 -->
    <div class="card">
      <h3 class="text-lg font-semibold mb-4">延迟热力图</h3>
      <div class="h-80">
        <LatencyHeatmapChart :data="latencyData" />
      </div>
    </div>

    <!-- 连接列表 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">实时连接</h3>
        <div class="flex items-center space-x-3">
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索连接..."
            size="small"
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <Icon icon="mdi:magnify" />
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            size="small"
            style="width: 120px"
            clearable
            :options="statusOptions"
          />
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="filteredConnections"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        size="small"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { formatTime, formatFileSize, formatLatency, formatStatus } from '@/utils/format'
import { connectionsApi } from '@/api/connections'
import StatCard from '@/components/StatCard.vue'
import ConnectionTrendChart from '@/components/charts/ConnectionTrendChart.vue'
import TrafficDistributionChart from '@/components/charts/TrafficDistributionChart.vue'
import LatencyHeatmapChart from '@/components/charts/LatencyHeatmapChart.vue'

const message = useMessage()
const loading = ref(false)
const autoRefresh = ref(true)
const searchKeyword = ref('')
const statusFilter = ref(null)
const timeRange = ref('1h')

let refreshTimer = null

// 统计数据
const stats = reactive({
  activeConnections: 0,
  totalTraffic: '0 GB',
  avgLatency: '0ms',
  errorConnections: 0,
  connectionsTrend: 0,
  trafficTrend: 0,
  latencyTrend: 0,
  errorTrend: 0
})

// 连接数据
const connections = ref([])
const trendData = ref([])
const trafficData = ref([])
const latencyData = ref([])

// 时间范围选项
const timeRangeOptions = [
  { label: '1小时', value: '1h' },
  { label: '6小时', value: '6h' },
  { label: '24小时', value: '24h' },
  { label: '7天', value: '7d' }
]

// 状态选项
const statusOptions = [
  { label: '已连接', value: 'connected' },
  { label: '连接中', value: 'connecting' },
  { label: '已断开', value: 'disconnected' },
  { label: '错误', value: 'error' }
]

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 表格列配置
const columns = [
  {
    title: '源客户端',
    key: 'sourceClient',
    width: 150,
    render: (row) => h('div', { class: 'flex items-center space-x-2' }, [
      h('div', {
        class: `w-2 h-2 rounded-full ${getStatusColor(row.status)}`
      }),
      h('span', row.sourceClient)
    ])
  },
  {
    title: '目标客户端',
    key: 'targetClient',
    width: 150
  },
  {
    title: '连接时间',
    key: 'connectTime',
    width: 150,
    render: (row) => formatTime(row.connectTime)
  },
  {
    title: '持续时间',
    key: 'duration',
    width: 100,
    render: (row) => formatDuration(row.duration)
  },
  {
    title: '上传流量',
    key: 'uploadBytes',
    width: 100,
    render: (row) => formatFileSize(row.uploadBytes)
  },
  {
    title: '下载流量',
    key: 'downloadBytes',
    width: 100,
    render: (row) => formatFileSize(row.downloadBytes)
  },
  {
    title: '延迟',
    key: 'latency',
    width: 80,
    render: (row) => formatLatency(row.latency)
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        connected: 'success',
        connecting: 'warning',
        disconnected: 'default',
        error: 'error'
      }
      return h('n-tag', {
        type: statusMap[row.status] || 'default',
        size: 'small'
      }, formatStatus(row.status))
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          onClick: () => viewDetails(row.id)
        }, '详情'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => terminateConnection(row.id),
          disabled: row.status !== 'connected'
        }, '断开')
      ])
    }
  }
]

// 过滤后的连接数据
const filteredConnections = computed(() => {
  let filtered = connections.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(conn =>
      conn.sourceClient.toLowerCase().includes(keyword) ||
      conn.targetClient.toLowerCase().includes(keyword)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(conn => conn.status === statusFilter.value)
  }

  pagination.itemCount = filtered.length
  return filtered
})

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    connected: 'bg-green-500',
    connecting: 'bg-yellow-500',
    disconnected: 'bg-gray-500',
    error: 'bg-red-500'
  }
  return colorMap[status] || 'bg-gray-500'
}

// 格式化持续时间
const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadConnections(),
      updateChartData()
    ])
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await connectionsApi.getStats()
    if (response.code === 0 && response.data) {
      const data = response.data
      stats.activeConnections = data.activeConnections || 0
      stats.totalTraffic = data.totalTraffic || '0 GB'
      stats.avgLatency = data.avgLatency || '0ms'
      stats.errorConnections = data.errorConnections || 0
      stats.connectionsTrend = data.connectionsTrend || 0
      stats.trafficTrend = data.trafficTrend || 0
      stats.latencyTrend = data.latencyTrend || 0
      stats.errorTrend = data.errorTrend || 0
    }
  } catch (error) {
    console.error('加载连接统计数据失败:', error)
    // 如果是 404 错误，说明后端还没实现这个 API，静默处理
    if (error.response && error.response.status !== 404) {
      console.warn('连接统计 API 调用失败，但不是 404 错误')
    }
    // 使用默认值
    stats.activeConnections = 0
    stats.totalTraffic = '0 GB'
    stats.avgLatency = '0ms'
    stats.errorConnections = 0
    stats.connectionsTrend = 0
    stats.trafficTrend = 0
    stats.latencyTrend = 0
    stats.errorTrend = 0
  }
}

// 加载连接数据
const loadConnections = async () => {
  try {
    const response = await connectionsApi.getConnections({
      page: pagination.page,
      pageSize: pagination.pageSize,
      status: statusFilter.value,
      keyword: searchKeyword.value
    })

    console.log('连接列表响应:', response)

    if (response.code === 0 && response.data) {
      if (response.data.items) {
        connections.value = response.data.items
        pagination.itemCount = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        connections.value = response.data
        pagination.itemCount = response.data.length
      } else {
        connections.value = []
        pagination.itemCount = 0
      }
    } else {
      connections.value = []
      pagination.itemCount = 0
    }
  } catch (error) {
    console.error('加载连接数据失败:', error)
    // 如果是 404 错误，说明后端还没实现这个 API，静默处理
    if (error.response && error.response.status !== 404) {
      console.warn('连接列表 API 调用失败，但不是 404 错误')
    }
    connections.value = []
    pagination.itemCount = 0
  }
}

// 更新图表数据
const updateChartData = async () => {
  // 连接趋势数据
  const trendPoints = []
  const now = new Date()
  const interval = timeRange.value === '1h' ? 5 : timeRange.value === '6h' ? 30 : 60 // 分钟
  const points = timeRange.value === '1h' ? 12 : timeRange.value === '6h' ? 12 : 24

  for (let i = points - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * interval * 60000)
    trendPoints.push({
      time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      connections: Math.floor(Math.random() * 30) + 10,
      traffic: Math.random() * 100 + 50
    })
  }
  trendData.value = trendPoints

  // 流量分布数据
  trafficData.value = [
    { name: 'HTTP', value: Math.floor(Math.random() * 40) + 30 },
    { name: 'HTTPS', value: Math.floor(Math.random() * 30) + 25 },
    { name: 'SSH', value: Math.floor(Math.random() * 15) + 10 },
    { name: 'FTP', value: Math.floor(Math.random() * 10) + 5 },
    { name: '其他', value: Math.floor(Math.random() * 10) + 5 }
  ]

  // 延迟热力图数据
  const heatmapData = []
  const hours = ['00', '04', '08', '12', '16', '20']
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

  days.forEach((day, dayIndex) => {
    hours.forEach((hour, hourIndex) => {
      heatmapData.push({
        day: dayIndex,
        hour: hourIndex,
        value: Math.random() * 100 + 20,
        dayName: day,
        hourName: hour
      })
    })
  })
  latencyData.value = heatmapData
}

// 切换自动刷新
const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer = setInterval(refreshData, 30000) // 30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 查看连接详情
const viewDetails = (connectionId) => {
  message.info(`查看连接 ${connectionId} 详情`)
}

// 终止连接
const terminateConnection = (connectionId) => {
  message.warning(`终止连接 ${connectionId}`)
  // 更新连接状态
  const connection = connections.value.find(c => c.id === connectionId)
  if (connection) {
    connection.status = 'disconnected'
  }
}

// 导出数据
const exportData = () => {
  message.info('导出功能开发中')
}

onMounted(() => {
  refreshData()
  if (autoRefresh.value) {
    toggleAutoRefresh(true)
  }
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>
