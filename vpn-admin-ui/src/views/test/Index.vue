<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">测试页面</h1>
    
    <div class="space-y-4">
      <n-card title="基本组件测试">
        <div class="space-y-4">
          <n-button type="primary">测试按钮</n-button>
          
          <n-input placeholder="测试输入框" />
          
          <n-select
            placeholder="测试选择器"
            :options="testOptions"
          />
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const testOptions = ref([
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' },
  { label: '选项3', value: 'option3' }
])
</script>
