<template>
  <n-modal v-model:show="showModal" preset="dialog" title="分配角色">
    <div class="space-y-4">
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">用户信息</h4>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <div class="flex items-center space-x-3">
            <n-avatar round size="small">
              <Icon icon="mdi:account" />
            </n-avatar>
            <div>
              <p class="font-medium">{{ userData?.username }}</p>
              <p class="text-sm text-gray-500">{{ userData?.email }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">当前角色</h4>
        <n-tag :type="getCurrentRoleType(userData?.role)" size="medium">
          {{ getCurrentRoleLabel(userData?.role) }}
        </n-tag>
      </div>
      
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">选择新角色</h4>
        <n-radio-group v-model:value="selectedRole">
          <n-space vertical>
            <n-radio
              v-for="role in roleOptions"
              :key="role.value"
              :value="role.value"
              :disabled="role.value === userData?.role"
            >
              <div class="flex items-center justify-between w-full">
                <div>
                  <div class="font-medium">{{ role.label }}</div>
                  <div class="text-sm text-gray-500">{{ role.description }}</div>
                </div>
                <n-tag :type="role.type" size="small">{{ role.label }}</n-tag>
              </div>
            </n-radio>
          </n-space>
        </n-radio-group>
      </div>
      
      <div v-if="selectedRole && selectedRole !== userData?.role">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">权限预览</h4>
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
          <div class="text-sm">
            <p class="font-medium text-blue-800 dark:text-blue-300 mb-2">
              {{ getRoleLabel(selectedRole) }} 权限包括：
            </p>
            <ul class="space-y-1 text-blue-700 dark:text-blue-400">
              <li v-for="permission in getRolePermissions(selectedRole)" :key="permission">
                • {{ permission }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button 
          type="primary" 
          :loading="loading" 
          :disabled="!selectedRole || selectedRole === userData?.role"
          @click="handleSubmit"
        >
          确认分配
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { usersApi } from '@/api/users'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const loading = ref(false)
const selectedRole = ref('')

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 角色选项
const roleOptions = [
  {
    label: '管理员',
    value: 'admin',
    type: 'error',
    description: '拥有系统所有权限，可以管理用户、角色和系统设置'
  },
  {
    label: '操作员',
    value: 'operator',
    type: 'warning',
    description: '可以管理客户端、服务端和连接，但不能管理用户和系统设置'
  },
  {
    label: '查看者',
    value: 'viewer',
    type: 'info',
    description: '只能查看数据，不能进行任何修改操作'
  }
]

// 获取当前角色类型
const getCurrentRoleType = (role) => {
  const roleMap = {
    admin: 'error',
    operator: 'warning',
    viewer: 'info'
  }
  return roleMap[role] || 'default'
}

// 获取当前角色标签
const getCurrentRoleLabel = (role) => {
  const roleMap = {
    admin: '管理员',
    operator: '操作员',
    viewer: '查看者'
  }
  return roleMap[role] || role
}

// 获取角色标签
const getRoleLabel = (role) => {
  const roleMap = {
    admin: '管理员',
    operator: '操作员',
    viewer: '查看者'
  }
  return roleMap[role] || role
}

// 获取角色权限
const getRolePermissions = (role) => {
  const permissionMap = {
    admin: [
      '用户管理（增删改查）',
      '角色管理（增删改查）',
      '客户端管理（增删改查）',
      '服务端管理（增删改查）',
      '连接监控（查看、断开）',
      '配置管理（增删改查）',
      '日志查看和导出',
      '系统设置管理'
    ],
    operator: [
      '客户端管理（增删改查）',
      '服务端管理（增删改查）',
      '连接监控（查看、断开）',
      '配置管理（查看、推送）',
      '日志查看',
      'NAT 管理'
    ],
    viewer: [
      '仪表盘查看',
      '客户端信息查看',
      '服务端信息查看',
      '连接状态查看',
      '配置信息查看',
      '日志查看'
    ]
  }
  return permissionMap[role] || []
}

// 提交处理
const handleSubmit = async () => {
  if (!selectedRole.value || selectedRole.value === props.userData?.role) {
    return
  }
  
  loading.value = true
  try {
    await usersApi.assignRole(props.userData.id, selectedRole.value)
    message.success('角色分配成功')
    emit('success')
  } catch (error) {
    message.error('角色分配失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show && props.userData) {
    selectedRole.value = props.userData.role
  }
})
</script>
