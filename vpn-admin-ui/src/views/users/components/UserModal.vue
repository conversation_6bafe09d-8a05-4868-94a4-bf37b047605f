<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑用户' : '新增用户'">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="用户名" path="username">
        <n-input v-model:value="formData.username" placeholder="请输入用户名" :disabled="isEdit" />
      </n-form-item>
      
      <n-form-item label="邮箱" path="email">
        <n-input v-model:value="formData.email" placeholder="请输入邮箱地址" />
      </n-form-item>
      
      <n-form-item label="密码" path="password" v-if="!isEdit">
        <n-input
          v-model:value="formData.password"
          type="password"
          placeholder="请输入密码"
          show-password-on="mousedown"
        />
      </n-form-item>
      
      <n-form-item label="确认密码" path="confirmPassword" v-if="!isEdit">
        <n-input
          v-model:value="formData.confirmPassword"
          type="password"
          placeholder="请确认密码"
          show-password-on="mousedown"
        />
      </n-form-item>
      
      <n-form-item label="角色" path="role">
        <n-select
          v-model:value="formData.role"
          placeholder="请选择角色"
          :options="roleOptions"
        />
      </n-form-item>
      
      <n-form-item label="状态" path="status">
        <n-select
          v-model:value="formData.status"
          placeholder="请选择状态"
          :options="statusOptions"
        />
      </n-form-item>
      
      <n-form-item label="真实姓名" path="realName">
        <n-input v-model:value="formData.realName" placeholder="请输入真实姓名" />
      </n-form-item>
      
      <n-form-item label="手机号" path="phone">
        <n-input v-model:value="formData.phone" placeholder="请输入手机号" />
      </n-form-item>
      
      <n-form-item label="备注" path="remark">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="3"
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { usersApi } from '@/api/users'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.userData)

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'viewer',
  status: 'active',
  realName: '',
  phone: '',
  remark: ''
})

// 角色选项
const roleOptions = [
  { label: '管理员', value: 'admin' },
  { label: '操作员', value: 'operator' },
  { label: '查看者', value: 'viewer' }
]

// 状态选项
const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '禁用', value: 'disabled' }
]

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9_]+$/,
      message: '用户名只能包含字母、数字和下划线',
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        return value === formData.password
      },
      message: '两次输入的密码不一致',
      trigger: 'blur'
    }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号',
      trigger: 'blur'
    }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'viewer',
    status: 'active',
    realName: '',
    phone: '',
    remark: ''
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      username: data.username || '',
      email: data.email || '',
      password: '',
      confirmPassword: '',
      role: data.role || 'viewer',
      status: data.status || 'active',
      realName: data.realName || '',
      phone: data.phone || '',
      remark: data.remark || ''
    })
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    const submitData = { ...formData }
    if (isEdit.value) {
      delete submitData.password
      delete submitData.confirmPassword
    }
    
    if (isEdit.value) {
      await usersApi.updateUser(props.userData.id, submitData)
      message.success('更新成功')
    } else {
      await usersApi.createUser(submitData)
      message.success('创建成功')
    }
    
    emit('success')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    if (props.userData) {
      fillFormData(props.userData)
    } else {
      resetForm()
    }
  }
})
</script>
