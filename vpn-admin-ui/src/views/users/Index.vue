<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">用户管理</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <Icon icon="mdi:plus" />
          </template>
          新增用户
        </n-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <n-input
          v-model:value="searchForm.keyword"
          placeholder="搜索用户名、邮箱"
          clearable
        >
          <template #prefix>
            <Icon icon="mdi:magnify" />
          </template>
        </n-input>

        <n-select
          v-model:value="searchForm.role"
          placeholder="角色筛选"
          clearable
          :options="roleOptions"
        />

        <n-select
          v-model:value="searchForm.status"
          placeholder="状态筛选"
          clearable
          :options="statusOptions"
        />

        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <Icon icon="mdi:magnify" />
          </template>
          搜索
        </n-button>
      </div>
    </div>

    <!-- 用户统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <StatCard
        title="总用户数"
        :value="userStats.total"
        icon="mdi:account-group"
        color="blue"
      />
      <StatCard
        title="活跃用户"
        :value="userStats.active"
        icon="mdi:account-check"
        color="green"
      />
      <StatCard
        title="管理员"
        :value="userStats.admins"
        icon="mdi:shield-account"
        color="purple"
      />
      <StatCard
        title="今日新增"
        :value="userStats.todayNew"
        icon="mdi:account-plus"
        color="orange"
      />
    </div>

    <!-- 用户列表 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">用户列表</h3>
        <div class="flex items-center space-x-3">
          <n-button size="small" @click="exportUsers">
            <template #icon>
              <Icon icon="mdi:download" />
            </template>
            导出
          </n-button>
          <n-button size="small" @click="batchDelete" :disabled="!selectedUsers.length">
            <template #icon>
              <Icon icon="mdi:delete" />
            </template>
            批量删除 ({{ selectedUsers.length }})
          </n-button>
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        v-model:checked-row-keys="selectedUsers"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- 新增/编辑用户弹窗 -->
    <UserModal
      v-model:show="showCreateModal"
      :user-data="editingUser"
      @success="handleModalSuccess"
    />

    <!-- 角色分配弹窗 -->
    <RoleAssignModal
      v-model:show="showRoleModal"
      :user-data="selectedUser"
      @success="handleRoleAssignSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'
import { formatTime } from '@/utils/format'
import { usersApi } from '@/api/users'
import StatCard from '@/components/StatCard.vue'
import UserModal from './components/UserModal.vue'
import RoleAssignModal from './components/RoleAssignModal.vue'

const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const showCreateModal = ref(false)
const showRoleModal = ref(false)
const editingUser = ref(null)
const selectedUser = ref(null)
const selectedUsers = ref([])
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  role: null,
  status: null
})

// 用户统计
const userStats = reactive({
  total: 0,
  active: 0,
  admins: 0,
  todayNew: 0
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 角色选项
const roleOptions = [
  { label: '管理员', value: 'admin' },
  { label: '操作员', value: 'operator' },
  { label: '查看者', value: 'viewer' }
]

// 状态选项
const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '禁用', value: 'disabled' },
  { label: '锁定', value: 'locked' }
]

// 表格列配置
const columns = [
  {
    type: 'selection'
  },
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '头像',
    key: 'avatar',
    width: 80,
    render: (row) => h('n-avatar', {
      round: true,
      size: 'small',
      src: row.avatar
    }, {
      default: () => h(Icon, { icon: 'mdi:account' })
    })
  },
  {
    title: '用户名',
    key: 'username',
    width: 120,
    render: (row) => h('div', { class: 'font-medium' }, row.username)
  },
  {
    title: '邮箱',
    key: 'email',
    width: 200
  },
  {
    title: '角色',
    key: 'role',
    width: 100,
    render: (row) => {
      const roleMap = {
        admin: { type: 'error', label: '管理员' },
        operator: { type: 'warning', label: '操作员' },
        viewer: { type: 'info', label: '查看者' }
      }
      const roleInfo = roleMap[row.role] || { type: 'default', label: row.role }
      return h('n-tag', {
        type: roleInfo.type,
        size: 'small'
      }, roleInfo.label)
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        active: { type: 'success', label: '活跃' },
        disabled: { type: 'warning', label: '禁用' },
        locked: { type: 'error', label: '锁定' }
      }
      const statusInfo = statusMap[row.status] || { type: 'default', label: row.status }
      return h('n-tag', {
        type: statusInfo.type,
        size: 'small'
      }, statusInfo.label)
    }
  },
  {
    title: '最后登录',
    key: 'lastLogin',
    width: 150,
    render: (row) => formatTime(row.lastLogin)
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 150,
    render: (row) => formatTime(row.createdAt)
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          onClick: () => editUser(row)
        }, '编辑'),
        h('n-button', {
          size: 'small',
          type: 'primary',
          onClick: () => assignRole(row)
        }, '分配角色'),
        h('n-button', {
          size: 'small',
          type: row.status === 'active' ? 'warning' : 'success',
          onClick: () => toggleUserStatus(row)
        }, row.status === 'active' ? '禁用' : '启用'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => deleteUser(row)
        }, '删除')
      ])
    }
  }
]

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    // 模拟 API 调用
    const mockUsers = []
    for (let i = 0; i < 25; i++) {
      mockUsers.push({
        id: i + 1,
        username: `user${String(i + 1).padStart(3, '0')}`,
        email: `user${i + 1}@example.com`,
        role: ['admin', 'operator', 'viewer'][Math.floor(Math.random() * 3)],
        status: ['active', 'disabled', 'locked'][Math.floor(Math.random() * 3)],
        avatar: null,
        lastLogin: new Date(Date.now() - Math.random() * 7 * 24 * 3600000),
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 3600000)
      })
    }

    tableData.value = mockUsers
    pagination.itemCount = mockUsers.length

    // 更新统计数据
    userStats.total = mockUsers.length
    userStats.active = mockUsers.filter(u => u.status === 'active').length
    userStats.admins = mockUsers.filter(u => u.role === 'admin').length
    userStats.todayNew = Math.floor(Math.random() * 5) + 1

  } catch (error) {
    message.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

// 刷新数据
const refreshData = () => {
  loadUsers()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  loadUsers()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadUsers()
}

// 编辑用户
const editUser = (user) => {
  editingUser.value = user
  showCreateModal.value = true
}

// 分配角色
const assignRole = (user) => {
  selectedUser.value = user
  showRoleModal.value = true
}

// 切换用户状态
const toggleUserStatus = (user) => {
  const action = user.status === 'active' ? '禁用' : '启用'
  dialog.warning({
    title: `确认${action}`,
    content: `确定要${action}用户 "${user.username}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        user.status = user.status === 'active' ? 'disabled' : 'active'
        message.success(`${action}成功`)
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 删除用户
const deleteUser = (user) => {
  dialog.error({
    title: '确认删除',
    content: `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const index = tableData.value.findIndex(u => u.id === user.id)
        if (index > -1) {
          tableData.value.splice(index, 1)
          pagination.itemCount--
        }
        message.success('删除成功')
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 批量删除
const batchDelete = () => {
  dialog.error({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        tableData.value = tableData.value.filter(user => !selectedUsers.value.includes(user.id))
        pagination.itemCount = tableData.value.length
        selectedUsers.value = []
        message.success('批量删除成功')
      } catch (error) {
        message.error('批量删除失败')
      }
    }
  })
}

// 导出用户
const exportUsers = () => {
  message.info('导出功能开发中')
}

// 弹窗成功回调
const handleModalSuccess = () => {
  showCreateModal.value = false
  editingUser.value = null
  loadUsers()
}

// 角色分配成功回调
const handleRoleAssignSuccess = () => {
  showRoleModal.value = false
  selectedUser.value = null
  loadUsers()
}

onMounted(() => {
  loadUsers()
})
</script>
