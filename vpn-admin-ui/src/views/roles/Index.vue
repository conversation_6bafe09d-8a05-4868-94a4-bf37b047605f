<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">角色管理</h1>
      <n-button type="primary">
        <template #icon>
          <Icon icon="mdi:plus" />
        </template>
        新增角色
      </n-button>
    </div>
    
    <div class="card">
      <p class="text-center text-gray-500">角色管理页面开发中...</p>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue'
</script>
