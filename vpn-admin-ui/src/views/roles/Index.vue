<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">角色管理</h1>
      <n-button type="primary" @click="showCreateModal = true">
        <template #icon>
          <Icon icon="mdi:plus" />
        </template>
        新增角色
      </n-button>
    </div>

    <!-- 角色卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="role in roles"
        :key="role.id"
        class="card hover:shadow-lg transition-shadow duration-200"
      >
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div :class="getRoleIconBg(role.type)" class="w-12 h-12 rounded-lg flex items-center justify-center">
              <Icon :icon="getRoleIcon(role.type)" class="text-xl text-white" />
            </div>
            <div>
              <h3 class="text-lg font-semibold">{{ role.name }}</h3>
              <p class="text-sm text-gray-500">{{ role.description }}</p>
            </div>
          </div>
          <n-dropdown :options="getActionOptions(role)" @select="(key) => handleAction(key, role)">
            <n-button quaternary circle size="small">
              <Icon icon="mdi:dots-vertical" />
            </n-button>
          </n-dropdown>
        </div>

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">用户数量</span>
            <n-tag size="small">{{ role.userCount }} 人</n-tag>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">权限数量</span>
            <n-tag size="small" type="info">{{ role.permissions.length }} 项</n-tag>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">状态</span>
            <n-tag size="small" :type="role.status === 'active' ? 'success' : 'warning'">
              {{ role.status === 'active' ? '启用' : '禁用' }}
            </n-tag>
          </div>
        </div>

        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500">创建时间</span>
            <span class="text-xs text-gray-500">{{ formatTime(role.createdAt) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限详情表格 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">权限详情</h3>
        <n-select
          v-model:value="selectedRoleId"
          placeholder="选择角色"
          :options="roleOptions"
          style="width: 200px"
          @update:value="loadPermissions"
        />
      </div>

      <n-data-table
        v-if="selectedRoleId"
        :columns="permissionColumns"
        :data="permissions"
        :loading="permissionLoading"
        size="small"
      />
      <div v-else class="text-center py-8 text-gray-500">
        请选择角色查看权限详情
      </div>
    </div>

    <!-- 新增/编辑角色弹窗 -->
    <RoleModal
      v-model:show="showCreateModal"
      :role-data="editingRole"
      @success="handleModalSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'
import { formatTime } from '@/utils/format'
import RoleModal from './components/RoleModal.vue'

const message = useMessage()
const dialog = useDialog()

const showCreateModal = ref(false)
const editingRole = ref(null)
const selectedRoleId = ref(null)
const permissionLoading = ref(false)
const roles = ref([])
const permissions = ref([])

// 角色选项
const roleOptions = computed(() => {
  return roles.value.map(role => ({
    label: role.name,
    value: role.id
  }))
})

// 权限表格列
const permissionColumns = [
  {
    title: '权限模块',
    key: 'module',
    width: 150
  },
  {
    title: '权限名称',
    key: 'name',
    width: 200
  },
  {
    title: '权限描述',
    key: 'description'
  },
  {
    title: '状态',
    key: 'granted',
    width: 100,
    render: (row) => {
      return h('n-tag', {
        type: row.granted ? 'success' : 'default',
        size: 'small'
      }, row.granted ? '已授权' : '未授权')
    }
  }
]

// 获取角色图标
const getRoleIcon = (type) => {
  const iconMap = {
    admin: 'mdi:shield-crown',
    operator: 'mdi:shield-account',
    viewer: 'mdi:shield-check'
  }
  return iconMap[type] || 'mdi:shield'
}

// 获取角色图标背景
const getRoleIconBg = (type) => {
  const bgMap = {
    admin: 'bg-gradient-to-r from-red-500 to-red-600',
    operator: 'bg-gradient-to-r from-orange-500 to-orange-600',
    viewer: 'bg-gradient-to-r from-blue-500 to-blue-600'
  }
  return bgMap[type] || 'bg-gradient-to-r from-gray-500 to-gray-600'
}

// 获取操作选项
const getActionOptions = (role) => {
  return [
    {
      label: '编辑',
      key: 'edit',
      icon: () => h(Icon, { icon: 'mdi:pencil' })
    },
    {
      label: role.status === 'active' ? '禁用' : '启用',
      key: 'toggle',
      icon: () => h(Icon, { icon: role.status === 'active' ? 'mdi:pause' : 'mdi:play' })
    },
    {
      type: 'divider'
    },
    {
      label: '删除',
      key: 'delete',
      icon: () => h(Icon, { icon: 'mdi:delete' })
    }
  ]
}

// 处理操作
const handleAction = (key, role) => {
  switch (key) {
    case 'edit':
      editRole(role)
      break
    case 'toggle':
      toggleRoleStatus(role)
      break
    case 'delete':
      deleteRole(role)
      break
  }
}

// 编辑角色
const editRole = (role) => {
  editingRole.value = role
  showCreateModal.value = true
}

// 切换角色状态
const toggleRoleStatus = (role) => {
  const action = role.status === 'active' ? '禁用' : '启用'
  dialog.warning({
    title: `确认${action}`,
    content: `确定要${action}角色 "${role.name}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      role.status = role.status === 'active' ? 'disabled' : 'active'
      message.success(`${action}成功`)
    }
  })
}

// 删除角色
const deleteRole = (role) => {
  if (role.userCount > 0) {
    message.error('该角色下还有用户，无法删除')
    return
  }

  dialog.error({
    title: '确认删除',
    content: `确定要删除角色 "${role.name}" 吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = roles.value.findIndex(r => r.id === role.id)
      if (index > -1) {
        roles.value.splice(index, 1)
      }
      message.success('删除成功')
    }
  })
}

// 加载角色列表
const loadRoles = async () => {
  try {
    // 模拟角色数据
    roles.value = [
      {
        id: 1,
        name: '超级管理员',
        type: 'admin',
        description: '拥有系统所有权限',
        userCount: 2,
        permissions: ['user:*', 'role:*', 'client:*', 'server:*', 'config:*', 'log:*', 'system:*'],
        status: 'active',
        createdAt: new Date('2024-01-01')
      },
      {
        id: 2,
        name: '运维管理员',
        type: 'operator',
        description: '负责日常运维管理',
        userCount: 5,
        permissions: ['client:*', 'server:*', 'config:read', 'config:push', 'log:read'],
        status: 'active',
        createdAt: new Date('2024-01-15')
      },
      {
        id: 3,
        name: '只读用户',
        type: 'viewer',
        description: '只能查看数据',
        userCount: 10,
        permissions: ['client:read', 'server:read', 'config:read', 'log:read'],
        status: 'active',
        createdAt: new Date('2024-02-01')
      }
    ]
  } catch (error) {
    message.error('加载角色列表失败')
  }
}

// 加载权限详情
const loadPermissions = async (roleId) => {
  if (!roleId) return

  permissionLoading.value = true
  try {
    // 模拟权限数据
    const allPermissions = [
      { module: '用户管理', name: 'user:read', description: '查看用户列表', granted: false },
      { module: '用户管理', name: 'user:create', description: '创建用户', granted: false },
      { module: '用户管理', name: 'user:update', description: '编辑用户', granted: false },
      { module: '用户管理', name: 'user:delete', description: '删除用户', granted: false },
      { module: '角色管理', name: 'role:read', description: '查看角色', granted: false },
      { module: '角色管理', name: 'role:create', description: '创建角色', granted: false },
      { module: '角色管理', name: 'role:update', description: '编辑角色', granted: false },
      { module: '角色管理', name: 'role:delete', description: '删除角色', granted: false },
      { module: '客户端管理', name: 'client:read', description: '查看客户端', granted: false },
      { module: '客户端管理', name: 'client:create', description: '创建客户端', granted: false },
      { module: '客户端管理', name: 'client:update', description: '编辑客户端', granted: false },
      { module: '客户端管理', name: 'client:delete', description: '删除客户端', granted: false },
      { module: '配置管理', name: 'config:read', description: '查看配置', granted: false },
      { module: '配置管理', name: 'config:create', description: '创建配置', granted: false },
      { module: '配置管理', name: 'config:update', description: '编辑配置', granted: false },
      { module: '配置管理', name: 'config:push', description: '推送配置', granted: false },
      { module: '日志管理', name: 'log:read', description: '查看日志', granted: false },
      { module: '系统设置', name: 'system:read', description: '查看系统设置', granted: false },
      { module: '系统设置', name: 'system:update', description: '修改系统设置', granted: false }
    ]

    const role = roles.value.find(r => r.id === roleId)
    if (role) {
      permissions.value = allPermissions.map(perm => ({
        ...perm,
        granted: role.permissions.some(rp =>
          rp === perm.name ||
          (rp.endsWith(':*') && perm.name.startsWith(rp.replace(':*', ':')))
        )
      }))
    }
  } catch (error) {
    message.error('加载权限详情失败')
  } finally {
    permissionLoading.value = false
  }
}

// 弹窗成功回调
const handleModalSuccess = () => {
  showCreateModal.value = false
  editingRole.value = null
  loadRoles()
}

onMounted(() => {
  loadRoles()
})
</script>
