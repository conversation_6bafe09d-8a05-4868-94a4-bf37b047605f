<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑角色' : '新增角色'" style="width: 600px">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="角色名称" path="name">
        <n-input v-model:value="formData.name" placeholder="请输入角色名称" />
      </n-form-item>
      
      <n-form-item label="角色类型" path="type">
        <n-select
          v-model:value="formData.type"
          placeholder="请选择角色类型"
          :options="typeOptions"
        />
      </n-form-item>
      
      <n-form-item label="角色描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入角色描述"
          :rows="3"
        />
      </n-form-item>
      
      <n-form-item label="状态" path="status">
        <n-select
          v-model:value="formData.status"
          placeholder="请选择状态"
          :options="statusOptions"
        />
      </n-form-item>
      
      <n-form-item label="权限配置" path="permissions">
        <div class="w-full">
          <div class="mb-3">
            <n-checkbox
              :checked="isAllSelected"
              :indeterminate="isIndeterminate"
              @update:checked="handleSelectAll"
            >
              全选
            </n-checkbox>
          </div>
          
          <div class="space-y-4">
            <div v-for="module in permissionModules" :key="module.name" class="border rounded-lg p-3">
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium">{{ module.label }}</h4>
                <n-checkbox
                  :checked="isModuleSelected(module.name)"
                  :indeterminate="isModuleIndeterminate(module.name)"
                  @update:checked="(checked) => handleModuleSelect(module.name, checked)"
                >
                  {{ isModuleSelected(module.name) ? '全选' : '选择' }}
                </n-checkbox>
              </div>
              
              <div class="grid grid-cols-2 gap-2">
                <n-checkbox
                  v-for="permission in module.permissions"
                  :key="permission.value"
                  :checked="formData.permissions.includes(permission.value)"
                  @update:checked="(checked) => handlePermissionChange(permission.value, checked)"
                >
                  {{ permission.label }}
                </n-checkbox>
              </div>
            </div>
          </div>
        </div>
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.roleData)

// 表单数据
const formData = reactive({
  name: '',
  type: 'viewer',
  description: '',
  status: 'active',
  permissions: []
})

// 角色类型选项
const typeOptions = [
  { label: '管理员', value: 'admin' },
  { label: '操作员', value: 'operator' },
  { label: '查看者', value: 'viewer' }
]

// 状态选项
const statusOptions = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'disabled' }
]

// 权限模块
const permissionModules = [
  {
    name: 'user',
    label: '用户管理',
    permissions: [
      { label: '查看用户', value: 'user:read' },
      { label: '创建用户', value: 'user:create' },
      { label: '编辑用户', value: 'user:update' },
      { label: '删除用户', value: 'user:delete' }
    ]
  },
  {
    name: 'role',
    label: '角色管理',
    permissions: [
      { label: '查看角色', value: 'role:read' },
      { label: '创建角色', value: 'role:create' },
      { label: '编辑角色', value: 'role:update' },
      { label: '删除角色', value: 'role:delete' }
    ]
  },
  {
    name: 'client',
    label: '客户端管理',
    permissions: [
      { label: '查看客户端', value: 'client:read' },
      { label: '创建客户端', value: 'client:create' },
      { label: '编辑客户端', value: 'client:update' },
      { label: '删除客户端', value: 'client:delete' }
    ]
  },
  {
    name: 'server',
    label: '服务端管理',
    permissions: [
      { label: '查看服务端', value: 'server:read' },
      { label: '创建服务端', value: 'server:create' },
      { label: '编辑服务端', value: 'server:update' },
      { label: '删除服务端', value: 'server:delete' }
    ]
  },
  {
    name: 'config',
    label: '配置管理',
    permissions: [
      { label: '查看配置', value: 'config:read' },
      { label: '创建配置', value: 'config:create' },
      { label: '编辑配置', value: 'config:update' },
      { label: '推送配置', value: 'config:push' }
    ]
  },
  {
    name: 'log',
    label: '日志管理',
    permissions: [
      { label: '查看日志', value: 'log:read' },
      { label: '导出日志', value: 'log:export' }
    ]
  },
  {
    name: 'system',
    label: '系统设置',
    permissions: [
      { label: '查看设置', value: 'system:read' },
      { label: '修改设置', value: 'system:update' }
    ]
  }
]

// 所有权限
const allPermissions = computed(() => {
  return permissionModules.flatMap(module => module.permissions.map(p => p.value))
})

// 是否全选
const isAllSelected = computed(() => {
  return formData.permissions.length === allPermissions.value.length
})

// 是否半选
const isIndeterminate = computed(() => {
  return formData.permissions.length > 0 && formData.permissions.length < allPermissions.value.length
})

// 模块是否全选
const isModuleSelected = (moduleName) => {
  const module = permissionModules.find(m => m.name === moduleName)
  if (!module) return false
  return module.permissions.every(p => formData.permissions.includes(p.value))
}

// 模块是否半选
const isModuleIndeterminate = (moduleName) => {
  const module = permissionModules.find(m => m.name === moduleName)
  if (!module) return false
  const selectedCount = module.permissions.filter(p => formData.permissions.includes(p.value)).length
  return selectedCount > 0 && selectedCount < module.permissions.length
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 20, message: '角色名称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择角色类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 全选处理
const handleSelectAll = (checked) => {
  if (checked) {
    formData.permissions = [...allPermissions.value]
  } else {
    formData.permissions = []
  }
}

// 模块选择处理
const handleModuleSelect = (moduleName, checked) => {
  const module = permissionModules.find(m => m.name === moduleName)
  if (!module) return
  
  if (checked) {
    // 添加模块所有权限
    module.permissions.forEach(p => {
      if (!formData.permissions.includes(p.value)) {
        formData.permissions.push(p.value)
      }
    })
  } else {
    // 移除模块所有权限
    module.permissions.forEach(p => {
      const index = formData.permissions.indexOf(p.value)
      if (index > -1) {
        formData.permissions.splice(index, 1)
      }
    })
  }
}

// 权限变更处理
const handlePermissionChange = (permission, checked) => {
  if (checked) {
    if (!formData.permissions.includes(permission)) {
      formData.permissions.push(permission)
    }
  } else {
    const index = formData.permissions.indexOf(permission)
    if (index > -1) {
      formData.permissions.splice(index, 1)
    }
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: 'viewer',
    description: '',
    status: 'active',
    permissions: []
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      name: data.name || '',
      type: data.type || 'viewer',
      description: data.description || '',
      status: data.status || 'active',
      permissions: [...(data.permissions || [])]
    })
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(isEdit.value ? '更新成功' : '创建成功')
    emit('success')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    if (props.roleData) {
      fillFormData(props.roleData)
    } else {
      resetForm()
    }
  }
})
</script>
