<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">服务端管理</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <Icon icon="mdi:plus" />
          </template>
          新增服务端
        </n-button>
      </div>
    </div>

    <!-- 服务端统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <StatCard
        title="服务端总数"
        :value="serverStats.total"
        icon="mdi:server"
        color="blue"
      />
      <StatCard
        title="在线服务端"
        :value="serverStats.online"
        icon="mdi:server-network"
        color="green"
      />
      <StatCard
        title="离线服务端"
        :value="serverStats.offline"
        icon="mdi:server-off"
        color="red"
      />
      <StatCard
        title="总连接数"
        :value="serverStats.totalConnections"
        icon="mdi:connection"
        color="purple"
      />
    </div>

    <!-- 服务端状态图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="card">
        <h3 class="text-lg font-semibold mb-4">服务端状态分布</h3>
        <div class="h-64">
          <ServerStatusChart :data="statusChartData" />
        </div>
      </div>

      <div class="card">
        <h3 class="text-lg font-semibold mb-4">连接数趋势</h3>
        <div class="h-64">
          <ConnectionTrendChart :data="connectionTrendData" />
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <n-input
          v-model:value="searchForm.keyword"
          placeholder="搜索服务端名称、IP"
          clearable
        >
          <template #prefix>
            <Icon icon="mdi:magnify" />
          </template>
        </n-input>

        <n-select
          v-model:value="searchForm.status"
          placeholder="状态筛选"
          clearable
          :options="statusOptions"
        />

        <n-select
          v-model:value="searchForm.region"
          placeholder="区域筛选"
          clearable
          :options="regionOptions"
        />

        <n-button type="primary" @click="handleSearch">
          <template #icon>
            <Icon icon="mdi:magnify" />
          </template>
          搜索
        </n-button>
      </div>
    </div>

    <!-- 服务端列表 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">服务端列表</h3>
        <div class="flex items-center space-x-3">
          <n-button size="small" @click="batchStart" :disabled="!selectedServers.length">
            <template #icon>
              <Icon icon="mdi:play" />
            </template>
            批量启动 ({{ selectedServers.length }})
          </n-button>
          <n-button size="small" @click="batchStop" :disabled="!selectedServers.length">
            <template #icon>
              <Icon icon="mdi:stop" />
            </template>
            批量停止 ({{ selectedServers.length }})
          </n-button>
          <n-button size="small" @click="exportData">
            <template #icon>
              <Icon icon="mdi:download" />
            </template>
            导出
          </n-button>
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        v-model:checked-row-keys="selectedServers"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- 新增/编辑服务端弹窗 -->
    <ServerModal
      v-model:show="showCreateModal"
      :server-data="editingServer"
      @success="handleModalSuccess"
    />

    <!-- 服务端详情弹窗 -->
    <ServerDetailModal
      v-model:show="showDetailModal"
      :server-data="selectedServer"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'
import { formatTime, formatFileSize } from '@/utils/format'
import StatCard from '@/components/StatCard.vue'
import ServerStatusChart from '@/components/charts/ServerStatusChart.vue'
import ConnectionTrendChart from '@/components/charts/ConnectionTrendChart.vue'
import ServerModal from './components/ServerModal.vue'
import ServerDetailModal from './components/ServerDetailModal.vue'

const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const editingServer = ref(null)
const selectedServer = ref(null)
const selectedServers = ref([])
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null,
  region: null
})

// 服务端统计
const serverStats = reactive({
  total: 0,
  online: 0,
  offline: 0,
  totalConnections: 0
})

// 图表数据
const statusChartData = ref([])
const connectionTrendData = ref([])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 状态选项
const statusOptions = [
  { label: '在线', value: 'online' },
  { label: '离线', value: 'offline' },
  { label: '维护中', value: 'maintenance' },
  { label: '错误', value: 'error' }
]

// 区域选项
const regionOptions = [
  { label: '华北', value: 'north' },
  { label: '华东', value: 'east' },
  { label: '华南', value: 'south' },
  { label: '华西', value: 'west' },
  { label: '海外', value: 'overseas' }
]

// 表格列配置
const columns = [
  {
    type: 'selection'
  },
  {
    title: '服务端名称',
    key: 'name',
    width: 150,
    render: (row) => h('div', { class: 'flex items-center space-x-2' }, [
      h('div', {
        class: `w-2 h-2 rounded-full ${getStatusColor(row.status)}`
      }),
      h('span', { class: 'font-medium' }, row.name)
    ])
  },
  {
    title: 'IP 地址',
    key: 'ip',
    width: 120
  },
  {
    title: '端口',
    key: 'port',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        online: { type: 'success', label: '在线' },
        offline: { type: 'error', label: '离线' },
        maintenance: { type: 'warning', label: '维护中' },
        error: { type: 'error', label: '错误' }
      }
      const statusInfo = statusMap[row.status] || { type: 'default', label: row.status }
      return h('n-tag', {
        type: statusInfo.type,
        size: 'small'
      }, statusInfo.label)
    }
  },
  {
    title: '区域',
    key: 'region',
    width: 100,
    render: (row) => {
      const regionMap = {
        north: '华北',
        east: '华东',
        south: '华南',
        west: '华西',
        overseas: '海外'
      }
      return regionMap[row.region] || row.region
    }
  },
  {
    title: '连接数',
    key: 'connections',
    width: 100,
    render: (row) => h('span', {
      class: row.connections > 50 ? 'text-red-600' : row.connections > 20 ? 'text-yellow-600' : 'text-green-600'
    }, row.connections)
  },
  {
    title: 'CPU 使用率',
    key: 'cpuUsage',
    width: 120,
    render: (row) => {
      const usage = row.cpuUsage || 0
      const color = usage > 80 ? 'error' : usage > 60 ? 'warning' : 'success'
      return h('n-progress', {
        type: 'line',
        status: color,
        percentage: usage,
        showIndicator: false,
        height: 8
      })
    }
  },
  {
    title: '内存使用率',
    key: 'memoryUsage',
    width: 120,
    render: (row) => {
      const usage = row.memoryUsage || 0
      const color = usage > 80 ? 'error' : usage > 60 ? 'warning' : 'success'
      return h('n-progress', {
        type: 'line',
        status: color,
        percentage: usage,
        showIndicator: false,
        height: 8
      })
    }
  },
  {
    title: '最后心跳',
    key: 'lastHeartbeat',
    width: 150,
    render: (row) => formatTime(row.lastHeartbeat)
  },
  {
    title: '操作',
    key: 'actions',
    width: 250,
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('n-button', {
          size: 'small',
          onClick: () => viewDetail(row)
        }, '详情'),
        h('n-button', {
          size: 'small',
          type: 'primary',
          onClick: () => editServer(row)
        }, '编辑'),
        h('n-button', {
          size: 'small',
          type: row.status === 'online' ? 'warning' : 'success',
          onClick: () => toggleServerStatus(row)
        }, row.status === 'online' ? '停止' : '启动'),
        h('n-button', {
          size: 'small',
          type: 'info',
          onClick: () => restartServer(row)
        }, '重启'),
        h('n-button', {
          size: 'small',
          type: 'error',
          onClick: () => deleteServer(row)
        }, '删除')
      ])
    }
  }
]

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    online: 'bg-green-500',
    offline: 'bg-red-500',
    maintenance: 'bg-yellow-500',
    error: 'bg-red-500'
  }
  return colorMap[status] || 'bg-gray-500'
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟服务端数据
    const mockServers = []
    const statuses = ['online', 'offline', 'maintenance', 'error']
    const regions = ['north', 'east', 'south', 'west', 'overseas']

    for (let i = 0; i < 20; i++) {
      const status = statuses[Math.floor(Math.random() * statuses.length)]
      mockServers.push({
        id: i + 1,
        name: `server-${String(i + 1).padStart(3, '0')}`,
        ip: `10.0.${Math.floor(i / 10)}.${(i % 10) + 1}`,
        port: 655 + i,
        status,
        region: regions[Math.floor(Math.random() * regions.length)],
        connections: status === 'online' ? Math.floor(Math.random() * 100) : 0,
        cpuUsage: status === 'online' ? Math.floor(Math.random() * 100) : 0,
        memoryUsage: status === 'online' ? Math.floor(Math.random() * 100) : 0,
        lastHeartbeat: status === 'online' ? new Date(Date.now() - Math.random() * 300000) : new Date(Date.now() - Math.random() * 3600000),
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 3600000)
      })
    }

    tableData.value = mockServers
    pagination.itemCount = mockServers.length

    // 更新统计数据
    serverStats.total = mockServers.length
    serverStats.online = mockServers.filter(s => s.status === 'online').length
    serverStats.offline = mockServers.filter(s => s.status === 'offline').length
    serverStats.totalConnections = mockServers.reduce((sum, s) => sum + s.connections, 0)

    // 更新图表数据
    statusChartData.value = [
      { name: '在线', value: serverStats.online },
      { name: '离线', value: serverStats.offline },
      { name: '维护中', value: mockServers.filter(s => s.status === 'maintenance').length },
      { name: '错误', value: mockServers.filter(s => s.status === 'error').length }
    ]

    // 连接数趋势
    connectionTrendData.value = Array.from({ length: 24 }, (_, i) => ({
      time: `${String(i).padStart(2, '0')}:00`,
      connections: Math.floor(Math.random() * 200) + 100
    }))

  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

// 查看详情
const viewDetail = (server) => {
  selectedServer.value = server
  showDetailModal.value = true
}

// 编辑服务端
const editServer = (server) => {
  editingServer.value = server
  showCreateModal.value = true
}

// 切换服务端状态
const toggleServerStatus = (server) => {
  const action = server.status === 'online' ? '停止' : '启动'
  dialog.warning({
    title: `确认${action}`,
    content: `确定要${action}服务端 "${server.name}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        server.status = server.status === 'online' ? 'offline' : 'online'
        if (server.status === 'offline') {
          server.connections = 0
          server.cpuUsage = 0
          server.memoryUsage = 0
        }
        message.success(`${action}成功`)
      } catch (error) {
        message.error(`${action}失败`)
      }
    }
  })
}

// 重启服务端
const restartServer = (server) => {
  dialog.warning({
    title: '确认重启',
    content: `确定要重启服务端 "${server.name}" 吗？这将断开所有连接。`,
    positiveText: '确定重启',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        server.status = 'maintenance'
        server.connections = 0
        message.info('正在重启服务端...')

        // 模拟重启过程
        setTimeout(() => {
          server.status = 'online'
          server.lastHeartbeat = new Date()
          message.success('重启成功')
        }, 3000)
      } catch (error) {
        message.error('重启失败')
      }
    }
  })
}

// 删除服务端
const deleteServer = (server) => {
  if (server.connections > 0) {
    message.error('该服务端还有活跃连接，无法删除')
    return
  }

  dialog.error({
    title: '确认删除',
    content: `确定要删除服务端 "${server.name}" 吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const index = tableData.value.findIndex(s => s.id === server.id)
        if (index > -1) {
          tableData.value.splice(index, 1)
          pagination.itemCount--
        }
        message.success('删除成功')
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 批量启动
const batchStart = async () => {
  const servers = tableData.value.filter(s => selectedServers.value.includes(s.id))
  for (const server of servers) {
    if (server.status !== 'online') {
      server.status = 'online'
      server.lastHeartbeat = new Date()
    }
  }
  selectedServers.value = []
  message.success('批量启动成功')
}

// 批量停止
const batchStop = async () => {
  const servers = tableData.value.filter(s => selectedServers.value.includes(s.id))
  for (const server of servers) {
    if (server.status === 'online') {
      server.status = 'offline'
      server.connections = 0
      server.cpuUsage = 0
      server.memoryUsage = 0
    }
  }
  selectedServers.value = []
  message.success('批量停止成功')
}

// 导出数据
const exportData = () => {
  message.info('导出功能开发中')
}

// 弹窗成功回调
const handleModalSuccess = () => {
  showCreateModal.value = false
  editingServer.value = null
  loadData()
}

onMounted(() => {
  loadData()
})
</script>
