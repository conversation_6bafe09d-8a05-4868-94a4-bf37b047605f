<template>
  <n-modal v-model:show="showModal" preset="dialog" title="服务端详情" style="width: 900px">
    <div v-if="serverData" class="space-y-6">
      <!-- 基本信息 -->
      <div class="grid grid-cols-2 gap-6">
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">基本信息</h4>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">服务端名称</span>
              <span class="text-sm font-medium">{{ serverData.name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">IP 地址</span>
              <span class="text-sm font-medium">{{ serverData.ip }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">端口</span>
              <span class="text-sm font-medium">{{ serverData.port }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">区域</span>
              <span class="text-sm font-medium">{{ getRegionLabel(serverData.region) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">状态</span>
              <n-tag :type="getStatusColor(serverData.status)" size="small">
                {{ getStatusLabel(serverData.status) }}
              </n-tag>
            </div>
          </div>
        </div>
        
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">运行状态</h4>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">当前连接数</span>
              <span class="text-sm font-medium">{{ serverData.connections || 0 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">CPU 使用率</span>
              <span class="text-sm font-medium">{{ serverData.cpuUsage || 0 }}%</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">内存使用率</span>
              <span class="text-sm font-medium">{{ serverData.memoryUsage || 0 }}%</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">网络流量</span>
              <span class="text-sm font-medium">{{ formatTraffic(serverData.traffic) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">最后心跳</span>
              <span class="text-sm font-medium">{{ formatTime(serverData.lastHeartbeat) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 性能监控图表 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">CPU 使用率趋势</h4>
          <div class="h-48 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
            <CpuUsageChart :data="cpuData" />
          </div>
        </div>
        
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">内存使用率趋势</h4>
          <div class="h-48 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
            <MemoryUsageChart :data="memoryData" />
          </div>
        </div>
      </div>

      <!-- 连接列表 -->
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">活跃连接</h4>
        <n-data-table
          :columns="connectionColumns"
          :data="connections"
          size="small"
          :pagination="{ pageSize: 5 }"
        />
      </div>

      <!-- 配置信息 -->
      <div class="grid grid-cols-2 gap-6">
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">网络配置</h4>
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">网络模式</span>
              <span>{{ getNetworkModeLabel(serverData.networkMode) }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">最大连接数</span>
              <span>{{ serverData.maxConnections || 100 }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">子网</span>
              <span>{{ serverData.subnet || '10.0.0.0/24' }}</span>
            </div>
          </div>
        </div>
        
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">安全配置</h4>
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">加密算法</span>
              <span>{{ serverData.encryption || 'AES-256-CBC' }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">压缩级别</span>
              <span>{{ serverData.compression || 6 }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">认证方式</span>
              <span>{{ serverData.authMethod || 'PSK' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center space-x-3">
        <n-button @click="restartServer" :loading="restarting">
          <template #icon>
            <Icon icon="mdi:restart" />
          </template>
          重启服务
        </n-button>
        <n-button type="primary" @click="viewLogs">
          <template #icon>
            <Icon icon="mdi:file-document" />
          </template>
          查看日志
        </n-button>
        <n-button type="warning" @click="exportConfig">
          <template #icon>
            <Icon icon="mdi:download" />
          </template>
          导出配置
        </n-button>
      </div>
    </div>
    
    <template #action>
      <div class="flex justify-end">
        <n-button @click="handleClose">关闭</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage } from 'naive-ui'
import { formatTime } from '@/utils/format'
import CpuUsageChart from '@/components/charts/CpuUsageChart.vue'
import MemoryUsageChart from '@/components/charts/MemoryUsageChart.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  serverData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show'])

const message = useMessage()
const restarting = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 模拟性能数据
const cpuData = ref(Array.from({ length: 20 }, (_, i) => ({
  time: new Date(Date.now() - (19 - i) * 60000),
  value: Math.floor(Math.random() * 50) + 20
})))

const memoryData = ref(Array.from({ length: 20 }, (_, i) => ({
  time: new Date(Date.now() - (19 - i) * 60000),
  value: Math.floor(Math.random() * 30) + 40
})))

// 模拟连接数据
const connections = ref([
  {
    id: 1,
    clientName: 'client-001',
    clientIp: '************',
    connectedAt: new Date(Date.now() - 3600000),
    traffic: '125.6 MB'
  },
  {
    id: 2,
    clientName: 'client-002',
    clientIp: '************',
    connectedAt: new Date(Date.now() - 1800000),
    traffic: '89.2 MB'
  },
  {
    id: 3,
    clientName: 'client-003',
    clientIp: '************',
    connectedAt: new Date(Date.now() - 900000),
    traffic: '45.8 MB'
  }
])

// 连接表格列
const connectionColumns = [
  {
    title: '客户端名称',
    key: 'clientName'
  },
  {
    title: '客户端 IP',
    key: 'clientIp'
  },
  {
    title: '连接时间',
    key: 'connectedAt',
    render: (row) => formatTime(row.connectedAt)
  },
  {
    title: '流量',
    key: 'traffic'
  },
  {
    title: '操作',
    key: 'actions',
    render: (row) => h('n-button', {
      size: 'small',
      type: 'error',
      onClick: () => disconnectClient(row)
    }, '断开')
  }
]

// 获取区域标签
const getRegionLabel = (region) => {
  const regionMap = {
    north: '华北',
    east: '华东',
    south: '华南',
    west: '华西',
    overseas: '海外'
  }
  return regionMap[region] || region
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    online: 'success',
    offline: 'error',
    maintenance: 'warning',
    error: 'error'
  }
  return colorMap[status] || 'default'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const labelMap = {
    online: '在线',
    offline: '离线',
    maintenance: '维护中',
    error: '错误'
  }
  return labelMap[status] || status
}

// 获取网络模式标签
const getNetworkModeLabel = (mode) => {
  const modeMap = {
    switch: '交换模式',
    router: '路由模式',
    hub: '集线器模式'
  }
  return modeMap[mode] || mode
}

// 格式化流量
const formatTraffic = (traffic) => {
  if (!traffic) return '0 B'
  return traffic
}

// 重启服务器
const restartServer = async () => {
  restarting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('服务端重启成功')
  } catch (error) {
    message.error('服务端重启失败')
  } finally {
    restarting.value = false
  }
}

// 查看日志
const viewLogs = () => {
  message.info('跳转到日志页面')
}

// 导出配置
const exportConfig = () => {
  message.success('配置导出成功')
}

// 断开客户端连接
const disconnectClient = (client) => {
  const index = connections.value.findIndex(c => c.id === client.id)
  if (index > -1) {
    connections.value.splice(index, 1)
    message.success(`已断开 ${client.clientName} 的连接`)
  }
}

// 关闭弹窗
const handleClose = () => {
  showModal.value = false
}
</script>
