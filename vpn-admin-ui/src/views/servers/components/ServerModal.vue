<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑服务端' : '新增服务端'" style="width: 600px">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="所属子网" path="subnetId">
        <n-select
          v-model:value="formData.subnetId"
          placeholder="请选择子网"
          :options="subnetOptions"
          :loading="subnetLoading"
          @update:value="handleSubnetChange"
        />
      </n-form-item>

      <n-form-item label="服务端名称" path="name">
        <n-input v-model:value="formData.name" placeholder="请输入服务端名称" />
      </n-form-item>

      <n-form-item label="主机公网地址" path="publicIp">
        <n-input v-model:value="formData.publicIp" placeholder="请输入主机公网 IP 地址" />
      </n-form-item>

      <n-form-item label="监听端口" path="port">
        <n-input-number v-model:value="formData.port" placeholder="请输入监听端口" :min="1024" :max="65535" />
      </n-form-item>
      
      <n-form-item label="区域" path="region">
        <n-select
          v-model:value="formData.region"
          placeholder="请选择区域"
          :options="regionOptions"
        />
      </n-form-item>
      
      <n-form-item label="最大连接数" path="maxConnections">
        <n-input-number v-model:value="formData.maxConnections" placeholder="最大连接数" :min="1" :max="10000" />
      </n-form-item>
      
      <n-form-item label="网络模式" path="networkMode">
        <n-select
          v-model:value="formData.networkMode"
          placeholder="请选择网络模式"
          :options="networkModeOptions"
        />
      </n-form-item>
      
      <n-form-item label="加密算法" path="encryption">
        <n-select
          v-model:value="formData.encryption"
          placeholder="请选择加密算法"
          :options="encryptionOptions"
        />
      </n-form-item>
      
      <n-form-item label="压缩级别" path="compression">
        <n-slider v-model:value="formData.compression" :min="0" :max="9" :step="1" :marks="compressionMarks" />
      </n-form-item>
      
      <n-form-item label="自动启动" path="autoStart">
        <n-switch v-model:value="formData.autoStart" />
      </n-form-item>
      
      <n-form-item label="描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入描述信息"
          :rows="3"
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { subnetsApi } from '@/api/subnets'
import { serversApi } from '@/api/servers'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  serverData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)
const subnetLoading = ref(false)
const subnetOptions = ref([])

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.serverData)

// 表单数据
const formData = reactive({
  subnetId: '',
  name: '',
  publicIp: '',
  port: 655,
  region: 'east',
  maxConnections: 100,
  networkMode: 'switch',
  encryption: 'aes-256-cbc',
  compression: 6,
  autoStart: true,
  description: ''
})

// 区域选项
const regionOptions = [
  { label: '华北', value: 'north' },
  { label: '华东', value: 'east' },
  { label: '华南', value: 'south' },
  { label: '华西', value: 'west' },
  { label: '海外', value: 'overseas' }
]

// 网络模式选项
const networkModeOptions = [
  { label: '交换模式', value: 'switch' },
  { label: '路由模式', value: 'router' },
  { label: '集线器模式', value: 'hub' }
]

// 加密算法选项
const encryptionOptions = [
  { label: 'AES-256-CBC', value: 'aes-256-cbc' },
  { label: 'AES-256-GCM', value: 'aes-256-gcm' },
  { label: 'ChaCha20-Poly1305', value: 'chacha20-poly1305' },
  { label: 'Blowfish', value: 'blowfish' }
]

// 压缩级别标记
const compressionMarks = {
  0: '无压缩',
  3: '低',
  6: '中',
  9: '高'
}

// 表单验证规则
const rules = {
  subnetId: [
    { required: true, message: '请选择子网', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入服务端名称', trigger: 'blur' },
    { min: 2, max: 50, message: '服务端名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  publicIp: [
    { required: true, message: '请输入主机公网地址', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: '请输入有效的 IP 地址',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入监听端口', trigger: 'blur' },
    { type: 'number', min: 1024, max: 65535, message: '端口范围在 1024-65535', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请选择区域', trigger: 'change' }
  ],
  maxConnections: [
    { required: true, message: '请输入最大连接数', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '最大连接数范围在 1-10000', trigger: 'blur' }
  ],
  networkMode: [
    { required: true, message: '请选择网络模式', trigger: 'change' }
  ],
  encryption: [
    { required: true, message: '请选择加密算法', trigger: 'change' }
  ]
}

// 加载可用子网
const loadAvailableSubnets = async () => {
  try {
    subnetLoading.value = true
    const response = await subnetsApi.getAvailableSubnets()
    console.log('可用子网响应:', response)

    if (response.code === 0 && response.data) {
      subnetOptions.value = response.data.map(subnet => ({
        label: `${subnet.name} (${subnet.cidr})`,
        value: subnet.id,
        subnet: subnet
      }))
    } else {
      subnetOptions.value = []
    }
  } catch (error) {
    console.error('加载子网失败:', error)
    message.error('加载子网列表失败')
    subnetOptions.value = []
  } finally {
    subnetLoading.value = false
  }
}

// 子网选择变化处理
const handleSubnetChange = (subnetId) => {
  const selectedSubnet = subnetOptions.value.find(option => option.value === subnetId)
  if (selectedSubnet && selectedSubnet.subnet) {
    // 自动填充端口（使用子网的监听端口）
    formData.port = selectedSubnet.subnet.port || 655
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    subnetId: '',
    name: '',
    publicIp: '',
    port: 655,
    region: 'east',
    maxConnections: 100,
    networkMode: 'switch',
    encryption: 'aes-256-cbc',
    compression: 6,
    autoStart: true,
    description: ''
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      subnetId: data.subnetId || '',
      name: data.name || '',
      publicIp: data.publicIp || '',
      port: data.port || 655,
      region: data.region || 'east',
      maxConnections: data.maxConnections || 100,
      networkMode: data.networkMode || 'switch',
      encryption: data.encryption || 'aes-256-cbc',
      compression: data.compression || 6,
      autoStart: data.autoStart !== undefined ? data.autoStart : true,
      description: data.description || ''
    })
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    console.log('开始验证表单，当前数据:', formData)
    await formRef.value?.validate()
    console.log('表单验证通过')

    loading.value = true

    const submitData = {
      subnetId: formData.subnetId,
      name: formData.name,
      publicIp: formData.publicIp,
      port: Number(formData.port),
      region: formData.region,
      maxConnections: Number(formData.maxConnections),
      networkMode: formData.networkMode,
      encryption: formData.encryption,
      compression: Number(formData.compression),
      autoStart: formData.autoStart,
      description: formData.description
    }

    console.log('提交数据:', submitData)

    if (isEdit.value) {
      await serversApi.updateServer(props.serverData.id, submitData)
      message.success('更新成功')
    } else {
      await serversApi.createServer(submitData)
      message.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交错误:', error)
    if (error.errors) {
      // 表单验证错误
      console.log('验证错误详情:', error.errors)
      const errorMessages = []

      Object.keys(error.errors).forEach(fieldName => {
        const fieldErrors = error.errors[fieldName]
        if (Array.isArray(fieldErrors)) {
          fieldErrors.forEach(err => {
            if (err && err.message) {
              errorMessages.push(`${fieldName}: ${err.message}`)
            }
          })
        }
      })

      if (errorMessages.length > 0) {
        message.error(`验证失败: ${errorMessages.join(', ')}`)
      } else {
        message.error('表单验证失败，请检查输入')
      }
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    loadAvailableSubnets()
    if (props.serverData) {
      fillFormData(props.serverData)
    } else {
      resetForm()
    }
  }
})

// 组件挂载时加载子网
onMounted(() => {
  loadAvailableSubnets()
})
</script>
