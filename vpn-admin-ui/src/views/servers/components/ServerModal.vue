<template>
  <n-modal v-model:show="showModal" preset="dialog" :title="isEdit ? '编辑服务端' : '新增服务端'" style="width: 600px">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="服务端名称" path="name">
        <n-input v-model:value="formData.name" placeholder="请输入服务端名称" />
      </n-form-item>
      
      <n-form-item label="IP 地址" path="ip">
        <n-input v-model:value="formData.ip" placeholder="请输入 IP 地址" />
      </n-form-item>
      
      <n-form-item label="端口" path="port">
        <n-input-number v-model:value="formData.port" placeholder="请输入端口" :min="1" :max="65535" />
      </n-form-item>
      
      <n-form-item label="区域" path="region">
        <n-select
          v-model:value="formData.region"
          placeholder="请选择区域"
          :options="regionOptions"
        />
      </n-form-item>
      
      <n-form-item label="最大连接数" path="maxConnections">
        <n-input-number v-model:value="formData.maxConnections" placeholder="最大连接数" :min="1" :max="10000" />
      </n-form-item>
      
      <n-form-item label="网络模式" path="networkMode">
        <n-select
          v-model:value="formData.networkMode"
          placeholder="请选择网络模式"
          :options="networkModeOptions"
        />
      </n-form-item>
      
      <n-form-item label="加密算法" path="encryption">
        <n-select
          v-model:value="formData.encryption"
          placeholder="请选择加密算法"
          :options="encryptionOptions"
        />
      </n-form-item>
      
      <n-form-item label="压缩级别" path="compression">
        <n-slider v-model:value="formData.compression" :min="0" :max="9" :step="1" :marks="compressionMarks" />
      </n-form-item>
      
      <n-form-item label="自动启动" path="autoStart">
        <n-switch v-model:value="formData.autoStart" />
      </n-form-item>
      
      <n-form-item label="描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入描述信息"
          :rows="3"
        />
      </n-form-item>
    </n-form>
    
    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  serverData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref(null)
const loading = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.serverData)

// 表单数据
const formData = reactive({
  name: '',
  ip: '',
  port: 655,
  region: 'east',
  maxConnections: 100,
  networkMode: 'switch',
  encryption: 'aes-256-cbc',
  compression: 6,
  autoStart: true,
  description: ''
})

// 区域选项
const regionOptions = [
  { label: '华北', value: 'north' },
  { label: '华东', value: 'east' },
  { label: '华南', value: 'south' },
  { label: '华西', value: 'west' },
  { label: '海外', value: 'overseas' }
]

// 网络模式选项
const networkModeOptions = [
  { label: '交换模式', value: 'switch' },
  { label: '路由模式', value: 'router' },
  { label: '集线器模式', value: 'hub' }
]

// 加密算法选项
const encryptionOptions = [
  { label: 'AES-256-CBC', value: 'aes-256-cbc' },
  { label: 'AES-256-GCM', value: 'aes-256-gcm' },
  { label: 'ChaCha20-Poly1305', value: 'chacha20-poly1305' },
  { label: 'Blowfish', value: 'blowfish' }
]

// 压缩级别标记
const compressionMarks = {
  0: '无压缩',
  3: '低',
  6: '中',
  9: '高'
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入服务端名称', trigger: 'blur' },
    { min: 2, max: 50, message: '服务端名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  ip: [
    { required: true, message: '请输入 IP 地址', trigger: 'blur' },
    { 
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: '请输入有效的 IP 地址',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围在 1-65535', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请选择区域', trigger: 'change' }
  ],
  maxConnections: [
    { required: true, message: '请输入最大连接数', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '最大连接数范围在 1-10000', trigger: 'blur' }
  ],
  networkMode: [
    { required: true, message: '请选择网络模式', trigger: 'change' }
  ],
  encryption: [
    { required: true, message: '请选择加密算法', trigger: 'change' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    ip: '',
    port: 655,
    region: 'east',
    maxConnections: 100,
    networkMode: 'switch',
    encryption: 'aes-256-cbc',
    compression: 6,
    autoStart: true,
    description: ''
  })
}

// 填充表单数据
const fillFormData = (data) => {
  if (data) {
    Object.assign(formData, {
      name: data.name || '',
      ip: data.ip || '',
      port: data.port || 655,
      region: data.region || 'east',
      maxConnections: data.maxConnections || 100,
      networkMode: data.networkMode || 'switch',
      encryption: data.encryption || 'aes-256-cbc',
      compression: data.compression || 6,
      autoStart: data.autoStart !== undefined ? data.autoStart : true,
      description: data.description || ''
    })
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(isEdit.value ? '更新成功' : '创建成功')
    emit('success')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}

// 监听弹窗显示状态
watch(() => props.show, (show) => {
  if (show) {
    if (props.serverData) {
      fillFormData(props.serverData)
    } else {
      resetForm()
    }
  }
})
</script>
