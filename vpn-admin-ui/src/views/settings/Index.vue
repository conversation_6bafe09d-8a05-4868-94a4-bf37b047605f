<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">系统设置</h1>
      <div class="flex items-center space-x-3">
        <n-button @click="resetSettings">
          <template #icon>
            <Icon icon="mdi:restore" />
          </template>
          重置默认
        </n-button>
        <n-button type="primary" @click="saveSettings" :loading="saving">
          <template #icon>
            <Icon icon="mdi:content-save" />
          </template>
          保存设置
        </n-button>
      </div>
    </div>

    <!-- 设置选项卡 -->
    <div class="card">
      <n-tabs v-model:value="activeTab" type="line" animated>
        <!-- 基本设置 -->
        <n-tab-pane name="basic" tab="基本设置">
          <div class="space-y-6 p-4">
            <n-form :model="basicSettings" label-placement="left" label-width="120">
              <n-form-item label="系统名称">
                <n-input v-model:value="basicSettings.systemName" placeholder="请输入系统名称" />
              </n-form-item>

              <n-form-item label="系统描述">
                <n-input
                  v-model:value="basicSettings.systemDescription"
                  type="textarea"
                  placeholder="请输入系统描述"
                  :rows="3"
                />
              </n-form-item>

              <n-form-item label="管理员邮箱">
                <n-input v-model:value="basicSettings.adminEmail" placeholder="请输入管理员邮箱" />
              </n-form-item>

              <n-form-item label="系统语言">
                <n-select
                  v-model:value="basicSettings.language"
                  :options="languageOptions"
                  placeholder="请选择系统语言"
                />
              </n-form-item>

              <n-form-item label="时区设置">
                <n-select
                  v-model:value="basicSettings.timezone"
                  :options="timezoneOptions"
                  placeholder="请选择时区"
                />
              </n-form-item>

              <n-form-item label="日期格式">
                <n-select
                  v-model:value="basicSettings.dateFormat"
                  :options="dateFormatOptions"
                  placeholder="请选择日期格式"
                />
              </n-form-item>
            </n-form>
          </div>
        </n-tab-pane>

        <!-- 安全设置 -->
        <n-tab-pane name="security" tab="安全设置">
          <div class="space-y-6 p-4">
            <n-form :model="securitySettings" label-placement="left" label-width="120">
              <n-form-item label="会话超时">
                <n-input-number
                  v-model:value="securitySettings.sessionTimeout"
                  :min="5"
                  :max="1440"
                  placeholder="分钟"
                />
                <template #suffix>分钟</template>
              </n-form-item>

              <n-form-item label="密码策略">
                <div class="space-y-3">
                  <n-checkbox v-model:checked="securitySettings.passwordPolicy.requireUppercase">
                    要求大写字母
                  </n-checkbox>
                  <n-checkbox v-model:checked="securitySettings.passwordPolicy.requireLowercase">
                    要求小写字母
                  </n-checkbox>
                  <n-checkbox v-model:checked="securitySettings.passwordPolicy.requireNumbers">
                    要求数字
                  </n-checkbox>
                  <n-checkbox v-model:checked="securitySettings.passwordPolicy.requireSpecialChars">
                    要求特殊字符
                  </n-checkbox>
                </div>
              </n-form-item>

              <n-form-item label="最小密码长度">
                <n-input-number
                  v-model:value="securitySettings.passwordPolicy.minLength"
                  :min="6"
                  :max="32"
                />
              </n-form-item>

              <n-form-item label="登录失败锁定">
                <n-switch v-model:value="securitySettings.loginLockout.enabled" />
              </n-form-item>

              <n-form-item label="最大失败次数" v-if="securitySettings.loginLockout.enabled">
                <n-input-number
                  v-model:value="securitySettings.loginLockout.maxAttempts"
                  :min="3"
                  :max="10"
                />
              </n-form-item>

              <n-form-item label="锁定时间" v-if="securitySettings.loginLockout.enabled">
                <n-input-number
                  v-model:value="securitySettings.loginLockout.lockoutDuration"
                  :min="5"
                  :max="60"
                />
                <template #suffix>分钟</template>
              </n-form-item>

              <n-form-item label="双因素认证">
                <n-switch v-model:value="securitySettings.twoFactorAuth" />
              </n-form-item>
            </n-form>
          </div>
        </n-tab-pane>

        <!-- 网络设置 -->
        <n-tab-pane name="network" tab="网络设置">
          <div class="space-y-6 p-4">
            <n-form :model="networkSettings" label-placement="left" label-width="120">
              <n-form-item label="默认端口">
                <n-input-number
                  v-model:value="networkSettings.defaultPort"
                  :min="1"
                  :max="65535"
                />
              </n-form-item>

              <n-form-item label="最大连接数">
                <n-input-number
                  v-model:value="networkSettings.maxConnections"
                  :min="1"
                  :max="10000"
                />
              </n-form-item>

              <n-form-item label="连接超时">
                <n-input-number
                  v-model:value="networkSettings.connectionTimeout"
                  :min="5"
                  :max="300"
                />
                <template #suffix>秒</template>
              </n-form-item>

              <n-form-item label="心跳间隔">
                <n-input-number
                  v-model:value="networkSettings.heartbeatInterval"
                  :min="10"
                  :max="300"
                />
                <template #suffix>秒</template>
              </n-form-item>

              <n-form-item label="默认加密算法">
                <n-select
                  v-model:value="networkSettings.defaultEncryption"
                  :options="encryptionOptions"
                  placeholder="请选择加密算法"
                />
              </n-form-item>

              <n-form-item label="压缩级别">
                <n-slider
                  v-model:value="networkSettings.compressionLevel"
                  :min="0"
                  :max="9"
                  :step="1"
                  :marks="compressionMarks"
                />
              </n-form-item>

              <n-form-item label="启用IPv6">
                <n-switch v-model:value="networkSettings.enableIPv6" />
              </n-form-item>

              <n-form-item label="启用UPnP">
                <n-switch v-model:value="networkSettings.enableUPnP" />
              </n-form-item>
            </n-form>
          </div>
        </n-tab-pane>

        <!-- 日志设置 -->
        <n-tab-pane name="logging" tab="日志设置">
          <div class="space-y-6 p-4">
            <n-form :model="loggingSettings" label-placement="left" label-width="120">
              <n-form-item label="日志级别">
                <n-select
                  v-model:value="loggingSettings.logLevel"
                  :options="logLevelOptions"
                  placeholder="请选择日志级别"
                />
              </n-form-item>

              <n-form-item label="日志保留天数">
                <n-input-number
                  v-model:value="loggingSettings.retentionDays"
                  :min="1"
                  :max="365"
                />
                <template #suffix>天</template>
              </n-form-item>

              <n-form-item label="最大日志文件大小">
                <n-input-number
                  v-model:value="loggingSettings.maxFileSize"
                  :min="1"
                  :max="1000"
                />
                <template #suffix>MB</template>
              </n-form-item>

              <n-form-item label="启用文件日志">
                <n-switch v-model:value="loggingSettings.enableFileLogging" />
              </n-form-item>

              <n-form-item label="启用系统日志">
                <n-switch v-model:value="loggingSettings.enableSyslog" />
              </n-form-item>

              <n-form-item label="系统日志服务器" v-if="loggingSettings.enableSyslog">
                <n-input v-model:value="loggingSettings.syslogServer" placeholder="请输入系统日志服务器地址" />
              </n-form-item>

              <n-form-item label="启用审计日志">
                <n-switch v-model:value="loggingSettings.enableAuditLog" />
              </n-form-item>
            </n-form>
          </div>
        </n-tab-pane>

        <!-- 通知设置 -->
        <n-tab-pane name="notification" tab="通知设置">
          <div class="space-y-6 p-4">
            <n-form :model="notificationSettings" label-placement="left" label-width="120">
              <n-form-item label="邮件通知">
                <n-switch v-model:value="notificationSettings.email.enabled" />
              </n-form-item>

              <div v-if="notificationSettings.email.enabled" class="ml-8 space-y-4">
                <n-form-item label="SMTP 服务器">
                  <n-input v-model:value="notificationSettings.email.smtpServer" placeholder="请输入 SMTP 服务器地址" />
                </n-form-item>

                <n-form-item label="SMTP 端口">
                  <n-input-number v-model:value="notificationSettings.email.smtpPort" :min="1" :max="65535" />
                </n-form-item>

                <n-form-item label="发件人邮箱">
                  <n-input v-model:value="notificationSettings.email.fromEmail" placeholder="请输入发件人邮箱" />
                </n-form-item>

                <n-form-item label="启用 SSL">
                  <n-switch v-model:value="notificationSettings.email.useSSL" />
                </n-form-item>
              </div>

              <n-form-item label="Webhook 通知">
                <n-switch v-model:value="notificationSettings.webhook.enabled" />
              </n-form-item>

              <div v-if="notificationSettings.webhook.enabled" class="ml-8 space-y-4">
                <n-form-item label="Webhook URL">
                  <n-input v-model:value="notificationSettings.webhook.url" placeholder="请输入 Webhook URL" />
                </n-form-item>

                <n-form-item label="密钥">
                  <n-input v-model:value="notificationSettings.webhook.secret" type="password" placeholder="请输入密钥" />
                </n-form-item>
              </div>

              <n-form-item label="通知事件">
                <div class="space-y-2">
                  <n-checkbox v-model:checked="notificationSettings.events.userLogin">
                    用户登录
                  </n-checkbox>
                  <n-checkbox v-model:checked="notificationSettings.events.userLogout">
                    用户登出
                  </n-checkbox>
                  <n-checkbox v-model:checked="notificationSettings.events.connectionEstablished">
                    连接建立
                  </n-checkbox>
                  <n-checkbox v-model:checked="notificationSettings.events.connectionLost">
                    连接断开
                  </n-checkbox>
                  <n-checkbox v-model:checked="notificationSettings.events.systemError">
                    系统错误
                  </n-checkbox>
                </div>
              </n-form-item>
            </n-form>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Icon } from '@iconify/vue'
import { useMessage, useDialog } from 'naive-ui'

const message = useMessage()
const dialog = useDialog()

const activeTab = ref('basic')
const saving = ref(false)

// 基本设置
const basicSettings = reactive({
  systemName: 'VPN 管理系统',
  systemDescription: '基于 Tinc 的 VPN 网络管理平台',
  adminEmail: '<EMAIL>',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD HH:mm:ss'
})

// 安全设置
const securitySettings = reactive({
  sessionTimeout: 30,
  passwordPolicy: {
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    minLength: 8
  },
  loginLockout: {
    enabled: true,
    maxAttempts: 5,
    lockoutDuration: 15
  },
  twoFactorAuth: false
})

// 网络设置
const networkSettings = reactive({
  defaultPort: 655,
  maxConnections: 1000,
  connectionTimeout: 30,
  heartbeatInterval: 60,
  defaultEncryption: 'aes-256-cbc',
  compressionLevel: 6,
  enableIPv6: true,
  enableUPnP: false
})

// 日志设置
const loggingSettings = reactive({
  logLevel: 'info',
  retentionDays: 30,
  maxFileSize: 100,
  enableFileLogging: true,
  enableSyslog: false,
  syslogServer: '',
  enableAuditLog: true
})

// 通知设置
const notificationSettings = reactive({
  email: {
    enabled: false,
    smtpServer: '',
    smtpPort: 587,
    fromEmail: '',
    useSSL: true
  },
  webhook: {
    enabled: false,
    url: '',
    secret: ''
  },
  events: {
    userLogin: true,
    userLogout: false,
    connectionEstablished: true,
    connectionLost: true,
    systemError: true
  }
})

// 选项数据
const languageOptions = [
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
  { label: '繁體中文', value: 'zh-TW' },
  { label: '日本語', value: 'ja-JP' }
]

const timezoneOptions = [
  { label: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
  { label: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
  { label: '纽约时间 (UTC-5)', value: 'America/New_York' },
  { label: '伦敦时间 (UTC+0)', value: 'Europe/London' },
  { label: 'UTC 时间', value: 'UTC' }
]

const dateFormatOptions = [
  { label: 'YYYY-MM-DD HH:mm:ss', value: 'YYYY-MM-DD HH:mm:ss' },
  { label: 'MM/DD/YYYY HH:mm:ss', value: 'MM/DD/YYYY HH:mm:ss' },
  { label: 'DD/MM/YYYY HH:mm:ss', value: 'DD/MM/YYYY HH:mm:ss' },
  { label: 'YYYY年MM月DD日 HH:mm:ss', value: 'YYYY年MM月DD日 HH:mm:ss' }
]

const encryptionOptions = [
  { label: 'AES-256-CBC', value: 'aes-256-cbc' },
  { label: 'AES-256-GCM', value: 'aes-256-gcm' },
  { label: 'ChaCha20-Poly1305', value: 'chacha20-poly1305' },
  { label: 'Blowfish', value: 'blowfish' }
]

const compressionMarks = {
  0: '无',
  3: '低',
  6: '中',
  9: '高'
}

const logLevelOptions = [
  { label: '调试 (Debug)', value: 'debug' },
  { label: '信息 (Info)', value: 'info' },
  { label: '警告 (Warning)', value: 'warning' },
  { label: '错误 (Error)', value: 'error' }
]

// 保存设置
const saveSettings = async () => {
  saving.value = true
  try {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 这里应该调用实际的 API 保存设置
    // await settingsApi.updateSettings({
    //   basic: basicSettings,
    //   security: securitySettings,
    //   network: networkSettings,
    //   logging: loggingSettings,
    //   notification: notificationSettings
    // })

    message.success('设置保存成功')
  } catch (error) {
    message.error('设置保存失败')
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  dialog.warning({
    title: '确认重置',
    content: '确定要重置所有设置为默认值吗？此操作不可恢复。',
    positiveText: '确定重置',
    negativeText: '取消',
    onPositiveClick: () => {
      // 重置为默认值
      Object.assign(basicSettings, {
        systemName: 'VPN 管理系统',
        systemDescription: '基于 Tinc 的 VPN 网络管理平台',
        adminEmail: '<EMAIL>',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        dateFormat: 'YYYY-MM-DD HH:mm:ss'
      })

      Object.assign(securitySettings, {
        sessionTimeout: 30,
        passwordPolicy: {
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
          minLength: 8
        },
        loginLockout: {
          enabled: true,
          maxAttempts: 5,
          lockoutDuration: 15
        },
        twoFactorAuth: false
      })

      Object.assign(networkSettings, {
        defaultPort: 655,
        maxConnections: 1000,
        connectionTimeout: 30,
        heartbeatInterval: 60,
        defaultEncryption: 'aes-256-cbc',
        compressionLevel: 6,
        enableIPv6: true,
        enableUPnP: false
      })

      Object.assign(loggingSettings, {
        logLevel: 'info',
        retentionDays: 30,
        maxFileSize: 100,
        enableFileLogging: true,
        enableSyslog: false,
        syslogServer: '',
        enableAuditLog: true
      })

      Object.assign(notificationSettings, {
        email: {
          enabled: false,
          smtpServer: '',
          smtpPort: 587,
          fromEmail: '',
          useSSL: true
        },
        webhook: {
          enabled: false,
          url: '',
          secret: ''
        },
        events: {
          userLogin: true,
          userLogout: false,
          connectionEstablished: true,
          connectionLost: true,
          systemError: true
        }
      })

      message.success('设置已重置为默认值')
    }
  })
}
</script>
