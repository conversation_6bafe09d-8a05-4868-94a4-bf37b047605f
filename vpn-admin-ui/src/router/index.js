import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store/auth'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { 
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    redirect: '/dashboard',
    component: () => import('@/layout/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: { 
          title: '仪表盘',
          icon: 'mdi:view-dashboard',
          requiresAuth: true
        }
      },
      {
        path: '/clients',
        name: 'Clients',
        component: () => import('@/views/clients/Index.vue'),
        meta: { 
          title: '客户端管理',
          icon: 'mdi:laptop',
          requiresAuth: true
        }
      },
      {
        path: '/clients/:id',
        name: 'ClientDetail',
        component: () => import('@/views/clients/Detail.vue'),
        meta: { 
          title: '客户端详情',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/servers',
        name: 'Servers',
        component: () => import('@/views/servers/Index.vue'),
        meta: { 
          title: '服务端管理',
          icon: 'mdi:server',
          requiresAuth: true
        }
      },
      {
        path: '/servers/:id',
        name: 'ServerDetail',
        component: () => import('@/views/servers/Detail.vue'),
        meta: { 
          title: '服务端详情',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/users/Index.vue'),
        meta: { 
          title: '用户管理',
          icon: 'mdi:account-group',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: '/roles',
        name: 'Roles',
        component: () => import('@/views/roles/Index.vue'),
        meta: { 
          title: '角色管理',
          icon: 'mdi:shield-account',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: '/configs',
        name: 'Configs',
        component: () => import('@/views/configs/Index.vue'),
        meta: { 
          title: '配置管理',
          icon: 'mdi:cog',
          requiresAuth: true
        }
      },
      {
        path: '/connections',
        name: 'Connections',
        component: () => import('@/views/connections/Index.vue'),
        meta: { 
          title: '连接监控',
          icon: 'mdi:connection',
          requiresAuth: true
        }
      },
      {
        path: '/nat',
        name: 'NAT',
        component: () => import('@/views/nat/Index.vue'),
        meta: { 
          title: 'NAT 管理',
          icon: 'mdi:network',
          requiresAuth: true
        }
      },
      {
        path: '/logs',
        name: 'Logs',
        component: () => import('@/views/logs/Index.vue'),
        meta: { 
          title: '日志中心',
          icon: 'mdi:text-box-search',
          requiresAuth: true
        }
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/settings/Index.vue'),
        meta: { 
          title: '系统设置',
          icon: 'mdi:settings',
          requiresAuth: true,
          roles: ['admin']
        }
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: { 
      title: '无权限访问',
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { 
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - VPN 管理系统` : 'VPN 管理系统'
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isLoggedIn) {
      next('/login')
      return
    }
    
    // 检查角色权限
    if (to.meta.roles && !authStore.hasRole(to.meta.roles)) {
      next('/403')
      return
    }
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isLoggedIn) {
    next('/dashboard')
    return
  }
  
  next()
})

export default router
