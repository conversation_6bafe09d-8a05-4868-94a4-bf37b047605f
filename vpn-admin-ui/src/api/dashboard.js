import request from './request'



export const dashboardApi = {
  // 获取仪表盘统计数据
  getStats() {
    return request.get('/api/dashboard/stats')
  },

  // 获取连接趋势数据
  getConnectionTrends(params) {
    return request.get('/api/dashboard/trends', { params })
  },

  // 获取 NAT 类型分布
  getNatDistribution() {
    return request.get('/api/dashboard/nat-distribution')
  },

  // 获取最近事件
  getRecentEvents(params) {
    return request.get('/api/dashboard/events', { params })
  },

  // 获取系统状态
  getSystemStatus() {
    return request.get('/api/dashboard/system-status')
  }
}
