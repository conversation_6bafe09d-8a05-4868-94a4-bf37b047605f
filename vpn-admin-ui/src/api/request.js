import axios from 'axios'
import { getToken, removeToken } from '@/utils/auth'
import router from '@/router'

// 创建一个全局的消息提示函数
let messageInstance = null

// 设置消息实例的函数，在 main.js 中调用
export const setMessageInstance = (instance) => {
  messageInstance = instance
}

// 全局消息提示函数
const showMessage = (type, content) => {
  if (messageInstance) {
    messageInstance[type](content)
  } else {
    // 降级到 console
    console.warn(`[${type.toUpperCase()}] ${content}`)
  }
}

// 创建 axios 实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: import.meta.env.VITE_API_TIMEOUT || 30000, // 增加到 30 秒
  headers: {
    'Content-Type': 'application/json'
  }
})

console.log('API 基础地址:', import.meta.env.VITE_API_BASE_URL)
console.log('所有环境变量:', import.meta.env)

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data)
    console.log('完整请求配置:', config)

    // 添加 token
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response
    
    // 检查业务状态码
    if (data.code !== undefined && data.code !== 0) {
      showMessage('error', data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
    
    return data
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          showMessage('error', '登录已过期，请重新登录')
          removeToken()
          router.push('/login')
          break
        case 403:
          showMessage('error', '没有权限访问')
          router.push('/403')
          break
        case 404:
          showMessage('error', '请求的资源不存在')
          break
        case 500:
          showMessage('error', '服务器内部错误')
          break
        default:
          showMessage('error', data?.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      if (error.code === 'ECONNABORTED') {
        showMessage('error', '请求超时，请检查网络连接或稍后重试')
      } else if (error.code === 'ERR_NETWORK') {
        showMessage('error', '网络错误，请检查后端服务是否启动')
      } else {
        showMessage('error', '网络连接失败，请检查网络')
      }
      console.error('网络错误详情:', error)
    } else {
      showMessage('error', '请求配置错误')
      console.error('请求配置错误:', error)
    }

    return Promise.reject(error)
  }
)

export default request
