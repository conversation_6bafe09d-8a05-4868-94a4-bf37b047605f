import request from './request'

/**
 * 服务端管理 API
 */
export const serversApi = {
  /**
   * 获取服务端列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getServers(params = {}) {
    console.log('获取服务端列表请求参数:', params)
    // 临时模拟数据，直到后端实现
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockServers = [
          {
            id: '1',
            name: '主服务器-01',
            publicIp: '************',
            port: 10001,
            subnetId: '1',
            status: 'online',
            region: 'east',
            connections: 25,
            cpuUsage: 45,
            memoryUsage: 60,
            maxConnections: 100,
            lastHeartbeat: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
            createdAt: new Date('2024-01-01')
          },
          {
            id: '2',
            name: '主服务器-02',
            publicIp: '************',
            port: 10001,
            subnetId: '1',
            status: 'online',
            region: 'north',
            connections: 18,
            cpuUsage: 32,
            memoryUsage: 48,
            maxConnections: 100,
            lastHeartbeat: new Date(Date.now() - 2 * 60 * 1000), // 2分钟前
            createdAt: new Date('2024-01-05')
          },
          {
            id: '3',
            name: '测试服务器-01',
            publicIp: '************',
            port: 10002,
            subnetId: '2',
            status: 'offline',
            region: 'south',
            connections: 0,
            cpuUsage: 0,
            memoryUsage: 0,
            maxConnections: 50,
            lastHeartbeat: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
            createdAt: new Date('2024-01-15')
          }
        ]

        resolve({
          code: 0,
          message: '获取成功',
          data: mockServers
        })
      }, 500)
    })
  },

  /**
   * 获取服务端详情
   * @param {string} id - 服务端ID
   * @returns {Promise}
   */
  getServer(id) {
    console.log('获取服务端详情，ID:', id)
    return request.get(`/api/servers/${id}`)
  },

  /**
   * 创建服务端
   * @param {Object} data - 服务端数据
   * @returns {Promise}
   */
  createServer(data) {
    console.log('创建服务端请求数据:', data)
    return request.post('/api/servers', data)
  },

  /**
   * 更新服务端
   * @param {string} id - 服务端ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateServer(id, data) {
    console.log('更新服务端请求，ID:', id, '数据:', data)
    return request.put(`/api/servers/${id}`, data)
  },

  /**
   * 删除服务端
   * @param {string} id - 服务端ID
   * @returns {Promise}
   */
  deleteServer(id) {
    console.log('删除服务端，ID:', id)
    return request.delete(`/api/servers/${id}`)
  },

  /**
   * 获取可用的服务端列表（用于下拉选择）
   * @returns {Promise}
   */
  getAvailableServers() {
    console.log('获取可用服务端列表')
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockServers = [
          {
            id: '1',
            name: '主服务器-01',
            publicIp: '************',
            port: 10001,
            subnetId: '1',
            status: 'online'
          },
          {
            id: '2',
            name: '主服务器-02',
            publicIp: '************',
            port: 10001,
            subnetId: '1',
            status: 'online'
          },
          {
            id: '3',
            name: '测试服务器-01',
            publicIp: '************',
            port: 10002,
            subnetId: '2',
            status: 'online'
          }
        ]

        resolve({
          code: 0,
          message: '获取成功',
          data: mockServers
        })
      }, 300)
    })
  },

  /**
   * 获取服务端统计信息
   * @returns {Promise}
   */
  getServerStats() {
    console.log('获取服务端统计信息')
    return request.get('/api/servers/stats')
  },

  /**
   * 获取连接趋势数据
   * @returns {Promise}
   */
  getConnectionTrends() {
    console.log('获取连接趋势数据')
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const trendData = Array.from({ length: 24 }, (_, i) => ({
          time: `${String(i).padStart(2, '0')}:00`,
          connections: Math.floor(Math.random() * 50) + 20 // 20-70 连接数
        }))

        resolve({
          code: 0,
          message: '获取成功',
          data: trendData
        })
      }, 300)
    })
  },

  /**
   * 启动服务端
   * @param {string} id - 服务端ID
   * @returns {Promise}
   */
  startServer(id) {
    console.log('启动服务端，ID:', id)
    return request.post(`/api/servers/${id}/start`)
  },

  /**
   * 停止服务端
   * @param {string} id - 服务端ID
   * @returns {Promise}
   */
  stopServer(id) {
    console.log('停止服务端，ID:', id)
    return request.post(`/api/servers/${id}/stop`)
  },

  /**
   * 重启服务端
   * @param {string} id - 服务端ID
   * @returns {Promise}
   */
  restartServer(id) {
    console.log('重启服务端，ID:', id)
    return request.post(`/api/servers/${id}/restart`)
  }
}
