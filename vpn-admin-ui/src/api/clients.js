import request from './request'

export const clientsApi = {
  // 获取客户端列表
  getClients(params) {
    return request.get('/api/nodes', { params })
  },

  // 获取客户端详情
  getClient(id) {
    return request.get(`/api/nodes/${id}`)
  },

  // 创建客户端
  createClient(data) {
    return request.post('/api/nodes', data)
  },

  // 更新客户端
  updateClient(id, data) {
    return request.put(`/api/nodes/${id}`, data)
  },

  // 删除客户端
  deleteClient(id) {
    return request.delete(`/api/nodes/${id}`)
  },

  // 测试连通性
  testConnection(id) {
    return request.post(`/api/nodes/${id}/test`)
  },

  // 重推配置
  pushConfig(id) {
    return request.post(`/api/nodes/${id}/push-config`)
  },

  // 强制断线
  disconnect(id) {
    return request.post(`/api/nodes/${id}/disconnect`)
  },

  // 获取客户端日志
  getClientLogs(id, params) {
    return request.get(`/api/nodes/${id}/logs`, { params })
  }
}
