import request from './request'

export const configsApi = {
  // 获取配置列表
  getConfigs(params) {
    return request.get('/api/configs', { params })
  },

  // 获取配置详情
  getConfig(id) {
    return request.get(`/api/configs/${id}`)
  },

  // 创建配置
  createConfig(data) {
    return request.post('/api/configs', data)
  },

  // 更新配置
  updateConfig(id, data) {
    return request.put(`/api/configs/${id}`, data)
  },

  // 删除配置
  deleteConfig(id) {
    return request.delete(`/api/configs/${id}`)
  },

  // 推送配置
  pushConfig(id, clientIds = []) {
    return request.post(`/api/configs/${id}/push`, { clientIds })
  },

  // 批量推送配置
  batchPushConfigs(configIds, clientIds = []) {
    return request.post('/api/configs/batch-push', { configIds, clientIds })
  },

  // 上传配置文件
  uploadConfig(file, metadata) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('metadata', JSON.stringify(metadata))
    
    return request.post('/api/configs/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 下载配置
  downloadConfig(id) {
    return request.get(`/api/configs/${id}/download`, {
      responseType: 'blob'
    })
  },

  // 批量下载配置
  batchDownloadConfigs(configIds) {
    return request.post('/api/configs/batch-download', { configIds }, {
      responseType: 'blob'
    })
  },

  // 获取配置版本历史
  getConfigVersions(id) {
    return request.get(`/api/configs/${id}/versions`)
  },

  // 回滚到指定版本
  rollbackConfig(id, versionId) {
    return request.post(`/api/configs/${id}/rollback`, { versionId })
  },

  // 对比配置版本
  compareConfigVersions(id, versionId1, versionId2) {
    return request.get(`/api/configs/${id}/compare`, {
      params: { version1: versionId1, version2: versionId2 }
    })
  },

  // 验证配置文件
  validateConfig(content, type) {
    return request.post('/api/configs/validate', { content, type })
  },

  // 获取配置模板
  getConfigTemplates(type) {
    return request.get('/api/configs/templates', { params: { type } })
  },

  // 应用配置模板
  applyConfigTemplate(templateId, variables) {
    return request.post(`/api/configs/templates/${templateId}/apply`, { variables })
  }
}
