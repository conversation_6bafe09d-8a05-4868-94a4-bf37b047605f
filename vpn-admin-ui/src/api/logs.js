import request from './request'

/**
 * 日志管理 API
 */
export const logsApi = {
  /**
   * 获取日志列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getLogs(params = {}) {
    console.log('获取日志列表请求参数:', params)
    // 临时返回空数据，避免 404 错误
    return Promise.resolve({
      code: 0,
      message: '日志管理 API 尚未实现',
      data: []
    })
  },

  /**
   * 获取日志详情
   * @param {string} id - 日志ID
   * @returns {Promise}
   */
  getLog(id) {
    console.log('获取日志详情，ID:', id)
    return request.get(`/api/logs/${id}`)
  },

  /**
   * 获取日志统计信息
   * @returns {Promise}
   */
  getLogStats() {
    console.log('获取日志统计信息')
    return request.get('/api/logs/stats')
  },

  /**
   * 获取日志趋势数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getLogTrends(params = {}) {
    console.log('获取日志趋势数据，参数:', params)
    return request.get('/api/logs/trends', { params })
  },

  /**
   * 清空日志
   * @returns {Promise}
   */
  clearLogs() {
    console.log('清空日志')
    return request.delete('/api/logs')
  },

  /**
   * 删除指定日志
   * @param {Array} ids - 日志ID列表
   * @returns {Promise}
   */
  deleteLogs(ids) {
    console.log('删除日志，IDs:', ids)
    return request.delete('/api/logs/batch', { data: { ids } })
  },

  /**
   * 导出日志
   * @param {Object} params - 导出参数
   * @returns {Promise}
   */
  exportLogs(params = {}) {
    console.log('导出日志，参数:', params)
    return request.get('/api/logs/export', { 
      params,
      responseType: 'blob'
    })
  },

  /**
   * 获取日志级别统计
   * @returns {Promise}
   */
  getLevelStats() {
    console.log('获取日志级别统计')
    return request.get('/api/logs/level-stats')
  },

  /**
   * 获取日志来源统计
   * @returns {Promise}
   */
  getSourceStats() {
    console.log('获取日志来源统计')
    return request.get('/api/logs/source-stats')
  }
}
