import request from './request'

/**
 * NAT 管理 API
 */
export const natApi = {
  /**
   * 获取 NAT 检测列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getNatDetections(params = {}) {
    console.log('获取 NAT 检测列表请求参数:', params)
    return request.get('/api/nat/detections', { params })
  },

  /**
   * 获取 NAT 检测详情
   * @param {string} id - 检测ID
   * @returns {Promise}
   */
  getNatDetection(id) {
    console.log('获取 NAT 检测详情，ID:', id)
    return request.get(`/api/nat/detections/${id}`)
  },

  /**
   * 执行 NAT 检测
   * @param {string} clientId - 客户端ID
   * @returns {Promise}
   */
  detectNat(clientId) {
    console.log('执行 NAT 检测，客户端ID:', clientId)
    return request.post(`/api/nat/detect/${clientId}`)
  },

  /**
   * 批量 NAT 检测
   * @param {Array} clientIds - 客户端ID列表
   * @returns {Promise}
   */
  batchDetectNat(clientIds) {
    console.log('批量 NAT 检测，客户端IDs:', clientIds)
    return request.post('/api/nat/batch-detect', { clientIds })
  },

  /**
   * 测试打洞
   * @param {string} clientId - 客户端ID
   * @returns {Promise}
   */
  testHolePunch(clientId) {
    console.log('测试打洞，客户端ID:', clientId)
    return request.post(`/api/nat/hole-punch/${clientId}`)
  },

  /**
   * 获取 NAT 统计信息
   * @returns {Promise}
   */
  getNatStats() {
    console.log('获取 NAT 统计信息')
    return request.get('/api/nat/stats')
  },

  /**
   * 获取 NAT 分布数据
   * @returns {Promise}
   */
  getNatDistribution() {
    console.log('获取 NAT 分布数据')
    return request.get('/api/nat/distribution')
  },

  /**
   * 获取打洞成功率趋势
   * @returns {Promise}
   */
  getHolePunchTrends() {
    console.log('获取打洞成功率趋势')
    return request.get('/api/nat/hole-punch-trends')
  },

  /**
   * 获取 NAT 策略配置
   * @returns {Promise}
   */
  getNatPolicies() {
    console.log('获取 NAT 策略配置')
    return request.get('/api/nat/policies')
  },

  /**
   * 创建 NAT 策略
   * @param {Object} data - 策略数据
   * @returns {Promise}
   */
  createNatPolicy(data) {
    console.log('创建 NAT 策略请求数据:', data)
    return request.post('/api/nat/policies', data)
  },

  /**
   * 更新 NAT 策略
   * @param {string} id - 策略ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateNatPolicy(id, data) {
    console.log('更新 NAT 策略请求，ID:', id, '数据:', data)
    return request.put(`/api/nat/policies/${id}`, data)
  },

  /**
   * 删除 NAT 策略
   * @param {string} id - 策略ID
   * @returns {Promise}
   */
  deleteNatPolicy(id) {
    console.log('删除 NAT 策略，ID:', id)
    return request.delete(`/api/nat/policies/${id}`)
  }
}
