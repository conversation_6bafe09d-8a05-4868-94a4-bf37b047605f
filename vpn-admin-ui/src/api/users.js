import request from './request'



export const usersApi = {
  // 获取用户列表
  getUsers(params) {
    return request.get('/api/users', { params })
  },

  // 获取用户详情
  getUser(id) {
    return request.get(`/api/users/${id}`)
  },

  // 创建用户
  createUser(data) {
    return request.post('/api/users', data)
  },

  // 更新用户
  updateUser(id, data) {
    return request.put(`/api/users/${id}`, data)
  },

  // 删除用户
  deleteUser(id) {
    return request.delete(`/api/users/${id}`)
  },

  // 批量删除用户
  batchDeleteUsers(ids) {
    return request.delete('/api/users/batch', { data: { ids } })
  },

  // 切换用户状态
  toggleUserStatus(id, status) {
    return request.patch(`/api/users/${id}/status`, { status })
  },

  // 重置用户密码
  resetPassword(id) {
    return request.post(`/api/users/${id}/reset-password`)
  },

  // 分配角色
  assignRole(id, roleId) {
    return request.post(`/api/users/${id}/assign-role`, { roleId })
  },

  // 获取用户权限
  getUserPermissions(id) {
    return request.get(`/api/users/${id}/permissions`)
  },

  // 导出用户数据
  exportUsers(params) {
    return request.get('/api/users/export', {
      params,
      responseType: 'blob'
    })
  }
}
