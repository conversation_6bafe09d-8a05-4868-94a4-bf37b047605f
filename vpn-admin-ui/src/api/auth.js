import request from './request'

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    password: 'admin123',
    role: 'admin',
    name: '系统管理员',
    email: '<EMAIL>',
    avatar: null,
    permissions: ['*']
  },
  {
    id: 2,
    username: 'operator',
    password: 'operator123',
    role: 'operator',
    name: '运维人员',
    email: '<EMAIL>',
    avatar: null,
    permissions: ['client:*', 'server:*', 'config:read', 'config:push']
  },
  {
    id: 3,
    username: 'viewer',
    password: 'viewer123',
    role: 'viewer',
    name: '查看者',
    email: '<EMAIL>',
    avatar: null,
    permissions: ['client:read', 'server:read', 'config:read']
  }
]

// 模拟登录函数
const mockLogin = (credentials) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const user = mockUsers.find(u =>
        u.username === credentials.username && u.password === credentials.password
      )

      if (user) {
        const token = `mock-token-${user.id}-${Date.now()}`
        const userInfo = {
          id: user.id,
          username: user.username,
          role: user.role,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          permissions: user.permissions
        }

        resolve({
          data: {
            token,
            user: userInfo
          }
        })
      } else {
        reject({
          response: {
            data: {
              message: '用户名或密码错误'
            }
          }
        })
      }
    }, 1000) // 模拟网络延迟
  })
}

// 模拟获取用户信息
const mockGetUserInfo = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 从 localStorage 获取当前用户信息
      const token = localStorage.getItem('token')
      if (token) {
        const userId = token.split('-')[2]
        const user = mockUsers.find(u => u.id.toString() === userId)
        if (user) {
          resolve({
            data: {
              id: user.id,
              username: user.username,
              role: user.role,
              name: user.name,
              email: user.email,
              avatar: user.avatar,
              permissions: user.permissions
            }
          })
          return
        }
      }

      // 默认返回管理员信息
      resolve({
        data: {
          id: 1,
          username: 'admin',
          role: 'admin',
          name: '系统管理员',
          email: '<EMAIL>',
          avatar: null,
          permissions: ['*']
        }
      })
    }, 500)
  })
}

export const authApi = {
  // 用户登录 - 使用模拟数据
  login(data) {
    // 开发环境使用模拟登录
    if (import.meta.env.DEV) {
      return mockLogin(data)
    }
    // 生产环境使用真实 API
    return request.post('/api/login', data)
  },

  // 获取用户信息 - 使用模拟数据
  getUserInfo() {
    // 开发环境使用模拟数据
    if (import.meta.env.DEV) {
      return mockGetUserInfo()
    }
    // 生产环境使用真实 API
    return request.get('/api/profile/me')
  },

  // 退出登录
  logout() {
    // 开发环境直接返回成功
    if (import.meta.env.DEV) {
      return Promise.resolve({ data: { message: '退出成功' } })
    }
    // 生产环境使用真实 API
    return request.post('/api/logout')
  },

  // 刷新 token
  refreshToken() {
    // 开发环境模拟刷新
    if (import.meta.env.DEV) {
      return Promise.resolve({
        data: {
          token: `mock-token-refresh-${Date.now()}`
        }
      })
    }
    // 生产环境使用真实 API
    return request.post('/api/refresh-token')
  }
}
