import request from './request'

/**
 * 子网管理 API
 */
export const subnetsApi = {
  /**
   * 获取子网列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getSubnets(params = {}) {
    console.log('获取子网列表请求参数:', params)
    return request.get('/api/subnets', { params })
  },

  /**
   * 获取子网详情
   * @param {string} id - 子网ID
   * @returns {Promise}
   */
  getSubnet(id) {
    console.log('获取子网详情，ID:', id)
    return request.get(`/api/subnets/${id}`)
  },

  /**
   * 创建子网
   * @param {Object} data - 子网数据
   * @returns {Promise}
   */
  createSubnet(data) {
    console.log('🚀 创建子网请求开始')
    console.log('请求数据:', data)
    console.log('请求URL: /api/subnets')
    console.log('Request实例baseURL:', request.defaults.baseURL)

    const promise = request.post('/api/subnets', data)

    promise.then(response => {
      console.log('✅ 创建子网请求成功:', response)
    }).catch(error => {
      console.error('❌ 创建子网请求失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
    })

    return promise
  },

  /**
   * 更新子网
   * @param {string} id - 子网ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateSubnet(id, data) {
    console.log('更新子网请求，ID:', id, '数据:', data)
    return request.put(`/api/subnets/${id}`, data)
  },

  /**
   * 更新子网状态
   * @param {string} id - 子网ID
   * @param {Object} data - 状态数据
   * @returns {Promise}
   */
  updateSubnetStatus(id, data) {
    console.log('更新子网状态，ID:', id, '数据:', data)
    return request.patch(`/api/subnets/${id}/status`, data)
  },

  /**
   * 删除子网
   * @param {string} id - 子网ID
   * @returns {Promise}
   */
  deleteSubnet(id) {
    console.log('删除子网，ID:', id)
    return request.delete(`/api/subnets/${id}`)
  },

  /**
   * 获取子网统计信息
   * @returns {Promise}
   */
  getSubnetStats() {
    console.log('获取子网统计信息')
    return request.get('/api/subnets/stats')
  },

  /**
   * 获取可用的子网列表（用于下拉选择）
   * @returns {Promise}
   */
  getAvailableSubnets() {
    console.log('获取可用子网列表')
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockSubnets = [
          {
            id: '1',
            name: '主网络',
            cidr: '10.10.1.0/24',
            port: 10001,
            status: 'active'
          },
          {
            id: '2',
            name: '测试网络',
            cidr: '10.10.2.0/24',
            port: 10002,
            status: 'active'
          }
        ]

        resolve({
          code: 0,
          message: '获取成功',
          data: mockSubnets
        })
      }, 300)
    })
  },

  /**
   * 验证子网配置
   * @param {Object} data - 子网配置数据
   * @returns {Promise}
   */
  validateSubnet(data) {
    console.log('验证子网配置:', data)
    return request.post('/api/subnets/validate', data)
  }
}
