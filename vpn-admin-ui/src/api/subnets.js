import request from './request'

/**
 * 子网管理 API
 */
export const subnetsApi = {
  /**
   * 获取子网列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getSubnets(params = {}) {
    console.log('获取子网列表请求参数:', params)
    // 临时模拟数据，直到后端实现
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockSubnets = [
          {
            id: '1',
            name: '主网络',
            cidr: '*********/24',
            port: 10001,
            mtu: 1500,
            identifier: 'main-net',
            description: '主要的 VPN 网络',
            status: 'active',
            serverCount: 3,
            clientCount: 15,
            createdAt: new Date('2024-01-01')
          },
          {
            id: '2',
            name: '测试网络',
            cidr: '*********/24',
            port: 10002,
            mtu: 1500,
            identifier: 'test-net',
            description: '测试环境网络',
            status: 'active',
            serverCount: 1,
            clientCount: 5,
            createdAt: new Date('2024-01-15')
          }
        ]

        resolve({
          code: 0,
          message: '获取成功',
          data: mockSubnets
        })
      }, 500)
    })
  },

  /**
   * 获取子网详情
   * @param {string} id - 子网ID
   * @returns {Promise}
   */
  getSubnet(id) {
    console.log('获取子网详情，ID:', id)
    return request.get(`/api/subnets/${id}`)
  },

  /**
   * 创建子网
   * @param {Object} data - 子网数据
   * @returns {Promise}
   */
  createSubnet(data) {
    console.log('创建子网请求数据:', data)
    return request.post('/api/subnets', data)
  },

  /**
   * 更新子网
   * @param {string} id - 子网ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateSubnet(id, data) {
    console.log('更新子网请求，ID:', id, '数据:', data)
    return request.put(`/api/subnets/${id}`, data)
  },

  /**
   * 更新子网状态
   * @param {string} id - 子网ID
   * @param {Object} data - 状态数据
   * @returns {Promise}
   */
  updateSubnetStatus(id, data) {
    console.log('更新子网状态，ID:', id, '数据:', data)
    return request.patch(`/api/subnets/${id}/status`, data)
  },

  /**
   * 删除子网
   * @param {string} id - 子网ID
   * @returns {Promise}
   */
  deleteSubnet(id) {
    console.log('删除子网，ID:', id)
    return request.delete(`/api/subnets/${id}`)
  },

  /**
   * 获取子网统计信息
   * @returns {Promise}
   */
  getSubnetStats() {
    console.log('获取子网统计信息')
    return request.get('/api/subnets/stats')
  },

  /**
   * 获取可用的子网列表（用于下拉选择）
   * @returns {Promise}
   */
  getAvailableSubnets() {
    console.log('获取可用子网列表')
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockSubnets = [
          {
            id: '1',
            name: '主网络',
            cidr: '*********/24',
            port: 10001,
            status: 'active'
          },
          {
            id: '2',
            name: '测试网络',
            cidr: '*********/24',
            port: 10002,
            status: 'active'
          }
        ]

        resolve({
          code: 0,
          message: '获取成功',
          data: mockSubnets
        })
      }, 300)
    })
  },

  /**
   * 验证子网配置
   * @param {Object} data - 子网配置数据
   * @returns {Promise}
   */
  validateSubnet(data) {
    console.log('验证子网配置:', data)
    return request.post('/api/subnets/validate', data)
  }
}
