import api from './index'

/**
 * 子网管理 API
 */
export const subnetsApi = {
  /**
   * 获取子网列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getSubnets(params = {}) {
    console.log('获取子网列表请求参数:', params)
    return api.get('/api/subnets', { params })
  },

  /**
   * 获取子网详情
   * @param {string} id - 子网ID
   * @returns {Promise}
   */
  getSubnet(id) {
    console.log('获取子网详情，ID:', id)
    return api.get(`/api/subnets/${id}`)
  },

  /**
   * 创建子网
   * @param {Object} data - 子网数据
   * @returns {Promise}
   */
  createSubnet(data) {
    console.log('创建子网请求数据:', data)
    return api.post('/api/subnets', data)
  },

  /**
   * 更新子网
   * @param {string} id - 子网ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateSubnet(id, data) {
    console.log('更新子网请求，ID:', id, '数据:', data)
    return api.put(`/api/subnets/${id}`, data)
  },

  /**
   * 更新子网状态
   * @param {string} id - 子网ID
   * @param {Object} data - 状态数据
   * @returns {Promise}
   */
  updateSubnetStatus(id, data) {
    console.log('更新子网状态，ID:', id, '数据:', data)
    return api.patch(`/api/subnets/${id}/status`, data)
  },

  /**
   * 删除子网
   * @param {string} id - 子网ID
   * @returns {Promise}
   */
  deleteSubnet(id) {
    console.log('删除子网，ID:', id)
    return api.delete(`/api/subnets/${id}`)
  },

  /**
   * 获取子网统计信息
   * @returns {Promise}
   */
  getSubnetStats() {
    console.log('获取子网统计信息')
    return api.get('/api/subnets/stats')
  },

  /**
   * 获取可用的子网列表（用于下拉选择）
   * @returns {Promise}
   */
  getAvailableSubnets() {
    console.log('获取可用子网列表')
    return api.get('/api/subnets/available')
  },

  /**
   * 验证子网配置
   * @param {Object} data - 子网配置数据
   * @returns {Promise}
   */
  validateSubnet(data) {
    console.log('验证子网配置:', data)
    return api.post('/api/subnets/validate', data)
  }
}
