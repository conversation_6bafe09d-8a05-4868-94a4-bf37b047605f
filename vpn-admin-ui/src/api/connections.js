import request from './request'

/**
 * 连接管理 API
 */
export const connectionsApi = {
  /**
   * 获取连接列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getConnections(params = {}) {
    console.log('获取连接列表请求参数:', params)
    return request.get('/api/connections', { params })
  },

  /**
   * 获取连接详情
   * @param {string} id - 连接ID
   * @returns {Promise}
   */
  getConnection(id) {
    console.log('获取连接详情，ID:', id)
    return request.get(`/api/connections/${id}`)
  },

  /**
   * 获取连接统计信息
   * @returns {Promise}
   */
  getStats() {
    console.log('获取连接统计信息')
    // 临时返回默认统计数据，避免 404 错误
    return Promise.resolve({
      code: 0,
      message: '连接统计 API 尚未实现',
      data: {
        activeConnections: 0,
        totalTraffic: '0 GB',
        avgLatency: '0ms',
        errorConnections: 0,
        connectionsTrend: 0,
        trafficTrend: 0,
        latencyTrend: 0,
        errorTrend: 0
      }
    })
  },

  /**
   * 获取连接趋势数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getTrends(params = {}) {
    console.log('获取连接趋势数据，参数:', params)
    return request.get('/api/connections/trends', { params })
  },

  /**
   * 终止连接
   * @param {string} id - 连接ID
   * @returns {Promise}
   */
  terminateConnection(id) {
    console.log('终止连接，ID:', id)
    return request.post(`/api/connections/${id}/terminate`)
  },

  /**
   * 批量终止连接
   * @param {Array} ids - 连接ID列表
   * @returns {Promise}
   */
  batchTerminate(ids) {
    console.log('批量终止连接，IDs:', ids)
    return request.post('/api/connections/batch-terminate', { ids })
  },

  /**
   * 获取流量统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getTrafficStats(params = {}) {
    console.log('获取流量统计，参数:', params)
    return request.get('/api/connections/traffic', { params })
  },

  /**
   * 获取延迟统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getLatencyStats(params = {}) {
    console.log('获取延迟统计，参数:', params)
    return request.get('/api/connections/latency', { params })
  }
}
