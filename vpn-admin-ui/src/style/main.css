@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    margin: 0;
    padding: 0;
    background-color: #f8fafc;
    color: #1f2937;
    line-height: 1.6;
  }
  
  /* 深色模式 */
  .dark body {
    background-color: #0f172a;
    color: #f1f5f9;
  }
}

/* 组件样式 */
@layer components {
  .page-container {
    @apply min-h-screen flex justify-center;
  }
  
  .layout-wrapper {
    @apply flex gap-[10px] w-full max-w-[1440px];
  }
  
  .sidebar {
    @apply w-[240px] flex-shrink-0;
  }
  
  .main-content {
    @apply flex-1 min-h-screen p-6;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-md p-6;
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-md 
           hover:scale-105 hover:shadow-lg transition-all duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md 
           hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200;
  }
  
  .status-online {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
           bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
  }
  
  .status-offline {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
           bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
  }
  
  .status-connecting {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
           bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
  }
}

/* 工具类 */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  .glass-effect {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-800/80;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}
