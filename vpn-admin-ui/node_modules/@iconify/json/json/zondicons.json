{"prefix": "zondicons", "info": {"name": "Zondicons", "total": 297, "version": "0.1.0", "author": {"name": "<PERSON>", "url": "https://github.com/dukestreetstudio/zondicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/dukestreetstudio/zondicons/blob/master/LICENSE"}, "samples": ["copy", "hand-stop", "mouse", "arrow-thick-down", "send", "checkmark"], "height": 20, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "lastModified": **********, "icons": {"add-outline": {"body": "<path fill=\"currentColor\" d=\"M11 9h4v2h-4v4H9v-4H5V9h4V5h2zm-1 11a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16\"/>"}, "add-solid": {"body": "<path fill=\"currentColor\" d=\"M11 9V5H9v4H5v2h4v4h2v-4h4V9zm-1 11a10 10 0 1 1 0-20a10 10 0 0 1 0 20\"/>"}, "adjust": {"body": "<path fill=\"currentColor\" d=\"M10 2v16a8 8 0 1 0 0-16m0 18a10 10 0 1 1 0-20a10 10 0 0 1 0 20\"/>"}, "airplane": {"body": "<path fill=\"currentColor\" d=\"M8.4 12H2.8L1 15H0V5h1l1.8 3h5.6L6 0h2l4.8 8H18a2 2 0 1 1 0 4h-5.2L8 20H6z\"/>"}, "album": {"body": "<path fill=\"currentColor\" d=\"M0 0h20v20H0zm10 18a8 8 0 1 0 0-16a8 8 0 0 0 0 16m0-5a3 3 0 1 1 0-6a3 3 0 0 1 0 6\"/>"}, "align-center": {"body": "<path fill=\"currentColor\" d=\"M1 1h18v2H1zm0 8h18v2H1zm0 8h18v2H1zM4 5h12v2H4zm0 8h12v2H4z\"/>"}, "align-justified": {"body": "<path fill=\"currentColor\" d=\"M1 1h18v2H1zm0 8h18v2H1zm0 8h18v2H1zM1 5h18v2H1zm0 8h18v2H1z\"/>"}, "align-left": {"body": "<path fill=\"currentColor\" d=\"M1 1h18v2H1zm0 8h18v2H1zm0 8h18v2H1zM1 5h12v2H1zm0 8h12v2H1z\"/>"}, "align-right": {"body": "<path fill=\"currentColor\" d=\"M1 1h18v2H1zm0 8h18v2H1zm0 8h18v2H1zM7 5h12v2H7zm0 8h12v2H7z\"/>"}, "anchor": {"body": "<path fill=\"currentColor\" d=\"M4.34 15.66A7.97 7.97 0 0 0 9 17.94V10H5V8h4V5.83a3 3 0 1 1 2 0V8h4v2h-4v7.94a7.97 7.97 0 0 0 4.66-2.28l-1.42-1.42h5.66l-2.83 2.83a10 10 0 0 1-14.14 0L.1 14.24h5.66zM10 4a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "announcement": {"body": "<path fill=\"currentColor\" d=\"M3 6c0-1.1.9-2 2-2h8l4-4h2v16h-2l-4-4H5a2 2 0 0 1-2-2H1V6zm8 9v5H8l-1.67-5H5v-2h8v2z\"/>"}, "apparel": {"body": "<path fill=\"currentColor\" d=\"M7 0H6L0 3v6l4-1v12h12V8l4 1V3l-6-3h-1a3 3 0 0 1-6 0\"/>"}, "arrow-down": {"body": "<path fill=\"currentColor\" d=\"m9 16.172l-6.071-6.071l-1.414 1.414L10 20l.707-.707l7.778-7.778l-1.414-1.414L11 16.172V0H9z\"/>"}, "arrow-left": {"body": "<path fill=\"currentColor\" d=\"m3.828 9l6.071-6.071l-1.414-1.414L0 10l.707.707l7.778 7.778l1.414-1.414L3.828 11H20V9z\"/>"}, "arrow-outline-down": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16m-2-8V5h4v5h3l-5 5l-5-5z\"/>"}, "arrow-outline-left": {"body": "<path fill=\"currentColor\" d=\"M0 10a10 10 0 1 1 20 0a10 10 0 0 1-20 0m2 0a8 8 0 1 0 16 0a8 8 0 0 0-16 0m8-2h5v4h-5v3l-5-5l5-5z\"/>"}, "arrow-outline-right": {"body": "<path fill=\"currentColor\" d=\"M20 10a10 10 0 1 1-20 0a10 10 0 0 1 20 0m-2 0a8 8 0 1 0-16 0a8 8 0 0 0 16 0m-8 2H5V8h5V5l5 5l-5 5z\"/>"}, "arrow-outline-up": {"body": "<path fill=\"currentColor\" d=\"M10 0a10 10 0 1 1 0 20a10 10 0 0 1 0-20m0 2a8 8 0 1 0 0 16a8 8 0 0 0 0-16m2 8v5H8v-5H5l5-5l5 5z\"/>"}, "arrow-right": {"body": "<path fill=\"currentColor\" d=\"m16.172 9l-6.071-6.071l1.414-1.414L20 10l-.707.707l-7.778 7.778l-1.414-1.414L16.172 11H0V9z\"/>"}, "arrow-thick-down": {"body": "<path fill=\"currentColor\" d=\"M7 10V2h6v8h5l-8 8l-8-8z\"/>"}, "arrow-thick-left": {"body": "<path fill=\"currentColor\" d=\"M10 13h8V7h-8V2l-8 8l8 8z\"/>"}, "arrow-thick-right": {"body": "<path fill=\"currentColor\" d=\"M10 7H2v6h8v5l8-8l-8-8z\"/>"}, "arrow-thick-up": {"body": "<path fill=\"currentColor\" d=\"M7 10v8h6v-8h5l-8-8l-8 8z\"/>"}, "arrow-thin-down": {"body": "<path fill=\"currentColor\" d=\"m9 16.172l-6.071-6.071l-1.414 1.414L10 20l.707-.707l7.778-7.778l-1.414-1.414L11 16.172V0H9z\"/>"}, "arrow-thin-left": {"body": "<path fill=\"currentColor\" d=\"m3.828 9l6.071-6.071l-1.414-1.414L0 10l.707.707l7.778 7.778l1.414-1.414L3.828 11H20V9z\"/>"}, "arrow-thin-right": {"body": "<path fill=\"currentColor\" d=\"m16.172 9l-6.071-6.071l1.414-1.414L20 10l-.707.707l-7.778 7.778l-1.414-1.414L16.172 11H0V9z\"/>"}, "arrow-thin-up": {"body": "<path fill=\"currentColor\" d=\"M9 3.828L2.929 9.899L1.515 8.485L10 0l.707.707l7.778 7.778l-1.414 1.414L11 3.828V20H9z\"/>"}, "arrow-up": {"body": "<path fill=\"currentColor\" d=\"M9 3.828L2.929 9.899L1.515 8.485L10 0l.707.707l7.778 7.778l-1.414 1.414L11 3.828V20H9z\"/>"}, "artist": {"body": "<path fill=\"currentColor\" d=\"m15.75 8l-3.74-3.75a3.99 3.99 0 0 1 6.82-3.08A4 4 0 0 1 15.75 8m-13.9 7.3l9.2-9.19l2.83 2.83l-9.2 9.2l-2.82-2.84zm-1.4 2.83l2.11-2.12l1.42 1.42l-2.12 2.12l-1.42-1.42zM10 15l2-2v7h-2z\"/>"}, "at-symbol": {"body": "<path fill=\"currentColor\" d=\"M13.6 13.47A4.99 4.99 0 0 1 5 10a5 5 0 0 1 8-4V5h2v6.5a1.5 1.5 0 0 0 3 0V10a8 8 0 1 0-4.42 7.16l.9 1.79A10 10 0 1 1 20 10h-.18h.17v1.5a3.5 3.5 0 0 1-6.4 1.97zM10 13a3 3 0 1 0 0-6a3 3 0 0 0 0 6\"/>"}, "attachment": {"body": "<path fill=\"currentColor\" d=\"M15 3H7a7 7 0 1 0 0 14h8v-2H7A5 5 0 0 1 7 5h8a3 3 0 0 1 0 6H7a1 1 0 0 1 0-2h8V7H7a3 3 0 1 0 0 6h8a5 5 0 0 0 0-10\"/>"}, "backspace": {"body": "<path fill=\"currentColor\" d=\"m0 10l7-7h13v14H7zm14.41 0l2.13-2.12l-1.42-1.42L13 8.6l-2.12-2.13l-1.42 1.42L11.6 10l-2.13 2.12l1.42 1.42L13 11.4l2.12 2.13l1.42-1.42L14.4 10z\"/>"}, "backward": {"body": "<path fill=\"currentColor\" d=\"M19 5v10l-9-5zm-9 0v10l-9-5z\"/>"}, "backward-step": {"body": "<path fill=\"currentColor\" d=\"M4 5h3v10H4zm12 0v10l-9-5z\"/>"}, "badge": {"body": "<path fill=\"currentColor\" d=\"M10 12a6 6 0 1 1 0-12a6 6 0 0 1 0 12m0-3a3 3 0 1 0 0-6a3 3 0 0 0 0 6m4 2.75V20l-4-4l-4 4v-8.25a6.97 6.97 0 0 0 8 0\"/>"}, "battery-full": {"body": "<path fill=\"currentColor\" d=\"M0 6c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 0v8h16V6zm1 1h4v6H3zm5 0h4v6H8zm5 0h4v6h-4z\"/>"}, "battery-half": {"body": "<path fill=\"currentColor\" d=\"M0 6c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 0v8h16V6zm1 1h4v6H3zm5 0h4v6H8z\"/>"}, "battery-low": {"body": "<path fill=\"currentColor\" d=\"M0 6c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 0v8h16V6zm1 1h4v6H3z\"/>"}, "beverage": {"body": "<path fill=\"currentColor\" d=\"M9 18v-7L0 2V0h20v2l-9 9v7l5 1v1H4v-1zm2-10a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/>"}, "block": {"body": "<path fill=\"currentColor\" d=\"M0 10a10 10 0 1 1 20 0a10 10 0 0 1-20 0m16.32-4.9L5.09 16.31A8 8 0 0 0 16.32 5.09zm-1.41-1.42A8 8 0 0 0 3.68 14.91z\"/>"}, "bluetooth": {"body": "<path fill=\"currentColor\" d=\"m9.41 0l6 6l-4 4l4 4l-6 6H9v-7.59l-3.3 3.3l-1.4-1.42L8.58 10l-4.3-4.3L5.7 4.3L9 7.58V0zM11 4.41V7.6L12.59 6zM12.59 14L11 12.41v3.18z\"/>"}, "bolt": {"body": "<path fill=\"currentColor\" d=\"M13 8V0L8.11 5.87L3 12h4v8L17 8z\"/>"}, "book-reference": {"body": "<path fill=\"currentColor\" d=\"M6 4H5a1 1 0 1 1 0-2h11V1a1 1 0 0 0-1-1H4a2 2 0 0 0-2 2v16c0 1.1.9 2 2 2h12a2 2 0 0 0 2-2V5a1 1 0 0 0-1-1h-7v8l-2-2l-2 2z\"/>"}, "bookmark": {"body": "<path fill=\"currentColor\" d=\"M2 2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v18l-8-4l-8 4z\"/>"}, "bookmark-copy-2": {"body": "<path fill=\"currentColor\" d=\"M18 12v1H8v5l-6-6l6-6v5h8V2h2z\"/>"}, "bookmark-copy-3": {"body": "<path fill=\"currentColor\" d=\"M3.5 13H12v5l6-6l-6-6v5H4V2H2v11z\"/>"}, "bookmark-outline": {"body": "<path fill=\"currentColor\" d=\"M2 2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v18l-8-4l-8 4zm2 0v15l6-3l6 3V2z\"/>"}, "bookmark-outline-add": {"body": "<path fill=\"currentColor\" d=\"M2 2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v18l-8-4l-8 4zm2 0v15l6-3l6 3V2zm5 5V5h2v2h2v2h-2v2H9V9H7V7z\"/>"}, "border-all": {"body": "<path fill=\"currentColor\" d=\"M11 11v6h6v-6zm0-2h6V3h-6zm-2 2H3v6h6zm0-2V3H3v6zm-8 9V1h18v18H1z\"/>"}, "border-bottom": {"body": "<path fill=\"currentColor\" d=\"M1 1h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h18v2H1zM5 1h2v2H5zm0 8h2v2H5zm4-8h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm4-12h2v2h-2zm0 8h2v2h-2zm4-8h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2z\"/>"}, "border-horizontal": {"body": "<path fill=\"currentColor\" d=\"M1 1h2v2H1zm0 4h2v2H1zm0 4h18v2H1zm0 4h2v2H1zm0 4h2v2H1zM5 1h2v2H5zm0 16h2v2H5zM9 1h2v2H9zm0 4h2v2H9zm0 8h2v2H9zm0 4h2v2H9zm4-16h2v2h-2zm0 16h2v2h-2zm4-16h2v2h-2zm0 4h2v2h-2zm0 8h2v2h-2zm0 4h2v2h-2z\"/>"}, "border-inner": {"body": "<path fill=\"currentColor\" d=\"M9 9V1h2v8h8v2h-8v8H9v-8H1V9zM1 1h2v2H1zm0 4h2v2H1zm0 8h2v2H1zm0 4h2v2H1zM5 1h2v2H5zm0 16h2v2H5zm8-16h2v2h-2zm0 16h2v2h-2zm4-16h2v2h-2zm0 4h2v2h-2zm0 8h2v2h-2zm0 4h2v2h-2z\"/>"}, "border-left": {"body": "<path fill=\"currentColor\" d=\"M1 1h2v18H1zm4 0h2v2H5zm0 8h2v2H5zm0 8h2v2H5zM9 1h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm4-16h2v2h-2zm0 8h2v2h-2zm0 8h2v2h-2zm4-16h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2z\"/>"}, "border-none": {"body": "<path fill=\"currentColor\" d=\"M1 1h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zM5 1h2v2H5zm0 8h2v2H5zm0 8h2v2H5zM9 1h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm4-16h2v2h-2zm0 8h2v2h-2zm0 8h2v2h-2zm4-16h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2z\"/>"}, "border-outer": {"body": "<path fill=\"currentColor\" d=\"M2 19H1V1h18v18zm1-2h14V3H3zm10-8h2v2h-2zM9 9h2v2H9zM5 9h2v2H5zm4-4h2v2H9zm0 8h2v2H9z\"/>"}, "border-right": {"body": "<path fill=\"currentColor\" d=\"M5 1h2v2H5zm0 8h2v2H5zm0 8h2v2H5zM9 1h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm4-16h2v2h-2zm0 8h2v2h-2zm0 8h2v2h-2zM1 1h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zM17 1h2v18h-2z\"/>"}, "border-top": {"body": "<path fill=\"currentColor\" d=\"M1 1h18v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm4-8h2v2H5zm0 8h2v2H5zM9 5h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm0 4h2v2H9zm4-8h2v2h-2zm0 8h2v2h-2zm4-12h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2z\"/>"}, "border-vertical": {"body": "<path fill=\"currentColor\" d=\"M1 1h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zm0 4h2v2H1zM5 1h2v2H5zm0 8h2v2H5zm0 8h2v2H5zM9 1h2v18H9zm4 0h2v2h-2zm0 8h2v2h-2zm0 8h2v2h-2zm4-16h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2zm0 4h2v2h-2z\"/>"}, "box": {"body": "<path fill=\"currentColor\" d=\"M0 2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v2H0zm1 3h18v13a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2zm6 2v2h6V7z\"/>"}, "brightness-down": {"body": "<path fill=\"currentColor\" d=\"M10 13a3 3 0 1 1 0-6a3 3 0 0 1 0 6M9 4a1 1 0 1 1 2 0a1 1 0 1 1-2 0m4.54 1.05a1 1 0 1 1 1.41 1.41a1 1 0 1 1-1.41-1.41M16 9a1 1 0 1 1 0 2a1 1 0 1 1 0-2m-1.05 4.54a1 1 0 1 1-1.41 1.41a1 1 0 1 1 1.41-1.41M11 16a1 1 0 1 1-2 0a1 1 0 1 1 2 0m-4.54-1.05a1 1 0 1 1-1.41-1.41a1 1 0 1 1 1.41 1.41M4 11a1 1 0 1 1 0-2a1 1 0 1 1 0 2m1.05-4.54a1 1 0 1 1 1.41-1.41a1 1 0 1 1-1.41 1.41\"/>"}, "brightness-up": {"body": "<path fill=\"currentColor\" d=\"M10 14a4 4 0 1 1 0-8a4 4 0 0 1 0 8M9 1a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0zm6.65 1.94a1 1 0 1 1 1.41 1.41l-1.4 1.4a1 1 0 1 1-1.41-1.41zM18.99 9a1 1 0 1 1 0 2h-1.98a1 1 0 1 1 0-2zm-1.93 6.65a1 1 0 1 1-1.41 1.41l-1.4-1.4a1 1 0 1 1 1.41-1.41zM11 18.99a1 1 0 1 1-2 0v-1.98a1 1 0 1 1 2 0zm-6.65-1.93a1 1 0 1 1-1.41-1.41l1.4-1.4a1 1 0 1 1 1.41 1.41zM1.01 11a1 1 0 1 1 0-2h1.98a1 1 0 1 1 0 2zm1.93-6.65a1 1 0 1 1 1.41-1.41l1.4 1.4a1 1 0 1 1-1.41 1.41z\"/>"}, "browser-window": {"body": "<path fill=\"currentColor\" d=\"M0 3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v12h16V5z\"/>"}, "browser-window-new": {"body": "<path fill=\"currentColor\" d=\"M9 10V8h2v2h2v2h-2v2H9v-2H7v-2zM0 3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v12h16V5z\"/>"}, "browser-window-open": {"body": "<path fill=\"currentColor\" d=\"M0 3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v12h16V5zm8 3l4 5H6z\"/>"}, "bug": {"body": "<path fill=\"currentColor\" d=\"m15.3 14.89l2.77 2.77a1 1 0 0 1 0 1.41a1 1 0 0 1-1.41 0l-2.59-2.58A6 6 0 0 1 11 18V9.04a1 1 0 0 0-2 0V18a6 6 0 0 1-3.07-1.51l-2.59 2.58a1 1 0 0 1-1.41 0a1 1 0 0 1 0-1.41l2.77-2.77A6 6 0 0 1 4.07 13H1a1 1 0 1 1 0-2h3V8.41L.93 5.34a1 1 0 0 1 0-1.41a1 1 0 0 1 1.41 0l2.1 2.1h11.12l2.1-2.1a1 1 0 0 1 1.41 0a1 1 0 0 1 0 1.41L16 8.41V11h3a1 1 0 1 1 0 2h-3.07c-.1.67-.32 1.31-.63 1.89M15 5H5a5 5 0 1 1 10 0\"/>"}, "buoy": {"body": "<path fill=\"currentColor\" d=\"M17.16 6.42a8.03 8.03 0 0 0-3.58-3.58l-1.34 2.69a5.02 5.02 0 0 1 2.23 2.23zm0 7.16l-2.69-1.34a5.02 5.02 0 0 1-2.23 2.23l1.34 2.69a8.03 8.03 0 0 0 3.58-3.58M6.42 2.84a8.03 8.03 0 0 0-3.58 3.58l2.69 1.34a5.02 5.02 0 0 1 2.23-2.23zM2.84 13.58a8.03 8.03 0 0 0 3.58 3.58l1.34-2.69a5.02 5.02 0 0 1-2.23-2.23zM10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-7a3 3 0 1 0 0-6a3 3 0 0 0 0 6\"/>"}, "calculator": {"body": "<path fill=\"currentColor\" d=\"M2 2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm3 1v2h10V3zm0 4v2h2V7zm4 0v2h2V7zm4 0v2h2V7zm-8 4v2h2v-2zm4 0v2h2v-2zm4 0v6h2v-6zm-8 4v2h2v-2zm4 0v2h2v-2z\"/>"}, "calendar": {"body": "<path fill=\"currentColor\" d=\"M1 4c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2zm2 2v12h14V6zm2-6h2v2H5zm8 0h2v2h-2zM5 9h2v2H5zm0 4h2v2H5zm4-4h2v2H9zm0 4h2v2H9zm4-4h2v2h-2zm0 4h2v2h-2z\"/>"}, "camera": {"body": "<path fill=\"currentColor\" d=\"M0 6c0-1.1.9-2 2-2h3l2-2h6l2 2h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm10 10a5 5 0 1 0 0-10a5 5 0 0 0 0 10m0-2a3 3 0 1 1 0-6a3 3 0 0 1 0 6\"/>"}, "chart": {"body": "<path fill=\"currentColor\" d=\"M4.13 12H4a2 2 0 1 0 1.8 1.11L7.86 10a2 2 0 0 0 .65-.07l1.55 1.55a2 2 0 1 0 3.72-.37L15.87 8H16a2 2 0 1 0-1.8-1.11L12.14 10a2 2 0 0 0-.65.07L9.93 8.52a2 2 0 1 0-3.72.37zM0 4c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z\"/>"}, "chart-bar": {"body": "<path fill=\"currentColor\" d=\"M1 10h3v10H1zM6 0h3v20H6zm5 8h3v12h-3zm5-4h3v16h-3z\"/>"}, "chart-pie": {"body": "<path fill=\"currentColor\" d=\"M19.95 11A10 10 0 1 1 9 .05V11zm-.08-2.6H11.6V.13a10 10 0 0 1 8.27 8.27\"/>"}, "chat-bubble-dots": {"body": "<path fill=\"currentColor\" d=\"m10 15l-4 4v-4H2a2 2 0 0 1-2-2V3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2zM5 7v2h2V7zm4 0v2h2V7zm4 0v2h2V7z\"/>"}, "checkmark": {"body": "<path fill=\"currentColor\" d=\"m0 11l2-2l5 5L18 3l2 2L7 18z\"/>"}, "checkmark-outline": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07m12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32M6.7 9.29L9 11.6l4.3-4.3l1.4 1.42L9 14.4l-3.7-3.7l1.4-1.42z\"/>"}, "cheveron-down": {"body": "<path fill=\"currentColor\" d=\"m9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828L5.757 6.586L4.343 8z\"/>"}, "cheveron-left": {"body": "<path fill=\"currentColor\" d=\"M7.05 9.293L6.343 10L12 15.657l1.414-1.414L9.172 10l4.242-4.243L12 4.343z\"/>"}, "cheveron-outline-down": {"body": "<path fill=\"currentColor\" d=\"M20 10a10 10 0 1 1-20 0a10 10 0 0 1 20 0M10 2a8 8 0 1 0 0 16a8 8 0 0 0 0-16m-.7 10.54L5.75 9l1.41-1.41L10 10.4l2.83-2.82L14.24 9L10 13.24z\"/>"}, "cheveron-outline-left": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m8-10a8 8 0 1 0-16 0a8 8 0 0 0 16 0M7.46 9.3L11 5.75l1.41 1.41L9.6 10l2.82 2.83L11 14.24L6.76 10z\"/>"}, "cheveron-outline-right": {"body": "<path fill=\"currentColor\" d=\"M10 0a10 10 0 1 1 0 20a10 10 0 0 1 0-20M2 10a8 8 0 1 0 16 0a8 8 0 0 0-16 0m10.54.7L9 14.25l-1.41-1.41L10.4 10L7.6 7.17L9 5.76L13.24 10z\"/>"}, "cheveron-outline-up": {"body": "<path fill=\"currentColor\" d=\"M0 10a10 10 0 1 1 20 0a10 10 0 0 1-20 0m10 8a8 8 0 1 0 0-16a8 8 0 0 0 0 16m.7-10.54L14.25 11l-1.41 1.41L10 9.6l-2.83 2.8L5.76 11L10 6.76z\"/>"}, "cheveron-right": {"body": "<path fill=\"currentColor\" d=\"m12.95 10.707l.707-.707L8 4.343L6.586 5.757L10.828 10l-4.242 4.243L8 15.657z\"/>"}, "cheveron-up": {"body": "<path fill=\"currentColor\" d=\"M10.707 7.05L10 6.343L4.343 12l1.414 1.414L10 9.172l4.243 4.242L15.657 12z\"/>"}, "clipboard": {"body": "<path fill=\"currentColor\" d=\"M7.03 2.6a3 3 0 0 1 5.94 0L15 3v1h1a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6c0-1.1.9-2 2-2h1V3zM5 6H4v12h12V6h-1v1H5zm5-2a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "close": {"body": "<path fill=\"currentColor\" d=\"M10 8.586L2.929 1.515L1.515 2.929L8.586 10l-7.071 7.071l1.414 1.414L10 11.414l7.071 7.071l1.414-1.414L11.414 10l7.071-7.071l-1.414-1.414z\"/>"}, "close-outline": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07m1.41-1.41A8 8 0 1 0 15.66 4.34A8 8 0 0 0 4.34 15.66m9.9-8.49L11.41 10l2.83 2.83l-1.41 1.41L10 11.41l-2.83 2.83l-1.41-1.41L8.59 10L5.76 7.17l1.41-1.41L10 8.59l2.83-2.83z\"/>"}, "close-solid": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07M11.4 10l2.83-2.83l-1.41-1.41L10 8.59L7.17 5.76L5.76 7.17L8.59 10l-2.83 2.83l1.41 1.41L10 11.41l2.83 2.83l1.41-1.41L11.41 10z\"/>"}, "cloud": {"body": "<path fill=\"currentColor\" d=\"M16.88 9.1A4 4 0 0 1 16 17H5a5 5 0 0 1-1-9.9V7a3 3 0 0 1 4.52-2.59A4.98 4.98 0 0 1 17 8c0 .38-.04.74-.12 1.1\"/>"}, "cloud-upload": {"body": "<path fill=\"currentColor\" d=\"M16.88 9.1A4 4 0 0 1 16 17H5a5 5 0 0 1-1-9.9V7a3 3 0 0 1 4.52-2.59A4.98 4.98 0 0 1 17 8c0 .38-.04.74-.12 1.1M11 11h3l-4-4l-4 4h3v3h2z\"/>"}, "code": {"body": "<path fill=\"currentColor\" d=\"m.7 9.3l4.8-4.8l1.4 1.42L2.84 10l4.07 4.07l-1.41 1.42L0 10zm18.6 1.4l.7-.7l-5.49-5.49l-1.4 1.42L17.16 10l-4.07 4.07l1.41 1.42l4.78-4.78z\"/>"}, "coffee": {"body": "<path fill=\"currentColor\" d=\"M4 11H2a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h2V1h14v10a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4m0-2V5H2v4zm-2 8v-1h18v1l-4 2H6z\"/>"}, "cog": {"body": "<path fill=\"currentColor\" d=\"M3.94 6.5L2.22 3.64l1.42-1.42L6.5 3.94c.52-.3 1.1-.54 1.7-.7L9 0h2l.8 3.24c.6.16 1.18.4 1.7.7l2.86-1.72l1.42 1.42l-1.72 2.86c.3.52.54 1.1.7 1.7L20 9v2l-3.24.8c-.16.6-.4 1.18-.7 1.7l1.72 2.86l-1.42 1.42l-2.86-1.72c-.52.3-1.1.54-1.7.7L11 20H9l-.8-3.24c-.6-.16-1.18-.4-1.7-.7l-2.86 1.72l-1.42-1.42l1.72-2.86c-.3-.52-.54-1.1-.7-1.7L0 11V9l3.24-.8c.16-.6.4-1.18.7-1.7M10 13a3 3 0 1 0 0-6a3 3 0 0 0 0 6\"/>"}, "color-palette": {"body": "<path fill=\"currentColor\" d=\"M9 20v-1.7l.01-.24L15.07 12h2.94c1.1 0 1.99.89 1.99 2v4a2 2 0 0 1-2 2zm0-3.34V5.34l2.08-2.07a1.99 1.99 0 0 1 2.82 0l2.83 2.83a2 2 0 0 1 0 2.82zM0 1.99C0 .9.89 0 2 0h4a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2zM4 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "compose": {"body": "<path fill=\"currentColor\" d=\"M2 4v14h14v-6l2-2v10H0V2h10L8 4zm10.3-.3l4 4L8 16H4v-4zm1.4-1.4L16 0l4 4l-2.3 2.3z\"/>"}, "computer-desktop": {"body": "<path fill=\"currentColor\" d=\"M7 17H2a2 2 0 0 1-2-2V2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2h-5l4 2v1H3v-1zM2 2v11h16V2z\"/>"}, "computer-laptop": {"body": "<path fill=\"currentColor\" d=\"M18 16h2v1a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1v-1h2V4c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2zM4 4v9h12V4zm4 11v1h4v-1z\"/>"}, "conversation": {"body": "<path fill=\"currentColor\" d=\"M17 11v3l-3-3H8a2 2 0 0 1-2-2V2c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2zm-3 2v2a2 2 0 0 1-2 2H6l-3 3v-3H2a2 2 0 0 1-2-2V8c0-1.1.9-2 2-2h2v3a4 4 0 0 0 4 4z\"/>"}, "copy": {"body": "<path fill=\"currentColor\" d=\"M6 6V2c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-4v4a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8c0-1.1.9-2 2-2zm2 0h4a2 2 0 0 1 2 2v4h4V2H8zM2 8v10h10V8z\"/>"}, "credit-card": {"body": "<path fill=\"currentColor\" d=\"M18 6V4H2v2zm0 4H2v6h16zM0 4c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm4 8h4v2H4z\"/>"}, "currency-dollar": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m1-5h1a3 3 0 0 0 0-6H7.99a1 1 0 0 1 0-2H14V5h-3V3H9v2H8a3 3 0 1 0 0 6h4a1 1 0 1 1 0 2H6v2h3v2h2z\"/>"}, "dashboard": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m-5.6-4.29a9.95 9.95 0 0 1 11.2 0a8 8 0 1 0-11.2 0m6.12-7.64l3.02-3.02l1.41 1.41l-3.02 3.02a2 2 0 1 1-1.41-1.41\"/>"}, "date-add": {"body": "<path fill=\"currentColor\" d=\"M15 2h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V4c0-1.1.9-2 2-2h2V0h2v2h6V0h2zM3 6v12h14V6zm6 5V9h2v2h2v2h-2v2H9v-2H7v-2z\"/>"}, "dial-pad": {"body": "<path fill=\"currentColor\" d=\"M5 4a2 2 0 1 1 0-4a2 2 0 0 1 0 4m5 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4m5 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4M5 9a2 2 0 1 1 0-4a2 2 0 0 1 0 4m5 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4m5 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4M5 14a2 2 0 1 1 0-4a2 2 0 0 1 0 4m5 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4m0 6a2 2 0 1 1 0-4a2 2 0 0 1 0 4m5-6a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "directions": {"body": "<path fill=\"currentColor\" d=\"m10 0l10 10l-10 10L0 10zM6 10v3h2v-3h3v3l4-4l-4-4v3H8a2 2 0 0 0-2 2\"/>"}, "document": {"body": "<path fill=\"currentColor\" d=\"M4 18h12V6h-4V2H4zm-2 1V0h12l4 4v16H2z\"/>"}, "document-add": {"body": "<path fill=\"currentColor\" d=\"M9 10V8h2v2h2v2h-2v2H9v-2H7v-2zm-5 8h12V6h-4V2H4zm-2 1V0h12l4 4v16H2z\"/>"}, "dots-horizontal-double": {"body": "<path fill=\"currentColor\" d=\"M10 9a2 2 0 1 1 0-4a2 2 0 0 1 0 4m0 6a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "dots-horizontal-triple": {"body": "<path fill=\"currentColor\" d=\"M10 12a2 2 0 1 1 0-4a2 2 0 0 1 0 4m0-6a2 2 0 1 1 0-4a2 2 0 0 1 0 4m0 12a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "download": {"body": "<path fill=\"currentColor\" d=\"M13 8V2H7v6H2l8 8l8-8zM0 18h20v2H0z\"/>"}, "duplicate": {"body": "<path fill=\"currentColor\" d=\"M6 6V2c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-4v4a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8c0-1.1.9-2 2-2zm2 0h4a2 2 0 0 1 2 2v4h4V2H8zM2 8v10h10V8zm4 4v-2h2v2h2v2H8v2H6v-2H4v-2z\"/>"}, "edit-copy": {"body": "<path fill=\"currentColor\" d=\"M6 6V2c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-4v4a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8c0-1.1.9-2 2-2zm2 0h4a2 2 0 0 1 2 2v4h4V2H8zM2 8v10h10V8z\"/>"}, "edit-crop": {"body": "<path fill=\"currentColor\" d=\"M14 16H6a2 2 0 0 1-2-2V6H0V4h4V0h2v14h14v2h-4v4h-2zm0-3V6H7V4h7a2 2 0 0 1 2 2v7z\"/>"}, "edit-cut": {"body": "<path fill=\"currentColor\" d=\"m9.77 11.5l5.34 3.91c.44.33 1.24.59 1.79.59H20L6.89 6.38A3.5 3.5 0 1 0 5.5 8.37L7.73 10L5.5 11.63a3.5 3.5 0 1 0 1.38 1.99l2.9-2.12zM3.5 7a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m0 9a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3M15.1 4.59A3.53 3.53 0 0 1 16.9 4H20l-7.5 5.5L10.45 8z\"/>"}, "edit-pencil": {"body": "<path fill=\"currentColor\" d=\"m12.3 3.7l4 4L4 20H0v-4zm1.4-1.4L16 0l4 4l-2.3 2.3z\"/>"}, "education": {"body": "<path fill=\"currentColor\" d=\"M3.33 8L10 12l10-6l-10-6L0 6h10v2zM0 8v8l2-2.22V9.2zm10 12l-5-3l-2-1.2v-6l7 4.2l7-4.2v6z\"/>"}, "envelope": {"body": "<path fill=\"currentColor\" d=\"M18 2a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4c0-1.1.9-2 2-2zm-4.37 9.1L20 16v-2l-5.12-3.9L20 6V4l-10 8L0 4v2l5.12 4.1L0 14v2l6.37-4.9L10 14z\"/>"}, "exclamation-outline": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07m12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32M9 5h2v6H9zm0 8h2v2H9z\"/>"}, "exclamation-solid": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07M9 5v6h2V5zm0 8v2h2v-2z\"/>"}, "explore": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20M7.88 7.88l-3.54 7.78l7.78-3.54l3.54-7.78zM10 11a1 1 0 1 1 0-2a1 1 0 0 1 0 2\"/>"}, "factory": {"body": "<path fill=\"currentColor\" d=\"M10.5 20H0V7l5 3.33V7l5 3.33V7l5 3.33V0h5v20z\"/>"}, "fast-forward": {"body": "<path fill=\"currentColor\" d=\"m1 5l9 5l-9 5zm9 0l9 5l-9 5z\"/>"}, "fast-rewind": {"body": "<path fill=\"currentColor\" d=\"M19 5v10l-9-5zm-9 0v10l-9-5z\"/>"}, "film": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm6 0v12h8V4zM2 5v2h2V5zm0 4v2h2V9zm0 4v2h2v-2zm14-8v2h2V5zm0 4v2h2V9zm0 4v2h2v-2zM8 7l5 3l-5 3z\"/>"}, "filter": {"body": "<path fill=\"currentColor\" d=\"m12 12l8-8V0H0v4l8 8v8l4-4z\"/>"}, "flag": {"body": "<path fill=\"currentColor\" d=\"M7.667 12H2v8H0V0h12l.333 2H20l-3 6l3 6H8z\"/>"}, "flashlight": {"body": "<path fill=\"currentColor\" d=\"M13 7v11a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2V7L5 5V3h10v2zM9 8v1a1 1 0 1 0 2 0V8a1 1 0 0 0-2 0M5 0h10v2H5z\"/>"}, "folder": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-1.1.9-2 2-2h7l2 2h7a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z\"/>"}, "folder-outline": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-1.1.9-2 2-2h7l2 2h7a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v10h16V6z\"/>"}, "folder-outline-add": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-1.1.9-2 2-2h7l2 2h7a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v10h16V6zm7 4V8h2v2h2v2h-2v2H9v-2H7v-2z\"/>"}, "format-bold": {"body": "<path fill=\"currentColor\" d=\"M3 19V1h8a5 5 0 0 1 3.88 8.16A5.5 5.5 0 0 1 11.5 19zm7.5-8H7v5h3.5a2.5 2.5 0 1 0 0-5M7 4v4h3a2 2 0 1 0 0-4z\"/>"}, "format-font-size": {"body": "<path fill=\"currentColor\" d=\"M16 9v8h-2V9h-4V7h10v2zM8 5v12H6V5H0V3h15v2z\"/>"}, "format-italic": {"body": "<path fill=\"currentColor\" d=\"M8 1h9v2H8zm3 2h3L8 17H5zM2 17h9v2H2z\"/>"}, "format-text-size": {"body": "<path fill=\"currentColor\" d=\"M16 9v8h-2V9h-4V7h10v2zM8 5v12H6V5H0V3h15v2z\"/>"}, "format-underline": {"body": "<path fill=\"currentColor\" d=\"M16 9A6 6 0 1 1 4 9V1h3v8a3 3 0 0 0 6 0V1h3zM2 17h16v2H2z\"/>"}, "forward": {"body": "<path fill=\"currentColor\" d=\"m1 5l9 5l-9 5zm9 0l9 5l-9 5z\"/>"}, "forward-step": {"body": "<path fill=\"currentColor\" d=\"M13 5h3v10h-3zM4 5l9 5l-9 5z\"/>"}, "gift": {"body": "<path fill=\"currentColor\" d=\"M14.83 4H20v6h-1v10H1V10H0V4h5.17A3 3 0 0 1 10 .76A3 3 0 0 1 14.83 4M8 10H3v8h5zm4 0v8h5v-8zM8 6H2v2h6zm4 0v2h6V6zM8 4a1 1 0 1 0 0-2a1 1 0 0 0 0 2m4 0a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "globe": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m2-2.25a8 8 0 0 0 4-2.46V9a2 2 0 0 1-2-2V3.07a7.95 7.95 0 0 0-3-1V3a2 2 0 0 1-2 2v1a2 2 0 0 1-2 2v2h3a2 2 0 0 1 2 2zm-4 0V15a2 2 0 0 1-2-2v-1h-.5A1.5 1.5 0 0 1 4 10.5V8H2.25A8.01 8.01 0 0 0 8 17.75\"/>"}, "hand-stop": {"body": "<path fill=\"currentColor\" d=\"M17 16a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4.01V4a1 1 0 0 1 1-1a1 1 0 0 1 1 1v6h1V2a1 1 0 0 1 1-1a1 1 0 0 1 1 1v8h1V1a1 1 0 1 1 2 0v9h1V2a1 1 0 0 1 1-1a1 1 0 0 1 1 1v13h1V9a1 1 0 0 1 1-1h1z\"/>"}, "hard-drive": {"body": "<path fill=\"currentColor\" d=\"M2 2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm10.4 5.6A5 5 0 1 0 15 12V5zM10 14a2 2 0 1 1 0-4a2 2 0 0 1 0 4M6 3v2h4V3zM4 3a1 1 0 1 0 0-2a1 1 0 0 0 0 2m0 16a1 1 0 1 0 0-2a1 1 0 0 0 0 2m12 0a1 1 0 1 0 0-2a1 1 0 0 0 0 2m0-16a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "headphones": {"body": "<path fill=\"currentColor\" d=\"M16 8A6 6 0 1 0 4 8v11H2a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2V8a8 8 0 1 1 16 0v3a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-2zm-4 2h3v10h-3zm-7 0h3v10H5z\"/>"}, "heart": {"body": "<path fill=\"currentColor\" d=\"m10 3.22l-.61-.6a5.5 5.5 0 0 0-7.78 7.77L10 18.78l8.39-8.4a5.5 5.5 0 0 0-7.78-7.77z\"/>"}, "home": {"body": "<path fill=\"currentColor\" d=\"M8 20H3V10H0L10 0l10 10h-3v10h-5v-6H8z\"/>"}, "hot": {"body": "<path fill=\"currentColor\" d=\"M10 0s8 7.58 8 12a8 8 0 1 1-16 0c0-1.5.91-3.35 2.12-5.15A3 3 0 0 0 10 6zM8 0a3 3 0 1 0 0 6z\"/>"}, "hour-glass": {"body": "<path fill=\"currentColor\" d=\"M3 18a7 7 0 0 1 4-6.33V8.33A7 7 0 0 1 3 2H1V0h18v2h-2a7 7 0 0 1-4 6.33v3.34A7 7 0 0 1 17 18h2v2H1v-2zM5 2a5 5 0 0 0 4 4.9V10h2V6.9A5 5 0 0 0 15 2z\"/>"}, "inbox": {"body": "<path fill=\"currentColor\" d=\"M0 2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm14 12h4V2H2v12h4c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2\"/>"}, "inbox-check": {"body": "<path fill=\"currentColor\" d=\"M0 2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm14 12h4V2H2v12h4c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2M5 9l2-2l2 2l4-4l2 2l-6 6z\"/>"}, "inbox-download": {"body": "<path fill=\"currentColor\" d=\"M0 2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm14 12h4V2H2v12h4c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2M9 8V5h2v3h3l-4 4l-4-4z\"/>"}, "inbox-full": {"body": "<path fill=\"currentColor\" d=\"M14 14h4V2H2v12h4c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2M0 2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm4 2h12v2H4zm0 3h12v2H4zm0 3h12v2H4z\"/>"}, "indent-decrease": {"body": "<path fill=\"currentColor\" d=\"M1 1h18v2H1zm6 8h12v2H7zm-6 8h18v2H1zM7 5h12v2H7zm0 8h12v2H7zM5 6v8l-4-4z\"/>"}, "indent-increase": {"body": "<path fill=\"currentColor\" d=\"M1 1h18v2H1zm6 8h12v2H7zm-6 8h18v2H1zM7 5h12v2H7zm0 8h12v2H7zM1 6l4 4l-4 4z\"/>"}, "information-outline": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07m12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32M9 11V9h2v6H9zm0-6h2v2H9z\"/>"}, "information-solid": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07M9 11v4h2V9H9zm0-6v2h2V5z\"/>"}, "key": {"body": "<path fill=\"currentColor\" d=\"M12.26 11.74L10 14H8v2H6v2l-2 2H0v-4l8.26-8.26a6 6 0 1 1 4 4m4.86-4.62A3 3 0 0 0 15 2a3 3 0 0 0-2.12.88z\"/>"}, "keyboard": {"body": "<path fill=\"currentColor\" d=\"M0 6c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 0v2h2V6zm1 3v2h2V9zm-1 3v2h2v-2zm3 0v2h10v-2zm11 0v2h2v-2zM6 9v2h2V9zm3 0v2h2V9zm3 0v2h2V9zm3 0v2h2V9zM5 6v2h2V6zm3 0v2h2V6zm3 0v2h2V6zm3 0v2h4V6z\"/>"}, "layers": {"body": "<path fill=\"currentColor\" d=\"m10 1l10 6l-10 6L0 7zm6.67 10L20 13l-10 6l-10-6l3.33-2L10 15z\"/>"}, "library": {"body": "<path fill=\"currentColor\" d=\"m0 6l10-6l10 6v2H0zm0 12h20v2H0zm2-2h16v2H2zm0-8h4v8H2zm6 0h4v8H8zm6 0h4v8h-4z\"/>"}, "light-bulb": {"body": "<path fill=\"currentColor\" d=\"M7 13.33a7 7 0 1 1 6 0V16H7zM7 17h6v1.5c0 .83-.67 1.5-1.5 1.5h-3A1.5 1.5 0 0 1 7 18.5zm2-5.1V14h2v-2.1a5 5 0 1 0-2 0\"/>"}, "link": {"body": "<path fill=\"currentColor\" d=\"M9.26 13a2 2 0 0 1 .01-2.01A3 3 0 0 0 9 5H5a3 3 0 0 0 0 6h.08a6 6 0 0 0 0 2H5A5 5 0 0 1 5 3h4a5 5 0 0 1 .26 10m1.48-6a2 2 0 0 1-.01 2.01A3 3 0 0 0 11 15h4a3 3 0 0 0 0-6h-.08a6 6 0 0 0 0-2H15a5 5 0 0 1 0 10h-4a5 5 0 0 1-.26-10\"/>"}, "list": {"body": "<path fill=\"currentColor\" d=\"M1 4h2v2H1zm4 0h14v2H5zM1 9h2v2H1zm4 0h14v2H5zm-4 5h2v2H1zm4 0h14v2H5z\"/>"}, "list-add": {"body": "<path fill=\"currentColor\" d=\"M15 9h-3v2h3v3h2v-3h3V9h-3V6h-2zM0 3h10v2H0zm0 8h10v2H0zm0-4h10v2H0zm0 8h10v2H0z\"/>"}, "list-bullet": {"body": "<path fill=\"currentColor\" d=\"M1 4h2v2H1zm4 0h14v2H5zM1 9h2v2H1zm4 0h14v2H5zm-4 5h2v2H1zm4 0h14v2H5z\"/>"}, "load-balancer": {"body": "<path fill=\"currentColor\" d=\"M17 12h-6v4h1v4H8v-4h1v-4H3v4h1v4H0v-4h1v-4a2 2 0 0 1 2-2h6V6H7V0h6v6h-2v4h6a2 2 0 0 1 2 2v4h1v4h-4v-4h1z\"/>"}, "location": {"body": "<path fill=\"currentColor\" d=\"M10 20S3 10.87 3 7a7 7 0 1 1 14 0c0 3.87-7 13-7 13m0-11a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/>"}, "location-current": {"body": "<path fill=\"currentColor\" d=\"m0 0l20 8l-8 4l-2 8z\"/>"}, "location-food": {"body": "<path fill=\"currentColor\" d=\"M18 11v7a2 2 0 0 1-4 0v-5h-2V3a3 3 0 0 1 3-3h3zM4 10a2 2 0 0 1-2-2V1a1 1 0 0 1 2 0v4h1V1a1 1 0 0 1 2 0v4h1V1a1 1 0 0 1 2 0v7a2 2 0 0 1-2 2v8a2 2 0 0 1-4 0z\"/>"}, "location-gas-station": {"body": "<path fill=\"currentColor\" d=\"M13 18h1v2H0v-2h1V2c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2zM3 2v6h8V2zm10 8h1a2 2 0 0 1 2 2v3a1 1 0 0 0 2 0v-5l-2-2V6l-2-2l1-1l5 5v7a3 3 0 0 1-6 0v-3h-1z\"/>"}, "location-hotel": {"body": "<path fill=\"currentColor\" d=\"M2 12h18v6h-2v-2H2v2H0V2h2zm8-6h8a2 2 0 0 1 2 2v3H10zm-4 5a3 3 0 1 1 0-6a3 3 0 0 1 0 6\"/>"}, "location-marina": {"body": "<path fill=\"currentColor\" d=\"M8 1.88V0h2v16h10l-4 4H2l-2-4h8v-2H0v-.26A24.03 24.03 0 0 0 8 1.88M19.97 14H10v-.36A11.94 11.94 0 0 0 10 .36v-.2A16.01 16.01 0 0 1 19.97 14\"/>"}, "location-park": {"body": "<path fill=\"currentColor\" d=\"M5.33 12.77A4 4 0 1 1 3 5.13V5a4 4 0 0 1 5.71-3.62a3.5 3.5 0 0 1 6.26 1.66a2.5 2.5 0 0 1 2 2.08a4 4 0 1 1-2.7 7.49A5 5 0 0 1 12 14.58V18l2 1v1H6v-1l2-1v-3zM5 10l3 3v-3z\"/>"}, "location-restroom": {"body": "<path fill=\"currentColor\" d=\"M12 16H9l2-4.5V9c0-1.1.9-2 2-2h2a2 2 0 0 1 2 2v2.5l2 4.5h-3v4h-4zm-5-3h2V9a2 2 0 0 0-2-2H3a2 2 0 0 0-2 2v4h2v7h4zM5 6a3 3 0 1 1 0-6a3 3 0 0 1 0 6m9 0a3 3 0 1 1 0-6a3 3 0 0 1 0 6\"/>"}, "location-shopping": {"body": "<path fill=\"currentColor\" d=\"M16 6v2h2l2 12H0L2 8h2V6a6 6 0 1 1 12 0m-2 0a4 4 0 1 0-8 0v2h8zM4 10v2h2v-2zm10 0v2h2v-2z\"/>"}, "lock-closed": {"body": "<path fill=\"currentColor\" d=\"M4 8V6a6 6 0 1 1 12 0v2h1a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2zm5 6.73V17h2v-2.27a2 2 0 1 0-2 0M7 6v2h6V6a3 3 0 0 0-6 0\"/>"}, "lock-open": {"body": "<path fill=\"currentColor\" d=\"M4 8V6a6 6 0 1 1 12 0h-3v2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2zm5 6.73V17h2v-2.27a2 2 0 1 0-2 0M7 6v2h6V6a3 3 0 0 0-6 0\"/>"}, "map": {"body": "<path fill=\"currentColor\" d=\"m0 0l6 4l8-4l6 4v16l-6-4l-8 4l-6-4zm7 6v11l6-3V3z\"/>"}, "menu": {"body": "<path fill=\"currentColor\" d=\"M0 3h20v2H0zm0 6h20v2H0zm0 6h20v2H0z\"/>"}, "mic": {"body": "<path fill=\"currentColor\" d=\"M9 18v-1.06A8 8 0 0 1 2 9h2a6 6 0 1 0 12 0h2a8 8 0 0 1-7 7.94V18h3v2H6v-2zM6 4a4 4 0 1 1 8 0v5a4 4 0 1 1-8 0z\"/>"}, "minus-outline": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16m5-9v2H5V9z\"/>"}, "minus-solid": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m5-11H5v2h10z\"/>"}, "mobile-devices": {"body": "<path fill=\"currentColor\" d=\"M17 6V5h-2V2H3v14h5v4h3.25H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2zm-5.75 14H3a2 2 0 0 1-2-2V2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v4a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2zM11 8v8h6V8zm3 11a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "mood-happy-outline": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16M6.5 9a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m7 0a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m2.16 3a6 6 0 0 1-11.32 0z\"/>"}, "mood-happy-solid": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20M6.5 9a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m7 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m2.16 3H4.34a6 6 0 0 0 11.32 0\"/>"}, "mood-neutral-outline": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16M6.5 9a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m7 0a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3M7 13h6a1 1 0 0 1 0 2H7a1 1 0 0 1 0-2\"/>"}, "mood-neutral-solid": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20M6.5 9a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m7 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3M7 13a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z\"/>"}, "mood-sad-outline": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16M6.5 9a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m7 0a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m2.16 6H4.34a6 6 0 0 1 11.32 0\"/>"}, "mood-sad-solid": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20M6.5 9a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m7 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m2.16 6a6 6 0 0 0-11.32 0z\"/>"}, "mouse": {"body": "<path fill=\"currentColor\" d=\"M4 9V6A6 6 0 0 1 9 .08V9zm0 2v3a6 6 0 1 0 12 0v-3zm12-2V6a6 6 0 0 0-5-5.92V9z\"/>"}, "music-album": {"body": "<path fill=\"currentColor\" d=\"M0 0h20v20H0zm10 18a8 8 0 1 0 0-16a8 8 0 0 0 0 16m0-5a3 3 0 1 1 0-6a3 3 0 0 1 0 6\"/>"}, "music-artist": {"body": "<path fill=\"currentColor\" d=\"m15.75 8l-3.74-3.75a3.99 3.99 0 0 1 6.82-3.08A4 4 0 0 1 15.75 8m-13.9 7.3l9.2-9.19l2.83 2.83l-9.2 9.2l-2.82-2.84zm-1.4 2.83l2.11-2.12l1.42 1.42l-2.12 2.12l-1.42-1.42zM10 15l2-2v7h-2z\"/>"}, "music-notes": {"body": "<path fill=\"currentColor\" d=\"M20 2.5V0L6 2v12.17A3 3 0 0 0 5 14H3a3 3 0 0 0 0 6h2a3 3 0 0 0 3-3V5.71L18 4.3v7.88a3 3 0 0 0-1-.17h-2a3 3 0 0 0 0 6h2a3 3 0 0 0 3-3z\"/>"}, "music-playlist": {"body": "<path fill=\"currentColor\" d=\"M16 17a3 3 0 0 1-3 3h-2a3 3 0 0 1 0-6h2a3 3 0 0 1 1 .17V1l6-1v4l-4 .67zM0 3h12v2H0zm0 4h12v2H0zm0 4h12v2H0zm0 4h6v2H0z\"/>"}, "navigation-more": {"body": "<path fill=\"currentColor\" d=\"M4 12a2 2 0 1 1 0-4a2 2 0 0 1 0 4m6 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4m6 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "network": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m7.75-8a8 8 0 0 0 0-4h-3.82a29 29 0 0 1 0 4zm-.82 2h-3.22a14.4 14.4 0 0 1-.95 3.51A8.03 8.03 0 0 0 16.93 14m-8.85-2h3.84a24.6 24.6 0 0 0 0-4H8.08a24.6 24.6 0 0 0 0 4m.25 2c.41 2.4 1.13 4 1.67 4s1.26-1.6 1.67-4zm-6.08-2h3.82a29 29 0 0 1 0-4H2.25a8 8 0 0 0 0 4m.82 2a8.03 8.03 0 0 0 4.17 3.51c-.42-.96-.74-2.16-.95-3.51zm13.86-8a8.03 8.03 0 0 0-4.17-3.51c.42.96.74 2.16.95 3.51zm-8.6 0h3.34c-.41-2.4-1.13-4-1.67-4S8.74 3.6 8.33 6M3.07 6h3.22c.2-1.35.53-2.55.95-3.51A8.03 8.03 0 0 0 3.07 6\"/>"}, "news-paper": {"body": "<path fill=\"currentColor\" d=\"M16 2h4v15a3 3 0 0 1-3 3H3a3 3 0 0 1-3-3V0h16zm0 2v13a1 1 0 0 0 1 1a1 1 0 0 0 1-1V4zM2 2v15a1 1 0 0 0 1 1h11.17a3 3 0 0 1-.17-1V2zm2 8h8v2H4zm0 4h8v2H4zM4 4h8v4H4z\"/>"}, "notification": {"body": "<path fill=\"currentColor\" d=\"M4 8a6 6 0 0 1 4.03-5.67a2 2 0 1 1 3.95 0A6 6 0 0 1 16 8v6l3 2v1H1v-1l3-2zm8 10a2 2 0 1 1-4 0z\"/>"}, "notifications": {"body": "<path fill=\"currentColor\" d=\"M4 8a6 6 0 0 1 4.03-5.67a2 2 0 1 1 3.95 0A6 6 0 0 1 16 8v6l3 2v1H1v-1l3-2zm8 10a2 2 0 1 1-4 0z\"/>"}, "notifications-outline": {"body": "<path fill=\"currentColor\" d=\"M6 8v7h8V8a4 4 0 1 0-8 0m2.03-5.67a2 2 0 1 1 3.95 0A6 6 0 0 1 16 8v6l3 2v1H1v-1l3-2V8a6 6 0 0 1 4.03-5.67M12 18a2 2 0 1 1-4 0z\"/>"}, "paste": {"body": "<path fill=\"currentColor\" d=\"M10.5 20H2a2 2 0 0 1-2-2V6c0-1.1.9-2 2-2h1V3l2.03-.4a3 3 0 0 1 5.94 0L13 3v1h1a2 2 0 0 1 2 2v1h-2V6h-1v1H3V6H2v12h5v2zM8 4a1 1 0 1 0 0-2a1 1 0 0 0 0 2m2 4h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-8a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2m0 2v8h8v-8z\"/>"}, "pause": {"body": "<path fill=\"currentColor\" d=\"M5 4h3v12H5zm7 0h3v12h-3z\"/>"}, "pause-outline": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07m12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32M7 6h2v8H7zm4 0h2v8h-2z\"/>"}, "pause-solid": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07M7 6v8h2V6zm4 0v8h2V6z\"/>"}, "pen-tool": {"body": "<path fill=\"currentColor\" d=\"M11 9.27V0l6 11l-4 6H7l-4-6L9 0v9.27a2 2 0 1 0 2 0M6 18h8v2H6z\"/>"}, "phone": {"body": "<path fill=\"currentColor\" d=\"M20 18.35V19a1 1 0 0 1-1 1h-2A17 17 0 0 1 0 3V1a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4c0 .56-.31 1.31-.7 1.7L3.16 8.84c1.52 3.6 4.4 6.48 8 8l2.12-2.12c.4-.4 1.15-.71 1.7-.71H19a1 1 0 0 1 .99 1v3.35z\"/>"}, "photo": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm11 9l-3-3l-6 6h16l-5-5zm4-4a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/>"}, "php-elephant": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10 12v8A10 10 0 0 1 8.17.17L10 2h5a5 5 0 0 1 5 4.99v9.02A4 4 0 0 1 16 20v-2a2 2 0 1 0 0-4h-4zm5.5-3a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3\"/>"}, "pin": {"body": "<path fill=\"currentColor\" d=\"M11 12h6v-1l-3-1V2l3-1V0H3v1l3 1v8l-3 1v1h6v7l1 1l1-1z\"/>"}, "play": {"body": "<path fill=\"currentColor\" d=\"m4 4l12 6l-12 6z\"/>"}, "play-outline": {"body": "<path fill=\"currentColor\" d=\"M2.93 17.07A10 10 0 1 1 17.07 2.93A10 10 0 0 1 2.93 17.07m12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32M7 6l8 4l-8 4z\"/>"}, "playlist": {"body": "<path fill=\"currentColor\" d=\"M16 17a3 3 0 0 1-3 3h-2a3 3 0 0 1 0-6h2a3 3 0 0 1 1 .17V1l6-1v4l-4 .67zM0 3h12v2H0zm0 4h12v2H0zm0 4h12v2H0zm0 4h6v2H0z\"/>"}, "plugin": {"body": "<path fill=\"currentColor\" d=\"M20 14v4a2 2 0 0 1-2 2h-4v-2a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2H6a2 2 0 0 1-2-2v-4H2a2 2 0 0 1-2-2a2 2 0 0 1 2-2h2V6c0-1.1.9-2 2-2h4V2a2 2 0 0 1 2-2a2 2 0 0 1 2 2v2h4a2 2 0 0 1 2 2v4h-2a2 2 0 0 0-2 2a2 2 0 0 0 2 2z\"/>"}, "portfolio": {"body": "<path fill=\"currentColor\" d=\"M9 12H1v6a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-6h-8v2H9zm0-1H0V5c0-1.1.9-2 2-2h4V2a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1h4a2 2 0 0 1 2 2v6h-9V9H9zm3-8V2H8v1z\"/>"}, "printer": {"body": "<path fill=\"currentColor\" d=\"M4 16H0V6h20v10h-4v4H4zm2-4v6h8v-6zM4 0h12v5H4zM2 8v2h2V8zm4 0v2h2V8z\"/>"}, "pylon": {"body": "<path fill=\"currentColor\" d=\"M17.4 18H20v2H0v-2h2.6L8 0h4zm-3.2-4H5.8l-1.2 4h10.8zm-2.4-8H8.2L7 10h6z\"/>"}, "question": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m2-13c0 .28-.21.8-.42 1L10 9.58c-.57.58-1 1.6-1 2.42v1h2v-1c0-.29.21-.8.42-1L13 9.42c.57-.58 1-1.6 1-2.42a4 4 0 1 0-8 0h2a2 2 0 1 1 4 0m-3 8v2h2v-2z\"/>"}, "queue": {"body": "<path fill=\"currentColor\" d=\"M0 2h20v4H0zm0 8h20v2H0zm0 6h20v2H0z\"/>"}, "radar": {"body": "<path fill=\"currentColor\" d=\"M12 10a2 2 0 0 1-3.41 1.41A2 2 0 0 1 10 8V0a9.97 9.97 0 0 1 10 10zm7.9 1.41A10 10 0 1 1 8.59.1v2.03a8 8 0 1 0 9.29 9.29h2.02zm-4.07 0a6 6 0 1 1-7.25-7.25v2.1a3.99 3.99 0 0 0-1.4 6.57a4 4 0 0 0 6.56-1.42h2.1z\"/>"}, "radar-copy-2": {"body": "<path fill=\"currentColor\" d=\"M12 10a2 2 0 0 1-3.41 1.41A2 2 0 0 1 10 8V0a9.97 9.97 0 0 1 10 10zm7.9 1.41A10 10 0 1 1 8.59.1v2.03a8 8 0 1 0 9.29 9.29h2.02zm-4.07 0a6 6 0 1 1-7.25-7.25v2.1a3.99 3.99 0 0 0-1.4 6.57a4 4 0 0 0 6.56-1.42h2.1z\"/>"}, "radio": {"body": "<path fill=\"currentColor\" d=\"M20 9v9a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8c0-1.1.9-2 2-2h13.8L.74 1.97L1.26.03L20 5.06zm-5 9a3 3 0 1 0 0-6a3 3 0 0 0 0 6M2 8v2h16V8zm1.5 10a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m5 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m6.5-1a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "refresh": {"body": "<path fill=\"currentColor\" d=\"M10 3v2a5 5 0 0 0-3.54 8.54l-1.41 1.41A7 7 0 0 1 10 3m4.95 2.05A7 7 0 0 1 10 17v-2a5 5 0 0 0 3.54-8.54zM10 20l-4-4l4-4zm0-12V0l4 4z\"/>"}, "reload": {"body": "<path fill=\"currentColor\" d=\"M14.66 15.66A8 8 0 1 1 17 10h-2a6 6 0 1 0-1.76 4.24zM12 10h8l-4 4z\"/>"}, "reply": {"body": "<path fill=\"currentColor\" d=\"M15 17v-2.99A4 4 0 0 0 11 10H8v5L2 9l6-6v5h3a6 6 0 0 1 6 6v3z\"/>"}, "reply-all": {"body": "<path fill=\"currentColor\" d=\"M18 17v-2.99A4 4 0 0 0 14 10h-3v5L5 9l6-6v5h3a6 6 0 0 1 6 6v3zM6 6V3L0 9l6 6v-3L3 9z\"/>"}, "repost": {"body": "<path fill=\"currentColor\" d=\"M5 4a2 2 0 0 0-2 2v6H0l4 4l4-4H5V6h7l2-2zm10 4h-3l4-4l4 4h-3v6a2 2 0 0 1-2 2H6l2-2h7z\"/>"}, "save-disk": {"body": "<path fill=\"currentColor\" d=\"M0 2C0 .9.9 0 2 0h14l4 4v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm5 0v6h10V2zm6 1h3v4h-3z\"/>"}, "screen-full": {"body": "<path fill=\"currentColor\" d=\"M2.8 15.8L0 13v7h7l-2.8-2.8l4.34-4.32l-1.42-1.42zM17.2 4.2L20 7V0h-7l2.8 2.8l-4.34 4.32l1.42 1.42zm-1.4 13L13 20h7v-7l-2.8 2.8l-4.32-4.34l-1.42 1.42l4.33 4.33zM4.2 2.8L7 0H0v7l2.8-2.8l4.32 4.34l1.42-1.42z\"/>"}, "search": {"body": "<path fill=\"currentColor\" d=\"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33l-1.42 1.42l-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12\"/>"}, "send": {"body": "<path fill=\"currentColor\" d=\"m0 0l20 10L0 20zm0 8v4l10-2z\"/>"}, "servers": {"body": "<path fill=\"currentColor\" d=\"M0 2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm0 7c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm0 7c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zM12 2v2h2V2zm4 0v2h2V2zm-4 7v2h2V9zm4 0v2h2V9zm-4 7v2h2v-2zm4 0v2h2v-2z\"/>"}, "share": {"body": "<path fill=\"currentColor\" d=\"M4 10c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2zm2 0v8h8v-8h-2V8H8v2zm3-6.17V16h2V3.83l3.07 3.07l1.42-1.41L10 0l-.7.7l-4.8 4.8l1.42 1.4L9 3.84z\"/>"}, "share-01": {"body": "<path fill=\"currentColor\" d=\"M4 10c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2zm2 0v8h8v-8h-2V8H8v2zm3-6.17V16h2V3.83l3.07 3.07l1.42-1.41L10 0l-.7.7l-4.8 4.8l1.42 1.4L9 3.84z\"/>"}, "share-alt": {"body": "<path fill=\"currentColor\" d=\"M5.08 12.16A2.99 2.99 0 0 1 0 10a3 3 0 0 1 5.08-2.16l8.94-4.47a3 3 0 1 1 .9 1.79L5.98 9.63a3 3 0 0 1 0 .74l8.94 4.47A2.99 2.99 0 0 1 20 17a3 3 0 1 1-5.98-.37z\"/>"}, "shield": {"body": "<path fill=\"currentColor\" d=\"M19 11a7.5 7.5 0 0 1-3.5 5.94L10 20l-5.5-3.06A7.5 7.5 0 0 1 1 11V3c3.38 0 6.5-1.12 9-3c2.5 1.89 5.62 3 9 3zm-9 1.08l2.92 2.04l-1.03-3.41l2.84-2.15l-3.56-.08L10 5.12L8.83 8.48l-3.56.08L8.1 10.7l-1.03 3.4L10 12.09z\"/>"}, "shopping-cart": {"body": "<path fill=\"currentColor\" d=\"M4 2h16l-3 9H4a1 1 0 1 0 0 2h13v2H4a3 3 0 0 1 0-6h.33L3 5L2 2H0V0h3a1 1 0 0 1 1 1zm1 18a2 2 0 1 1 0-4a2 2 0 0 1 0 4m10 0a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "show-sidebar": {"body": "<path fill=\"currentColor\" d=\"M7 3H2v14h5zm2 0v14h9V3zM0 3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm3 1h3v2H3zm0 3h3v2H3zm0 3h3v2H3z\"/>"}, "shuffle": {"body": "<path fill=\"currentColor\" d=\"M6.59 12.83L4.4 15c-.58.58-1.59 1-2.4 1H0v-2h2c.29 0 .8-.2 1-.41l2.17-2.18zM16 4V1l4 4l-4 4V6h-2c-.29 0-.8.2-1 .41l-2.17 2.18L9.4 7.17L11.6 5c.58-.58 1.59-1 2.41-1h2zm0 10v-3l4 4l-4 4v-3h-2c-.82 0-1.83-.42-2.41-1l-8.6-8.59C2.8 6.21 2.3 6 2 6H0V4h2c.82 0 1.83.42 2.41 1l8.6 8.59c.2.2.7.41.99.41z\"/>"}, "stand-by": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m4.16 4.16l1.42 1.42A6.99 6.99 0 0 0 10 18a7 7 0 0 0 4.42-12.42l1.42-1.42a9 9 0 1 1-11.69 0zM9 0h2v8H9z\"/>"}, "star-full": {"body": "<path fill=\"currentColor\" d=\"m10 15l-5.878 3.09l1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955l6.572.955l-4.756 4.635l1.123 6.545z\"/>"}, "station": {"body": "<path fill=\"currentColor\" d=\"M9 11.73a2 2 0 1 1 2 0V20H9zm5.24 2.51l-1.41-1.41A3.99 3.99 0 0 0 10 6a4 4 0 0 0-2.83 6.83l-1.41 1.41a6 6 0 1 1 8.49 0zm2.83 2.83l-1.41-1.41a8 8 0 1 0-11.31 0l-1.42 1.41a10 10 0 1 1 14.14 0\"/>"}, "step-backward": {"body": "<path fill=\"currentColor\" d=\"M4 5h3v10H4zm12 0v10l-9-5z\"/>"}, "step-forward": {"body": "<path fill=\"currentColor\" d=\"M13 5h3v10h-3zM4 5l9 5l-9 5z\"/>"}, "stethoscope": {"body": "<path fill=\"currentColor\" d=\"M17 10.27V4.99a1 1 0 0 0-2 0V15a5 5 0 0 1-10 0v-1.08A6 6 0 0 1 0 8V2C0 .9.9 0 2 0h1a1 1 0 0 1 1 1a1 1 0 0 1-1 1H2v6a4 4 0 1 0 8 0V2H9a1 1 0 0 1-1-1a1 1 0 0 1 1-1h1a2 2 0 0 1 2 2v6a6 6 0 0 1-5 5.92V15a3 3 0 0 0 6 0V5a3 3 0 0 1 6 0v5.27a2 2 0 1 1-2 0\"/>"}, "store-front": {"body": "<path fill=\"currentColor\" d=\"M18 9.87V20H2V9.87a4.25 4.25 0 0 0 3-.38V14h10V9.5a4.26 4.26 0 0 0 3 .37M3 0h4l-.67 6.03A3.43 3.43 0 0 1 3 9C1.34 9 .42 7.73.95 6.15zm5 0h4l.7 6.3c.17 1.5-.91 2.7-2.42 2.7h-.56A2.38 2.38 0 0 1 7.3 6.3zm5 0h4l2.05 6.15C19.58 7.73 18.65 9 17 9a3.42 3.42 0 0 1-3.33-2.97z\"/>"}, "stroke-width": {"body": "<path fill=\"currentColor\" d=\"M0 0h20v5H0zm0 7h20v4H0zm0 6h20v3H0zm0 5h20v2H0z\"/>"}, "subdirectory-left": {"body": "<path fill=\"currentColor\" d=\"M18 12v1H8v5l-6-6l6-6v5h8V2h2z\"/>"}, "subdirectory-right": {"body": "<path fill=\"currentColor\" d=\"M3.5 13H12v5l6-6l-6-6v5H4V2H2v11z\"/>"}, "swap": {"body": "<path fill=\"currentColor\" d=\"M9 6a4 4 0 1 1 8 0v8h3l-4 4l-4-4h3V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8a4 4 0 1 1-8 0V6H0l4-4l4 4H5v8a2 2 0 0 0 2 2a2 2 0 0 0 2-2z\"/>"}, "tablet": {"body": "<path fill=\"currentColor\" d=\"M2 2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm2 0v14h12V2zm6 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "tag": {"body": "<path fill=\"currentColor\" d=\"M0 10V2l2-2h8l10 10l-10 10zm4.5-4a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3\"/>"}, "target": {"body": "<path fill=\"currentColor\" d=\"M17.94 11H13V9h4.94A8 8 0 0 0 11 2.06V7H9V2.06A8 8 0 0 0 2.06 9H7v2H2.06A8 8 0 0 0 9 17.94V13h2v4.94A8 8 0 0 0 17.94 11M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20\"/>"}, "text-box": {"body": "<path fill=\"currentColor\" d=\"M0 0h6v6H0zm2 2v2h2V2zm12-2h6v6h-6zm2 2v2h2V2zm-2 12h6v6h-6zm2 2v2h2v-2zM0 14h6v6H0zm2 2v2h2v-2zM6 2h8v2H6zm0 14h8v2H6zM16 6h2v8h-2zM2 6h2v8H2zm5 1h6v2H7zm2 2h2v4H9z\"/>"}, "text-decoration": {"body": "<path fill=\"currentColor\" d=\"M12 5h-2v12H8V3h8v2h-2v12h-2zM8 3a4 4 0 1 0 0 8z\"/>"}, "thermometer": {"body": "<path fill=\"currentColor\" d=\"M9 11.17V7h2v4.17a3 3 0 1 1-2 0m-1-.63a4 4 0 1 0 4 0V4a2 2 0 1 0-4 0v6.53zM6 9.53V4a4 4 0 0 1 8 0v5.53A5.99 5.99 0 0 1 10 20A6 6 0 0 1 6 9.53\"/>"}, "thumbs-down": {"body": "<path fill=\"currentColor\" d=\"M11 20a2 2 0 0 1-2-2v-6H2a2 2 0 0 1-2-2V8l2.3-6.12A3.11 3.11 0 0 1 5 0h8a2 2 0 0 1 2 2v8l-3 7v3zm6-10V0h3v10z\"/>"}, "thumbs-up": {"body": "<path fill=\"currentColor\" d=\"M11 0h1v3l3 7v8a2 2 0 0 1-2 2H5c-1.1 0-2.31-.84-2.7-1.88L0 12v-2a2 2 0 0 1 2-2h7V2a2 2 0 0 1 2-2m6 10h3v10h-3z\"/>"}, "ticket": {"body": "<path fill=\"currentColor\" d=\"M20 12v5H0v-5a2 2 0 1 0 0-4V3h20v5a2 2 0 1 0 0 4M3 5v10h14V5zm7 7.08l-2.92 2.04L8.1 10.7L5.27 8.56l3.56-.08L10 5.12l1.17 3.36l3.56.08l-2.84 2.15l1.03 3.4L10 12.09z\"/>"}, "time": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16m-1-7.59V4h2v5.59l3.95 3.95l-1.41 1.41z\"/>"}, "timer": {"body": "<path fill=\"currentColor\" d=\"M16.32 7.1A8 8 0 1 1 9 4.06V2h2v2.06c1.46.18 2.8.76 3.9 1.62l1.46-1.46l1.42 1.42l-1.46 1.45zM10 18a6 6 0 1 0 0-12a6 6 0 0 0 0 12M7 0h6v2H7zm5.12 8.46l1.42 1.42L10 13.4L8.59 12z\"/>"}, "tools-copy": {"body": "<path fill=\"currentColor\" d=\"M10 0s8 7.58 8 12a8 8 0 1 1-16 0c0-1.5.91-3.35 2.12-5.15A3 3 0 0 0 10 6zM8 0a3 3 0 1 0 0 6z\"/>"}, "translate": {"body": "<path fill=\"currentColor\" d=\"m7.41 9l2.24 2.24l-.83 2L6 10.4l-3.3 3.3l-1.4-1.42L4.58 9l-.88-.88c-.53-.53-1-1.3-1.3-2.12h2.2c.15.28.33.53.51.7l.89.9l.88-.88C7.48 6.1 8 4.84 8 4H0V2h5V0h2v2h5v2h-2c0 1.37-.74 3.15-1.7 4.12L7.4 9zm3.84 8L10 20H8l5-12h2l5 12h-2l-1.25-3zm.83-2h3.84L14 10.4z\"/>"}, "trash": {"body": "<path fill=\"currentColor\" d=\"m6 2l2-2h4l2 2h4v2H2V2zM3 6h14l-1 14H4zm5 2v10h1V8zm3 0v10h1V8z\"/>"}, "travel": {"body": "<path fill=\"currentColor\" d=\"M14 5h2v14H4V5h2V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2zm3 0h1a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1zM3 5v14H2a2 2 0 0 1-2-2V7c0-1.1.9-2 2-2zm5-1v1h4V4z\"/>"}, "travel-bus": {"body": "<path fill=\"currentColor\" d=\"M13 18H7v1a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-1a2 2 0 0 1-2-2V2c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1zM4 5v6h5V5zm7 0v6h5V5zM5 2v1h10V2zm.5 14a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m9 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3\"/>"}, "travel-car": {"body": "<path fill=\"currentColor\" d=\"M2 14v-3H1a1 1 0 0 1-1-1a1 1 0 0 1 1-1h1l4-7h8l4 7h1a1 1 0 0 1 1 1a1 1 0 0 1-1 1h-1v6a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1H5v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1zm13.86-5L13 4H7L4.14 9zM5.5 14a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m9 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3\"/>"}, "travel-case": {"body": "<path fill=\"currentColor\" d=\"M14 5h2v14H4V5h2V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2zm3 0h1a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1zM3 5v14H2a2 2 0 0 1-2-2V7c0-1.1.9-2 2-2zm5-1v1h4V4z\"/>"}, "travel-taxi-cab": {"body": "<path fill=\"currentColor\" d=\"M12 3h2l4 7h1a1 1 0 0 1 1 1a1 1 0 0 1-1 1h-1v6a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1H5v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-6H1a1 1 0 0 1-1-1a1 1 0 0 1 1-1h1l4-7h2V1h4zm3.86 7L13 5H7l-2.86 5zM5.5 15a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m9 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3\"/>"}, "travel-train": {"body": "<path fill=\"currentColor\" d=\"M12 18H8l-2 2H3l2-2a2 2 0 0 1-2-2V2c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2l2 2h-3zM5 5v6h10V5zm1.5 11a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m7 0a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3M8 2v1h4V2z\"/>"}, "travel-walk": {"body": "<path fill=\"currentColor\" d=\"m11 7l1.44 2.16c.31.47 1.01.84 1.57.84H17V8h-3l-1.44-2.16a6 6 0 0 0-1.4-1.4l-1.32-.88a1.72 1.72 0 0 0-1.7-.04L4 6v5h2V7l2-1l-3 14h2l2.35-7.65L11 14v6h2v-8l-2.7-2.7zm1-3a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/>"}, "trophy": {"body": "<path fill=\"currentColor\" d=\"M15 9a3 3 0 0 0 3-3h2a5 5 0 0 1-5.1 5a5 5 0 0 1-3.9 3.9V17l5 2v1H4v-1l5-2v-2.1A5 5 0 0 1 5.1 11H5a5 5 0 0 1-5-5h2a3 3 0 0 0 3 3V4H2v2H0V2h5V0h10v2h5v4h-2V4h-3z\"/>"}, "tuning": {"body": "<path fill=\"currentColor\" d=\"M17 16v4h-2v-4h-2v-3h6v3zM1 9h6v3H1zm6-4h6v3H7zM3 0h2v8H3zm12 0h2v12h-2zM9 0h2v4H9zM3 12h2v8H3zm6-4h2v12H9z\"/>"}, "upload": {"body": "<path fill=\"currentColor\" d=\"M13 10v6H7v-6H2l8-8l8 8zM0 18h20v2H0z\"/>"}, "usb": {"body": "<path fill=\"currentColor\" d=\"M15 8v2h-4V4h2l-3-4l-3 4h2v8H5V9.73a2 2 0 1 0-2 0V12a2 2 0 0 0 2 2h4v2.27a2 2 0 1 0 2 0V12h4a2 2 0 0 0 2-2V8h1V4h-4v4z\"/>"}, "user": {"body": "<path fill=\"currentColor\" d=\"M5 5a5 5 0 0 1 10 0v2A5 5 0 0 1 5 7zM0 16.68A19.9 19.9 0 0 1 10 14c3.64 0 7.06.97 10 2.68V20H0z\"/>"}, "user-add": {"body": "<path fill=\"currentColor\" d=\"M2 6H0v2h2v2h2V8h2V6H4V4H2zm7 0a3 3 0 0 1 6 0v2a3 3 0 0 1-6 0zm11 9.14A15.93 15.93 0 0 0 12 13c-2.91 0-5.65.78-8 2.14V18h16z\"/>"}, "user-group": {"body": "<path fill=\"currentColor\" d=\"M7 8a4 4 0 1 1 0-8a4 4 0 0 1 0 8m0 1c2.15 0 4.2.4 6.1 1.09L12 16h-1.25L10 20H4l-.75-4H2L.9 10.09A17.9 17.9 0 0 1 7 9m8.31.17c1.32.18 2.59.48 3.8.92L18 16h-1.25L16 20h-3.96l.37-2h1.25zM13 0a4 4 0 1 1-1.33 7.76a5.96 5.96 0 0 0 0-7.52C12.1.1 12.53 0 13 0\"/>"}, "user-solid-circle": {"body": "<path fill=\"currentColor\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20M7 6v2a3 3 0 1 0 6 0V6a3 3 0 1 0-6 0m-3.65 8.44a8 8 0 0 0 13.3 0a15.94 15.94 0 0 0-13.3 0\"/>"}, "user-solid-square": {"body": "<path fill=\"currentColor\" d=\"M0 2C0 .9.9 0 2 0h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm7 4v2a3 3 0 1 0 6 0V6a3 3 0 1 0-6 0m11 9.14A15.93 15.93 0 0 0 10 13c-2.91 0-5.65.78-8 2.14V18h16z\"/>"}, "vector": {"body": "<path fill=\"currentColor\" d=\"M12 4h4.27a2 2 0 1 1 0 2h-2.14a9 9 0 0 1 4.84 7.25a2 2 0 1 1-2 .04a7 7 0 0 0-4.97-6V8H8v-.71a7 7 0 0 0-4.96 6a2 2 0 1 1-2-.04A9 9 0 0 1 5.86 6H3.73a2 2 0 1 1 0-2H8V3h4z\"/>"}, "video-camera": {"body": "<path fill=\"currentColor\" d=\"m16 7l4-4v14l-4-4v3a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4c0-1.1.9-2 2-2h12a2 2 0 0 1 2 2zm-8 7a4 4 0 1 0 0-8a4 4 0 0 0 0 8m0-2a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "view-carousel": {"body": "<path fill=\"currentColor\" d=\"M16 16v2H4v-2H0V4h4V2h12v2h4v12zM14 5.5V4H6v12h8zm2 .5v8h2V6zM4 6H2v8h2z\"/>"}, "view-column": {"body": "<path fill=\"currentColor\" d=\"M12 4H8v12h4zm2 0v12h4V4zM6 4H2v12h4zM0 2h20v16H0z\"/>"}, "view-hide": {"body": "<path fill=\"currentColor\" d=\"m12.81 4.36l-1.77 1.78a4 4 0 0 0-4.9 4.9l-2.76 2.75C2.06 12.79.96 11.49.2 10a11 11 0 0 1 12.6-5.64zm3.8 1.85c1.33 1 2.43 2.3 3.2 3.79a11 11 0 0 1-12.62 5.64l1.77-1.78a4 4 0 0 0 4.9-4.9l2.76-2.75zm-.25-3.99l1.42 1.42L3.64 17.78l-1.42-1.42z\"/>"}, "view-list": {"body": "<path fill=\"currentColor\" d=\"M0 3h20v2H0zm0 4h20v2H0zm0 4h20v2H0zm0 4h20v2H0z\"/>"}, "view-show": {"body": "<path fill=\"currentColor\" d=\"M.2 10a11 11 0 0 1 19.6 0A11 11 0 0 1 .2 10m9.8 4a4 4 0 1 0 0-8a4 4 0 0 0 0 8m0-2a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/>"}, "view-tile": {"body": "<path fill=\"currentColor\" d=\"M0 0h9v9H0zm2 2v5h5V2zm-2 9h9v9H0zm2 2v5h5v-5zm9-13h9v9h-9zm2 2v5h5V2zm-2 9h9v9h-9zm2 2v5h5v-5z\"/>"}, "volume-down": {"body": "<path fill=\"currentColor\" d=\"M7 7H3v6h4l5 5V2zm8.54 6.54l-1.42-1.42a3 3 0 0 0 0-4.24l1.42-1.42a4.98 4.98 0 0 1 0 7.08\"/>"}, "volume-mute": {"body": "<path fill=\"currentColor\" d=\"M9 7H5v6h4l5 5V2z\"/>"}, "volume-off": {"body": "<path fill=\"currentColor\" d=\"m15 8.59l-2.12-2.13l-1.42 1.42L13.6 10l-2.13 2.12l1.42 1.42L15 11.4l2.12 2.13l1.42-1.42L16.4 10l2.13-2.12l-1.42-1.42L15 8.6zM4 7H0v6h4l5 5V2z\"/>"}, "volume-up": {"body": "<path fill=\"currentColor\" d=\"M5 7H1v6h4l5 5V2zm11.36 9.36l-1.41-1.41a6.98 6.98 0 0 0 0-9.9l1.41-1.41a8.97 8.97 0 0 1 0 12.72m-2.82-2.82l-1.42-1.42a3 3 0 0 0 0-4.24l1.42-1.42a4.98 4.98 0 0 1 0 7.08\"/>"}, "wallet": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-1.1.9-2 2-2h15a1 1 0 0 1 1 1v1H2v1h17a1 1 0 0 1 1 1v10a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm16.5 9a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3\"/>"}, "watch": {"body": "<path fill=\"currentColor\" d=\"M11 9h2v2H9V7h2zm-5.82 6.08a6.98 6.98 0 0 1 0-10.16L6 0h8l.82 4.92a6.98 6.98 0 0 1 0 10.16L14 20H6zM10 15a5 5 0 1 0 0-10a5 5 0 0 0 0 10\"/>"}, "window": {"body": "<path fill=\"currentColor\" d=\"M0 3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v12h16V5z\"/>"}, "window-new": {"body": "<path fill=\"currentColor\" d=\"M9 10V8h2v2h2v2h-2v2H9v-2H7v-2zM0 3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v12h16V5z\"/>"}, "window-open": {"body": "<path fill=\"currentColor\" d=\"M0 3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2 2v12h16V5zm8 3l4 5H6z\"/>"}, "wrench": {"body": "<path fill=\"currentColor\" d=\"M6.47 9.8A5 5 0 0 1 .2 3.22l3.95 3.95l2.82-2.83L3.03.41a5 5 0 0 1 6.4 6.68l10 10l-2.83 2.83z\"/>"}, "yin-yang": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10 20a10 10 0 1 1 0-20a10 10 0 0 1 0 20m0-18a8 8 0 1 0 0 16a4 4 0 1 1 0-8a4 4 0 1 0 0-8m0 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2m0-8a1 1 0 1 1 0-2a1 1 0 0 1 0 2\"/>"}, "zoom-in": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33l-1.42 1.42l-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12M7 7V5h2v2h2v2H9v2H7V9H5V7z\"/>"}, "zoom-out": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33l-1.42 1.42l-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12M5 7h6v2H5z\"/>"}}, "aliases": {"bookmarkcopy2": {"parent": "bookmark-copy-2"}, "bookmarkcopy3": {"parent": "bookmark-copy-3"}, "radarcopy2": {"parent": "radar"}, "toolscopy": {"parent": "hot"}}, "width": 20, "height": 20}