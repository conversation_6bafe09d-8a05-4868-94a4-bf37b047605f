{"version": 3, "sources": ["../../@iconify/vue/dist/iconify.mjs"], "sourcesContent": ["import { h, defineComponent } from 'vue';\n\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\nconst defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nfunction mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nfunction getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\n\nfunction parseIconSet(data, callback) {\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    data.not_found.forEach((name) => {\n      callback(name, null);\n      names.push(name);\n    });\n  }\n  const tree = getIconsTree(data);\n  for (const name in tree) {\n    const item = tree[name];\n    if (item) {\n      callback(name, internalGetIconData(data, name, item));\n      names.push(name);\n    }\n  }\n  return names;\n}\n\nconst optionalPropertyDefaults = {\n  provider: \"\",\n  aliases: {},\n  not_found: {},\n  ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n  for (const prop in defaults) {\n    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n    return null;\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (\n      // Name cannot be empty\n      !name || // Must have body\n      typeof icon.body !== \"string\" || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  for (const name in aliases) {\n    const icon = aliases[name];\n    const parent = icon.parent;\n    if (\n      // Name cannot be empty\n      !name || // Parent must be set and point to existing icon\n      typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  return data;\n}\n\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n  return {\n    provider,\n    prefix,\n    icons: /* @__PURE__ */ Object.create(null),\n    missing: /* @__PURE__ */ new Set()\n  };\n}\nfunction getStorage(provider, prefix) {\n  const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n  return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n  if (!quicklyValidateIconSet(data)) {\n    return [];\n  }\n  return parseIconSet(data, (name, icon) => {\n    if (icon) {\n      storage.icons[name] = icon;\n    } else {\n      storage.missing.add(name);\n    }\n  });\n}\nfunction addIconToStorage(storage, name, icon) {\n  try {\n    if (typeof icon.body === \"string\") {\n      storage.icons[name] = { ...icon };\n      return true;\n    }\n  } catch (err) {\n  }\n  return false;\n}\nfunction listIcons(provider, prefix) {\n  let allIcons = [];\n  const providers = typeof provider === \"string\" ? [provider] : Object.keys(dataStorage);\n  providers.forEach((provider2) => {\n    const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [prefix] : Object.keys(dataStorage[provider2] || {});\n    prefixes.forEach((prefix2) => {\n      const storage = getStorage(provider2, prefix2);\n      allIcons = allIcons.concat(\n        Object.keys(storage.icons).map(\n          (name) => (provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name\n        )\n      );\n    });\n  });\n  return allIcons;\n}\n\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n  if (typeof allow === \"boolean\") {\n    simpleNames = allow;\n  }\n  return simpleNames;\n}\nfunction getIconData(name) {\n  const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n  if (icon) {\n    const storage = getStorage(icon.provider, icon.prefix);\n    const iconName = icon.name;\n    return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n  }\n}\nfunction addIcon(name, data) {\n  const icon = stringToIcon(name, true, simpleNames);\n  if (!icon) {\n    return false;\n  }\n  const storage = getStorage(icon.provider, icon.prefix);\n  if (data) {\n    return addIconToStorage(storage, icon.name, data);\n  } else {\n    storage.missing.add(icon.name);\n    return true;\n  }\n}\nfunction addCollection(data, provider) {\n  if (typeof data !== \"object\") {\n    return false;\n  }\n  if (typeof provider !== \"string\") {\n    provider = data.provider || \"\";\n  }\n  if (simpleNames && !provider && !data.prefix) {\n    let added = false;\n    if (quicklyValidateIconSet(data)) {\n      data.prefix = \"\";\n      parseIconSet(data, (name, icon) => {\n        if (addIcon(name, icon)) {\n          added = true;\n        }\n      });\n    }\n    return added;\n  }\n  const prefix = data.prefix;\n  if (!validateIconName({\n    provider,\n    prefix,\n    name: \"a\"\n  })) {\n    return false;\n  }\n  const storage = getStorage(provider, prefix);\n  return !!addIconSet(storage, data);\n}\nfunction iconLoaded(name) {\n  return !!getIconData(name);\n}\nfunction getIcon(name) {\n  const result = getIconData(name);\n  return result ? {\n    ...defaultIconProps,\n    ...result\n  } : result;\n}\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nfunction splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = wrapSVGContent(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n  storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n  return storage[provider] || storage[\"\"];\n}\n\nfunction createAPIConfig(source) {\n  let resources;\n  if (typeof source.resources === \"string\") {\n    resources = [source.resources];\n  } else {\n    resources = source.resources;\n    if (!(resources instanceof Array) || !resources.length) {\n      return null;\n    }\n  }\n  const result = {\n    // API hosts\n    resources,\n    // Root path\n    path: source.path || \"/\",\n    // URL length limit\n    maxURL: source.maxURL || 500,\n    // Timeout before next host is used.\n    rotate: source.rotate || 750,\n    // Timeout before failing query.\n    timeout: source.timeout || 5e3,\n    // Randomise default API end point.\n    random: source.random === true,\n    // Start index\n    index: source.index || 0,\n    // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n    dataAfterTimeout: source.dataAfterTimeout !== false\n  };\n  return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n  \"https://api.simplesvg.com\",\n  \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile (fallBackAPISources.length > 0) {\n  if (fallBackAPISources.length === 1) {\n    fallBackAPI.push(fallBackAPISources.shift());\n  } else {\n    if (Math.random() > 0.5) {\n      fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n      fallBackAPI.push(fallBackAPISources.pop());\n    }\n  }\n}\nconfigStorage[\"\"] = createAPIConfig({\n  resources: [\"https://api.iconify.design\"].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n  const config = createAPIConfig(customConfig);\n  if (config === null) {\n    return false;\n  }\n  configStorage[provider] = config;\n  return true;\n}\nfunction getAPIConfig(provider) {\n  return configStorage[provider];\n}\nfunction listAPIProviders() {\n  return Object.keys(configStorage);\n}\n\nconst detectFetch = () => {\n  let callback;\n  try {\n    callback = fetch;\n    if (typeof callback === \"function\") {\n      return callback;\n    }\n  } catch (err) {\n  }\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n  fetchModule = fetch2;\n}\nfunction getFetch() {\n  return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n  const config = getAPIConfig(provider);\n  if (!config) {\n    return 0;\n  }\n  let result;\n  if (!config.maxURL) {\n    result = 0;\n  } else {\n    let maxHostLength = 0;\n    config.resources.forEach((item) => {\n      const host = item;\n      maxHostLength = Math.max(maxHostLength, host.length);\n    });\n    const url = prefix + \".json?icons=\";\n    result = config.maxURL - maxHostLength - config.path.length - url.length;\n  }\n  return result;\n}\nfunction shouldAbort(status) {\n  return status === 404;\n}\nconst prepare = (provider, prefix, icons) => {\n  const results = [];\n  const maxLength = calculateMaxLength(provider, prefix);\n  const type = \"icons\";\n  let item = {\n    type,\n    provider,\n    prefix,\n    icons: []\n  };\n  let length = 0;\n  icons.forEach((name, index) => {\n    length += name.length + 1;\n    if (length >= maxLength && index > 0) {\n      results.push(item);\n      item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n      };\n      length = name.length;\n    }\n    item.icons.push(name);\n  });\n  results.push(item);\n  return results;\n};\nfunction getPath(provider) {\n  if (typeof provider === \"string\") {\n    const config = getAPIConfig(provider);\n    if (config) {\n      return config.path;\n    }\n  }\n  return \"/\";\n}\nconst send = (host, params, callback) => {\n  if (!fetchModule) {\n    callback(\"abort\", 424);\n    return;\n  }\n  let path = getPath(params.provider);\n  switch (params.type) {\n    case \"icons\": {\n      const prefix = params.prefix;\n      const icons = params.icons;\n      const iconsList = icons.join(\",\");\n      const urlParams = new URLSearchParams({\n        icons: iconsList\n      });\n      path += prefix + \".json?\" + urlParams.toString();\n      break;\n    }\n    case \"custom\": {\n      const uri = params.uri;\n      path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n      break;\n    }\n    default:\n      callback(\"abort\", 400);\n      return;\n  }\n  let defaultError = 503;\n  fetchModule(host + path).then((response) => {\n    const status = response.status;\n    if (status !== 200) {\n      setTimeout(() => {\n        callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n      });\n      return;\n    }\n    defaultError = 501;\n    return response.json();\n  }).then((data) => {\n    if (typeof data !== \"object\" || data === null) {\n      setTimeout(() => {\n        if (data === 404) {\n          callback(\"abort\", data);\n        } else {\n          callback(\"next\", defaultError);\n        }\n      });\n      return;\n    }\n    setTimeout(() => {\n      callback(\"success\", data);\n    });\n  }).catch(() => {\n    callback(\"next\", defaultError);\n  });\n};\nconst fetchAPIModule = {\n  prepare,\n  send\n};\n\nfunction sortIcons(icons) {\n  const result = {\n    loaded: [],\n    missing: [],\n    pending: []\n  };\n  const storage = /* @__PURE__ */ Object.create(null);\n  icons.sort((a, b) => {\n    if (a.provider !== b.provider) {\n      return a.provider.localeCompare(b.provider);\n    }\n    if (a.prefix !== b.prefix) {\n      return a.prefix.localeCompare(b.prefix);\n    }\n    return a.name.localeCompare(b.name);\n  });\n  let lastIcon = {\n    provider: \"\",\n    prefix: \"\",\n    name: \"\"\n  };\n  icons.forEach((icon) => {\n    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n      return;\n    }\n    lastIcon = icon;\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n    const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n    let list;\n    if (name in localStorage.icons) {\n      list = result.loaded;\n    } else if (prefix === \"\" || localStorage.missing.has(name)) {\n      list = result.missing;\n    } else {\n      list = result.pending;\n    }\n    const item = {\n      provider,\n      prefix,\n      name\n    };\n    list.push(item);\n  });\n  return result;\n}\n\nfunction removeCallback(storages, id) {\n  storages.forEach((storage) => {\n    const items = storage.loaderCallbacks;\n    if (items) {\n      storage.loaderCallbacks = items.filter((row) => row.id !== id);\n    }\n  });\n}\nfunction updateCallbacks(storage) {\n  if (!storage.pendingCallbacksFlag) {\n    storage.pendingCallbacksFlag = true;\n    setTimeout(() => {\n      storage.pendingCallbacksFlag = false;\n      const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n      if (!items.length) {\n        return;\n      }\n      let hasPending = false;\n      const provider = storage.provider;\n      const prefix = storage.prefix;\n      items.forEach((item) => {\n        const icons = item.icons;\n        const oldLength = icons.pending.length;\n        icons.pending = icons.pending.filter((icon) => {\n          if (icon.prefix !== prefix) {\n            return true;\n          }\n          const name = icon.name;\n          if (storage.icons[name]) {\n            icons.loaded.push({\n              provider,\n              prefix,\n              name\n            });\n          } else if (storage.missing.has(name)) {\n            icons.missing.push({\n              provider,\n              prefix,\n              name\n            });\n          } else {\n            hasPending = true;\n            return true;\n          }\n          return false;\n        });\n        if (icons.pending.length !== oldLength) {\n          if (!hasPending) {\n            removeCallback([storage], item.id);\n          }\n          item.callback(\n            icons.loaded.slice(0),\n            icons.missing.slice(0),\n            icons.pending.slice(0),\n            item.abort\n          );\n        }\n      });\n    });\n  }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n  const id = idCounter++;\n  const abort = removeCallback.bind(null, pendingSources, id);\n  if (!icons.pending.length) {\n    return abort;\n  }\n  const item = {\n    id,\n    icons,\n    callback,\n    abort\n  };\n  pendingSources.forEach((storage) => {\n    (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n  });\n  return abort;\n}\n\nfunction listToIcons(list, validate = true, simpleNames = false) {\n  const result = [];\n  list.forEach((item) => {\n    const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n    if (icon) {\n      result.push(icon);\n    }\n  });\n  return result;\n}\n\n// src/config.ts\nvar defaultConfig = {\n  resources: [],\n  index: 0,\n  timeout: 2e3,\n  rotate: 750,\n  random: false,\n  dataAfterTimeout: false\n};\n\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n  const resourcesCount = config.resources.length;\n  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n  let resources;\n  if (config.random) {\n    let list = config.resources.slice(0);\n    resources = [];\n    while (list.length > 1) {\n      const nextIndex = Math.floor(Math.random() * list.length);\n      resources.push(list[nextIndex]);\n      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n    }\n    resources = resources.concat(list);\n  } else {\n    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n  }\n  const startTime = Date.now();\n  let status = \"pending\";\n  let queriesSent = 0;\n  let lastError;\n  let timer = null;\n  let queue = [];\n  let doneCallbacks = [];\n  if (typeof done === \"function\") {\n    doneCallbacks.push(done);\n  }\n  function resetTimer() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function abort() {\n    if (status === \"pending\") {\n      status = \"aborted\";\n    }\n    resetTimer();\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function subscribe(callback, overwrite) {\n    if (overwrite) {\n      doneCallbacks = [];\n    }\n    if (typeof callback === \"function\") {\n      doneCallbacks.push(callback);\n    }\n  }\n  function getQueryStatus() {\n    return {\n      startTime,\n      payload,\n      status,\n      queriesSent,\n      queriesPending: queue.length,\n      subscribe,\n      abort\n    };\n  }\n  function failQuery() {\n    status = \"failed\";\n    doneCallbacks.forEach((callback) => {\n      callback(void 0, lastError);\n    });\n  }\n  function clearQueue() {\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function moduleResponse(item, response, data) {\n    const isError = response !== \"success\";\n    queue = queue.filter((queued) => queued !== item);\n    switch (status) {\n      case \"pending\":\n        break;\n      case \"failed\":\n        if (isError || !config.dataAfterTimeout) {\n          return;\n        }\n        break;\n      default:\n        return;\n    }\n    if (response === \"abort\") {\n      lastError = data;\n      failQuery();\n      return;\n    }\n    if (isError) {\n      lastError = data;\n      if (!queue.length) {\n        if (!resources.length) {\n          failQuery();\n        } else {\n          execNext();\n        }\n      }\n      return;\n    }\n    resetTimer();\n    clearQueue();\n    if (!config.random) {\n      const index = config.resources.indexOf(item.resource);\n      if (index !== -1 && index !== config.index) {\n        config.index = index;\n      }\n    }\n    status = \"completed\";\n    doneCallbacks.forEach((callback) => {\n      callback(data);\n    });\n  }\n  function execNext() {\n    if (status !== \"pending\") {\n      return;\n    }\n    resetTimer();\n    const resource = resources.shift();\n    if (resource === void 0) {\n      if (queue.length) {\n        timer = setTimeout(() => {\n          resetTimer();\n          if (status === \"pending\") {\n            clearQueue();\n            failQuery();\n          }\n        }, config.timeout);\n        return;\n      }\n      failQuery();\n      return;\n    }\n    const item = {\n      status: \"pending\",\n      resource,\n      callback: (status2, data) => {\n        moduleResponse(item, status2, data);\n      }\n    };\n    queue.push(item);\n    queriesSent++;\n    timer = setTimeout(execNext, config.rotate);\n    query(resource, payload, item.callback);\n  }\n  setTimeout(execNext);\n  return getQueryStatus;\n}\n\n// src/index.ts\nfunction initRedundancy(cfg) {\n  const config = {\n    ...defaultConfig,\n    ...cfg\n  };\n  let queries = [];\n  function cleanup() {\n    queries = queries.filter((item) => item().status === \"pending\");\n  }\n  function query(payload, queryCallback, doneCallback) {\n    const query2 = sendQuery(\n      config,\n      payload,\n      queryCallback,\n      (data, error) => {\n        cleanup();\n        if (doneCallback) {\n          doneCallback(data, error);\n        }\n      }\n    );\n    queries.push(query2);\n    return query2;\n  }\n  function find(callback) {\n    return queries.find((value) => {\n      return callback(value);\n    }) || null;\n  }\n  const instance = {\n    query,\n    find,\n    setIndex: (index) => {\n      config.index = index;\n    },\n    getIndex: () => config.index,\n    cleanup\n  };\n  return instance;\n}\n\nfunction emptyCallback$1() {\n}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n  if (!redundancyCache[provider]) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n      return;\n    }\n    const redundancy = initRedundancy(config);\n    const cachedReundancy = {\n      config,\n      redundancy\n    };\n    redundancyCache[provider] = cachedReundancy;\n  }\n  return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n  let redundancy;\n  let send;\n  if (typeof target === \"string\") {\n    const api = getAPIModule(target);\n    if (!api) {\n      callback(void 0, 424);\n      return emptyCallback$1;\n    }\n    send = api.send;\n    const cached = getRedundancyCache(target);\n    if (cached) {\n      redundancy = cached.redundancy;\n    }\n  } else {\n    const config = createAPIConfig(target);\n    if (config) {\n      redundancy = initRedundancy(config);\n      const moduleKey = target.resources ? target.resources[0] : \"\";\n      const api = getAPIModule(moduleKey);\n      if (api) {\n        send = api.send;\n      }\n    }\n  }\n  if (!redundancy || !send) {\n    callback(void 0, 424);\n    return emptyCallback$1;\n  }\n  return redundancy.query(query, send, callback)().abort;\n}\n\nfunction emptyCallback() {\n}\nfunction loadedNewIcons(storage) {\n  if (!storage.iconsLoaderFlag) {\n    storage.iconsLoaderFlag = true;\n    setTimeout(() => {\n      storage.iconsLoaderFlag = false;\n      updateCallbacks(storage);\n    });\n  }\n}\nfunction checkIconNamesForAPI(icons) {\n  const valid = [];\n  const invalid = [];\n  icons.forEach((name) => {\n    (name.match(matchIconName) ? valid : invalid).push(name);\n  });\n  return {\n    valid,\n    invalid\n  };\n}\nfunction parseLoaderResponse(storage, icons, data) {\n  function checkMissing() {\n    const pending = storage.pendingIcons;\n    icons.forEach((name) => {\n      if (pending) {\n        pending.delete(name);\n      }\n      if (!storage.icons[name]) {\n        storage.missing.add(name);\n      }\n    });\n  }\n  if (data && typeof data === \"object\") {\n    try {\n      const parsed = addIconSet(storage, data);\n      if (!parsed.length) {\n        checkMissing();\n        return;\n      }\n    } catch (err) {\n      console.error(err);\n    }\n  }\n  checkMissing();\n  loadedNewIcons(storage);\n}\nfunction parsePossiblyAsyncResponse(response, callback) {\n  if (response instanceof Promise) {\n    response.then((data) => {\n      callback(data);\n    }).catch(() => {\n      callback(null);\n    });\n  } else {\n    callback(response);\n  }\n}\nfunction loadNewIcons(storage, icons) {\n  if (!storage.iconsToLoad) {\n    storage.iconsToLoad = icons;\n  } else {\n    storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n  }\n  if (!storage.iconsQueueFlag) {\n    storage.iconsQueueFlag = true;\n    setTimeout(() => {\n      storage.iconsQueueFlag = false;\n      const { provider, prefix } = storage;\n      const icons2 = storage.iconsToLoad;\n      delete storage.iconsToLoad;\n      if (!icons2 || !icons2.length) {\n        return;\n      }\n      const customIconLoader = storage.loadIcon;\n      if (storage.loadIcons && (icons2.length > 1 || !customIconLoader)) {\n        parsePossiblyAsyncResponse(\n          storage.loadIcons(icons2, prefix, provider),\n          (data) => {\n            parseLoaderResponse(storage, icons2, data);\n          }\n        );\n        return;\n      }\n      if (customIconLoader) {\n        icons2.forEach((name) => {\n          const response = customIconLoader(name, prefix, provider);\n          parsePossiblyAsyncResponse(response, (data) => {\n            const iconSet = data ? {\n              prefix,\n              icons: {\n                [name]: data\n              }\n            } : null;\n            parseLoaderResponse(storage, [name], iconSet);\n          });\n        });\n        return;\n      }\n      const { valid, invalid } = checkIconNamesForAPI(icons2);\n      if (invalid.length) {\n        parseLoaderResponse(storage, invalid, null);\n      }\n      if (!valid.length) {\n        return;\n      }\n      const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;\n      if (!api) {\n        parseLoaderResponse(storage, valid, null);\n        return;\n      }\n      const params = api.prepare(provider, prefix, valid);\n      params.forEach((item) => {\n        sendAPIQuery(provider, item, (data) => {\n          parseLoaderResponse(storage, item.icons, data);\n        });\n      });\n    });\n  }\n}\nconst loadIcons = (icons, callback) => {\n  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n  const sortedIcons = sortIcons(cleanedIcons);\n  if (!sortedIcons.pending.length) {\n    let callCallback = true;\n    if (callback) {\n      setTimeout(() => {\n        if (callCallback) {\n          callback(\n            sortedIcons.loaded,\n            sortedIcons.missing,\n            sortedIcons.pending,\n            emptyCallback\n          );\n        }\n      });\n    }\n    return () => {\n      callCallback = false;\n    };\n  }\n  const newIcons = /* @__PURE__ */ Object.create(null);\n  const sources = [];\n  let lastProvider, lastPrefix;\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix } = icon;\n    if (prefix === lastPrefix && provider === lastProvider) {\n      return;\n    }\n    lastProvider = provider;\n    lastPrefix = prefix;\n    sources.push(getStorage(provider, prefix));\n    const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n    if (!providerNewIcons[prefix]) {\n      providerNewIcons[prefix] = [];\n    }\n  });\n  sortedIcons.pending.forEach((icon) => {\n    const { provider, prefix, name } = icon;\n    const storage = getStorage(provider, prefix);\n    const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n    if (!pendingQueue.has(name)) {\n      pendingQueue.add(name);\n      newIcons[provider][prefix].push(name);\n    }\n  });\n  sources.forEach((storage) => {\n    const list = newIcons[storage.provider][storage.prefix];\n    if (list.length) {\n      loadNewIcons(storage, list);\n    }\n  });\n  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon) => {\n  return new Promise((fulfill, reject) => {\n    const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n    if (!iconObj) {\n      reject(icon);\n      return;\n    }\n    loadIcons([iconObj || icon], (loaded) => {\n      if (loaded.length && iconObj) {\n        const data = getIconData(iconObj);\n        if (data) {\n          fulfill({\n            ...defaultIconProps,\n            ...data\n          });\n          return;\n        }\n      }\n      reject(icon);\n    });\n  });\n};\n\nfunction setCustomIconsLoader(loader, prefix, provider) {\n  getStorage(provider || \"\", prefix).loadIcons = loader;\n}\nfunction setCustomIconLoader(loader, prefix, provider) {\n  getStorage(provider || \"\", prefix).loadIcon = loader;\n}\n\nfunction mergeCustomisations(defaults, item) {\n  const result = {\n    ...defaults\n  };\n  for (const key in item) {\n    const value = item[key];\n    const valueType = typeof value;\n    if (key in defaultIconSizeCustomisations) {\n      if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n        result[key] = value;\n      }\n    } else if (valueType === typeof result[key]) {\n      result[key] = key === \"rotate\" ? value % 4 : value;\n    }\n  }\n  return result;\n}\n\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n  flip.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"horizontal\":\n        custom.hFlip = true;\n        break;\n      case \"vertical\":\n        custom.vFlip = true;\n        break;\n    }\n  });\n}\n\nfunction rotateFromString(value, defaultValue = 0) {\n  const units = value.replace(/^-?[0-9.]*/, \"\");\n  function cleanup(value2) {\n    while (value2 < 0) {\n      value2 += 4;\n    }\n    return value2 % 4;\n  }\n  if (units === \"\") {\n    const num = parseInt(value);\n    return isNaN(num) ? 0 : cleanup(num);\n  } else if (units !== value) {\n    let split = 0;\n    switch (units) {\n      case \"%\":\n        split = 25;\n        break;\n      case \"deg\":\n        split = 90;\n    }\n    if (split) {\n      let num = parseFloat(value.slice(0, value.length - units.length));\n      if (isNaN(num)) {\n        return 0;\n      }\n      num = num / split;\n      return num % 1 === 0 ? cleanup(num) : 0;\n    }\n  }\n  return defaultValue;\n}\n\nfunction iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nfunction encodeSVGforURL(svg) {\n  return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n  return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n  return 'url(\"' + svgToData(svg) + '\")';\n}\n\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false,\n};\n\n/**\n * Default SVG attributes\n */\nconst svgDefaults = {\n    'xmlns': 'http://www.w3.org/2000/svg',\n    'xmlns:xlink': 'http://www.w3.org/1999/xlink',\n    'aria-hidden': true,\n    'role': 'img',\n};\n/**\n * Style modes\n */\nconst commonProps = {\n    display: 'inline-block',\n};\nconst monotoneProps = {\n    backgroundColor: 'currentColor',\n};\nconst coloredProps = {\n    backgroundColor: 'transparent',\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    Image: 'var(--svg)',\n    Repeat: 'no-repeat',\n    Size: '100% 100%',\n};\nconst propsToAddTo = {\n    webkitMask: monotoneProps,\n    mask: monotoneProps,\n    background: coloredProps,\n};\nfor (const prefix in propsToAddTo) {\n    const list = propsToAddTo[prefix];\n    for (const prop in propsToAdd) {\n        list[prefix + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Aliases for customisations.\n * In Vue 'v-' properties are reserved, so v-flip must be renamed\n */\nconst customisationAliases = {};\n['horizontal', 'vertical'].forEach((prefix) => {\n    const attr = prefix.slice(0, 1) + 'Flip';\n    // vertical-flip\n    customisationAliases[prefix + '-flip'] = attr;\n    // v-flip\n    customisationAliases[prefix.slice(0, 1) + '-flip'] = attr;\n    // verticalFlip\n    customisationAliases[prefix + 'Flip'] = attr;\n});\n/**\n * Fix size: add 'px' to numbers\n */\nfunction fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? 'px' : '');\n}\n/**\n * Render icon\n */\nconst render = (\n// Icon must be validated before calling this function\nicon, \n// Partial properties\nprops) => {\n    // Split properties\n    const customisations = mergeCustomisations(defaultExtendedIconCustomisations, props);\n    const componentProps = { ...svgDefaults };\n    // Check mode\n    const mode = props.mode || 'svg';\n    // Copy style\n    const style = {};\n    const propsStyle = props.style;\n    const customStyle = typeof propsStyle === 'object' && !(propsStyle instanceof Array)\n        ? propsStyle\n        : {};\n    // Get element properties\n    for (let key in props) {\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch (key) {\n            // Properties to ignore\n            case 'icon':\n            case 'style':\n            case 'onLoad':\n            case 'mode':\n            case 'ssr':\n                break;\n            // Boolean attributes\n            case 'inline':\n            case 'hFlip':\n            case 'vFlip':\n                customisations[key] =\n                    value === true || value === 'true' || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case 'flip':\n                if (typeof value === 'string') {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Color: override style\n            case 'color':\n                style.color = value;\n                break;\n            // Rotation as string\n            case 'rotate':\n                if (typeof value === 'string') {\n                    customisations[key] = rotateFromString(value);\n                }\n                else if (typeof value === 'number') {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case 'ariaHidden':\n            case 'aria-hidden':\n                // Vue transforms 'aria-hidden' property to 'ariaHidden'\n                if (value !== true && value !== 'true') {\n                    delete componentProps['aria-hidden'];\n                }\n                break;\n            default: {\n                const alias = customisationAliases[key];\n                if (alias) {\n                    // Aliases for boolean customisations\n                    if (value === true || value === 'true' || value === 1) {\n                        customisations[alias] = true;\n                    }\n                }\n                else if (defaultExtendedIconCustomisations[key] === void 0) {\n                    // Copy missing property if it does not exist in customisations\n                    componentProps[key] = value;\n                }\n            }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    const renderAttribs = item.attributes;\n    // Inline display\n    if (customisations.inline) {\n        style.verticalAlign = '-0.125em';\n    }\n    if (mode === 'svg') {\n        // Add style\n        componentProps.style = {\n            ...style,\n            ...customStyle,\n        };\n        // Add icon stuff\n        Object.assign(componentProps, renderAttribs);\n        // Counter for ids based on \"id\" property to render icons consistently on server and client\n        let localCounter = 0;\n        let id = props.id;\n        if (typeof id === 'string') {\n            // Convert '-' to '_' to avoid errors in animations\n            id = id.replace(/-/g, '_');\n        }\n        // Add innerHTML and style to props\n        componentProps['innerHTML'] = replaceIDs(item.body, id ? () => id + 'ID' + localCounter++ : 'iconifyVue');\n        // Render icon\n        return h('svg', componentProps);\n    }\n    // Render <span> with style\n    const { body, width, height } = icon;\n    const useMask = mode === 'mask' ||\n        (mode === 'bg' ? false : body.indexOf('currentColor') !== -1);\n    // Generate SVG\n    const html = iconToHTML(body, {\n        ...renderAttribs,\n        width: width + '',\n        height: height + '',\n    });\n    // Generate style\n    componentProps.style = {\n        ...style,\n        '--svg': svgToURL(html),\n        'width': fixSize(renderAttribs.width),\n        'height': fixSize(renderAttribs.height),\n        ...commonProps,\n        ...(useMask ? monotoneProps : coloredProps),\n        ...customStyle,\n    };\n    return h('span', componentProps);\n};\n\n/**\n * Enable cache\n *\n * @deprecated No longer used\n */\nfunction enableCache(storage) {\n    //\n}\n/**\n * Disable cache\n *\n * @deprecated No longer used\n */\nfunction disableCache(storage) {\n    //\n}\n/**\n * Initialise stuff\n */\n// Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule('', fetchAPIModule);\n/**\n * Browser stuff\n */\nif (typeof document !== 'undefined' && typeof window !== 'undefined') {\n    const _window = window;\n    // Load icons from global \"IconifyPreload\"\n    if (_window.IconifyPreload !== void 0) {\n        const preload = _window.IconifyPreload;\n        const err = 'Invalid IconifyPreload syntax.';\n        if (typeof preload === 'object' && preload !== null) {\n            (preload instanceof Array ? preload : [preload]).forEach((item) => {\n                try {\n                    if (\n                    // Check if item is an object and not null/array\n                    typeof item !== 'object' ||\n                        item === null ||\n                        item instanceof Array ||\n                        // Check for 'icons' and 'prefix'\n                        typeof item.icons !== 'object' ||\n                        typeof item.prefix !== 'string' ||\n                        // Add icon set\n                        !addCollection(item)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            });\n        }\n    }\n    // Set API from global \"IconifyProviders\"\n    if (_window.IconifyProviders !== void 0) {\n        const providers = _window.IconifyProviders;\n        if (typeof providers === 'object' && providers !== null) {\n            for (let key in providers) {\n                const err = 'IconifyProviders[' + key + '] is invalid.';\n                try {\n                    const value = providers[key];\n                    if (typeof value !== 'object' ||\n                        !value ||\n                        value.resources === void 0) {\n                        continue;\n                    }\n                    if (!addAPIProvider(key, value)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            }\n        }\n    }\n}\n/**\n * Empty icon data, rendered when icon is not available\n */\nconst emptyIcon = {\n    ...defaultIconProps,\n    body: '',\n};\nconst Icon = defineComponent({\n    // Do not inherit other attributes: it is handled by render()\n    inheritAttrs: false,\n    // Set initial data\n    data() {\n        return {\n            // Current icon name\n            _name: '',\n            // Loading\n            _loadingIcon: null,\n            // Mounted status\n            iconMounted: false,\n            // Callback counter to trigger re-render\n            counter: 0,\n        };\n    },\n    mounted() {\n        // Mark as mounted\n        this.iconMounted = true;\n    },\n    unmounted() {\n        this.abortLoading();\n    },\n    methods: {\n        abortLoading() {\n            if (this._loadingIcon) {\n                this._loadingIcon.abort();\n                this._loadingIcon = null;\n            }\n        },\n        // Get data for icon to render or null\n        getIcon(icon, onload, customise) {\n            // Icon is an object\n            if (typeof icon === 'object' &&\n                icon !== null &&\n                typeof icon.body === 'string') {\n                // Stop loading\n                this._name = '';\n                this.abortLoading();\n                return {\n                    data: icon,\n                };\n            }\n            // Invalid icon?\n            let iconName;\n            if (typeof icon !== 'string' ||\n                (iconName = stringToIcon(icon, false, true)) === null) {\n                this.abortLoading();\n                return null;\n            }\n            // Load icon\n            let data = getIconData(iconName);\n            if (!data) {\n                // Icon data is not available\n                if (!this._loadingIcon || this._loadingIcon.name !== icon) {\n                    // New icon to load\n                    this.abortLoading();\n                    this._name = '';\n                    if (data !== null) {\n                        // Icon was not loaded\n                        this._loadingIcon = {\n                            name: icon,\n                            abort: loadIcons([iconName], () => {\n                                this.counter++;\n                            }),\n                        };\n                    }\n                }\n                return null;\n            }\n            // Icon data is available\n            this.abortLoading();\n            if (this._name !== icon) {\n                this._name = icon;\n                if (onload) {\n                    onload(icon);\n                }\n            }\n            // Customise icon\n            if (customise) {\n                // Clone data and customise it\n                data = Object.assign({}, data);\n                const customised = customise(data.body, iconName.name, iconName.prefix, iconName.provider);\n                if (typeof customised === 'string') {\n                    data.body = customised;\n                }\n            }\n            // Add classes\n            const classes = ['iconify'];\n            if (iconName.prefix !== '') {\n                classes.push('iconify--' + iconName.prefix);\n            }\n            if (iconName.provider !== '') {\n                classes.push('iconify--' + iconName.provider);\n            }\n            return { data, classes };\n        },\n    },\n    // Render icon\n    render() {\n        // Re-render when counter changes\n        this.counter;\n        const props = this.$attrs;\n        // Get icon data\n        const icon = this.iconMounted || props.ssr\n            ? this.getIcon(props.icon, props.onLoad, props.customise)\n            : null;\n        // Validate icon object\n        if (!icon) {\n            return render(emptyIcon, props);\n        }\n        // Add classes\n        let newProps = props;\n        if (icon.classes) {\n            newProps = {\n                ...props,\n                class: (typeof props['class'] === 'string'\n                    ? props['class'] + ' '\n                    : '') + icon.classes.join(' '),\n            };\n        }\n        // Render icon\n        return render({\n            ...defaultIconProps,\n            ...icon.data,\n        }, newProps);\n    },\n});\n/**\n * Internal API\n */\nconst _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders,\n};\n\nexport { Icon, _api, addAPIProvider, addCollection, addIcon, iconToSVG as buildIcon, calculateSize, disableCache, enableCache, getIcon, iconLoaded as iconExists, iconLoaded, listIcons, loadIcon, loadIcons, replaceIDs, setCustomIconLoader, setCustomIconsLoader };\n"], "mappings": ";;;;;;;AAEA,IAAM,gBAAgB;AACtB,IAAM,eAAe,CAAC,OAAO,UAAU,iBAAiB,WAAW,OAAO;AACxE,QAAM,iBAAiB,MAAM,MAAM,GAAG;AACtC,MAAI,MAAM,MAAM,GAAG,CAAC,MAAM,KAAK;AAC7B,QAAI,eAAe,SAAS,KAAK,eAAe,SAAS,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,eAAW,eAAe,MAAM,EAAE,MAAM,CAAC;AAAA,EAC3C;AACA,MAAI,eAAe,SAAS,KAAK,CAAC,eAAe,QAAQ;AACvD,WAAO;AAAA,EACT;AACA,MAAI,eAAe,SAAS,GAAG;AAC7B,UAAM,QAAQ,eAAe,IAAI;AACjC,UAAM,SAAS,eAAe,IAAI;AAClC,UAAM,SAAS;AAAA;AAAA,MAEb,UAAU,eAAe,SAAS,IAAI,eAAe,CAAC,IAAI;AAAA,MAC1D;AAAA,MACA,MAAM;AAAA,IACR;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,QAAM,OAAO,eAAe,CAAC;AAC7B,QAAM,gBAAgB,KAAK,MAAM,GAAG;AACpC,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ,cAAc,MAAM;AAAA,MAC5B,MAAM,cAAc,KAAK,GAAG;AAAA,IAC9B;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,MAAI,mBAAmB,aAAa,IAAI;AACtC,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF;AACA,WAAO,YAAY,CAAC,iBAAiB,QAAQ,eAAe,IAAI,OAAO;AAAA,EACzE;AACA,SAAO;AACT;AACA,IAAM,mBAAmB,CAAC,MAAM,oBAAoB;AAClD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AAAA;AAAA,IAEN,mBAAmB,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,KAAK;AACtE;AAEA,IAAM,wBAAwB,OAAO;AAAA,EACnC;AAAA,IACE,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AACA,IAAM,6BAA6B,OAAO,OAAO;AAAA,EAC/C,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT,CAAC;AACD,IAAM,mBAAmB,OAAO,OAAO;AAAA,EACrC,GAAG;AAAA,EACH,GAAG;AACL,CAAC;AACD,IAAM,2BAA2B,OAAO,OAAO;AAAA,EAC7C,GAAG;AAAA,EACH,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;AAED,SAAS,yBAAyB,MAAM,MAAM;AAC5C,QAAM,SAAS,CAAC;AAChB,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,WAAW,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM;AAC3D,MAAI,QAAQ;AACV,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,QAAM,SAAS,yBAAyB,QAAQ,KAAK;AACrD,aAAW,OAAO,0BAA0B;AAC1C,QAAI,OAAO,4BAA4B;AACrC,UAAI,OAAO,UAAU,EAAE,OAAO,SAAS;AACrC,eAAO,GAAG,IAAI,2BAA2B,GAAG;AAAA,MAC9C;AAAA,IACF,WAAW,OAAO,OAAO;AACvB,aAAO,GAAG,IAAI,MAAM,GAAG;AAAA,IACzB,WAAW,OAAO,QAAQ;AACxB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,QAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAS,QAAQ,MAAM;AACrB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,SAAS,IAAI,IAAI,CAAC;AAAA,IAC3B;AACA,QAAI,EAAE,QAAQ,WAAW;AACvB,eAAS,IAAI,IAAI;AACjB,YAAM,SAAS,QAAQ,IAAI,KAAK,QAAQ,IAAI,EAAE;AAC9C,YAAM,QAAQ,UAAU,QAAQ,MAAM;AACtC,UAAI,OAAO;AACT,iBAAS,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK;AAAA,MACxC;AAAA,IACF;AACA,WAAO,SAAS,IAAI;AAAA,EACtB;AACA,EAAC,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO,KAAK,OAAO,CAAC,EAAG,QAAQ,OAAO;AACjE,SAAO;AACT;AAEA,SAAS,oBAAoB,MAAM,MAAM,MAAM;AAC7C,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,MAAI,eAAe,CAAC;AACpB,WAAS,MAAM,OAAO;AACpB,mBAAe;AAAA,MACb,MAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,QAAM,IAAI;AACV,OAAK,QAAQ,KAAK;AAClB,SAAO,cAAc,MAAM,YAAY;AACzC;AAEA,SAAS,aAAa,MAAM,UAAU;AACpC,QAAM,QAAQ,CAAC;AACf,MAAI,OAAO,SAAS,YAAY,OAAO,KAAK,UAAU,UAAU;AAC9D,WAAO;AAAA,EACT;AACA,MAAI,KAAK,qBAAqB,OAAO;AACnC,SAAK,UAAU,QAAQ,CAAC,SAAS;AAC/B,eAAS,MAAM,IAAI;AACnB,YAAM,KAAK,IAAI;AAAA,IACjB,CAAC;AAAA,EACH;AACA,QAAM,OAAO,aAAa,IAAI;AAC9B,aAAW,QAAQ,MAAM;AACvB,UAAM,OAAO,KAAK,IAAI;AACtB,QAAI,MAAM;AACR,eAAS,MAAM,oBAAoB,MAAM,MAAM,IAAI,CAAC;AACpD,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,2BAA2B;AAAA,EAC/B,UAAU;AAAA,EACV,SAAS,CAAC;AAAA,EACV,WAAW,CAAC;AAAA,EACZ,GAAG;AACL;AACA,SAAS,mBAAmB,MAAM,UAAU;AAC1C,aAAW,QAAQ,UAAU;AAC3B,QAAI,QAAQ,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,SAAS,IAAI,GAAG;AAC/D,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,KAAK;AACnC,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AACb,MAAI,OAAO,KAAK,WAAW,YAAY,CAAC,IAAI,SAAS,OAAO,IAAI,UAAU,UAAU;AAClF,WAAO;AAAA,EACT;AACA,MAAI,CAAC,mBAAmB,KAAK,wBAAwB,GAAG;AACtD,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,KAAK;AACnB,aAAW,QAAQ,OAAO;AACxB,UAAM,OAAO,MAAM,IAAI;AACvB;AAAA;AAAA,MAEE,CAAC;AAAA,MACD,OAAO,KAAK,SAAS;AAAA,MACrB,CAAC;AAAA,QACC;AAAA,QACA;AAAA,MACF;AAAA,MACA;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,aAAW,QAAQ,SAAS;AAC1B,UAAM,OAAO,QAAQ,IAAI;AACzB,UAAM,SAAS,KAAK;AACpB;AAAA;AAAA,MAEE,CAAC;AAAA,MACD,OAAO,WAAW,YAAY,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,MAAM;AAAA,MAC/D,CAAC;AAAA,QACC;AAAA,QACA;AAAA,MACF;AAAA,MACA;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,cAA8B,uBAAO,OAAO,IAAI;AACtD,SAAS,WAAW,UAAU,QAAQ;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAuB,uBAAO,OAAO,IAAI;AAAA,IACzC,SAAyB,oBAAI,IAAI;AAAA,EACnC;AACF;AACA,SAAS,WAAW,UAAU,QAAQ;AACpC,QAAM,kBAAkB,YAAY,QAAQ,MAAM,YAAY,QAAQ,IAAoB,uBAAO,OAAO,IAAI;AAC5G,SAAO,gBAAgB,MAAM,MAAM,gBAAgB,MAAM,IAAI,WAAW,UAAU,MAAM;AAC1F;AACA,SAAS,WAAWA,UAAS,MAAM;AACjC,MAAI,CAAC,uBAAuB,IAAI,GAAG;AACjC,WAAO,CAAC;AAAA,EACV;AACA,SAAO,aAAa,MAAM,CAAC,MAAM,SAAS;AACxC,QAAI,MAAM;AACR,MAAAA,SAAQ,MAAM,IAAI,IAAI;AAAA,IACxB,OAAO;AACL,MAAAA,SAAQ,QAAQ,IAAI,IAAI;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AACA,SAAS,iBAAiBA,UAAS,MAAM,MAAM;AAC7C,MAAI;AACF,QAAI,OAAO,KAAK,SAAS,UAAU;AACjC,MAAAA,SAAQ,MAAM,IAAI,IAAI,EAAE,GAAG,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF,SAAS,KAAK;AAAA,EACd;AACA,SAAO;AACT;AACA,SAAS,UAAU,UAAU,QAAQ;AACnC,MAAI,WAAW,CAAC;AAChB,QAAM,YAAY,OAAO,aAAa,WAAW,CAAC,QAAQ,IAAI,OAAO,KAAK,WAAW;AACrF,YAAU,QAAQ,CAAC,cAAc;AAC/B,UAAM,WAAW,OAAO,cAAc,YAAY,OAAO,WAAW,WAAW,CAAC,MAAM,IAAI,OAAO,KAAK,YAAY,SAAS,KAAK,CAAC,CAAC;AAClI,aAAS,QAAQ,CAAC,YAAY;AAC5B,YAAMA,WAAU,WAAW,WAAW,OAAO;AAC7C,iBAAW,SAAS;AAAA,QAClB,OAAO,KAAKA,SAAQ,KAAK,EAAE;AAAA,UACzB,CAAC,UAAU,cAAc,KAAK,MAAM,YAAY,MAAM,MAAM,UAAU,MAAM;AAAA,QAC9E;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAEA,IAAI,cAAc;AAClB,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,UAAU,WAAW;AAC9B,kBAAc;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,YAAY,MAAM;AACzB,QAAM,OAAO,OAAO,SAAS,WAAW,aAAa,MAAM,MAAM,WAAW,IAAI;AAChF,MAAI,MAAM;AACR,UAAMA,WAAU,WAAW,KAAK,UAAU,KAAK,MAAM;AACrD,UAAM,WAAW,KAAK;AACtB,WAAOA,SAAQ,MAAM,QAAQ,MAAMA,SAAQ,QAAQ,IAAI,QAAQ,IAAI,OAAO;AAAA,EAC5E;AACF;AACA,SAAS,QAAQ,MAAM,MAAM;AAC3B,QAAM,OAAO,aAAa,MAAM,MAAM,WAAW;AACjD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,QAAMA,WAAU,WAAW,KAAK,UAAU,KAAK,MAAM;AACrD,MAAI,MAAM;AACR,WAAO,iBAAiBA,UAAS,KAAK,MAAM,IAAI;AAAA,EAClD,OAAO;AACL,IAAAA,SAAQ,QAAQ,IAAI,KAAK,IAAI;AAC7B,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,MAAM,UAAU;AACrC,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,eAAW,KAAK,YAAY;AAAA,EAC9B;AACA,MAAI,eAAe,CAAC,YAAY,CAAC,KAAK,QAAQ;AAC5C,QAAI,QAAQ;AACZ,QAAI,uBAAuB,IAAI,GAAG;AAChC,WAAK,SAAS;AACd,mBAAa,MAAM,CAAC,MAAM,SAAS;AACjC,YAAI,QAAQ,MAAM,IAAI,GAAG;AACvB,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK;AACpB,MAAI,CAAC,iBAAiB;AAAA,IACpB;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC,GAAG;AACF,WAAO;AAAA,EACT;AACA,QAAMA,WAAU,WAAW,UAAU,MAAM;AAC3C,SAAO,CAAC,CAAC,WAAWA,UAAS,IAAI;AACnC;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,CAAC,CAAC,YAAY,IAAI;AAC3B;AACA,SAAS,QAAQ,MAAM;AACrB,QAAM,SAAS,YAAY,IAAI;AAC/B,SAAO,SAAS;AAAA,IACd,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AACN;AAEA,IAAM,gCAAgC,OAAO,OAAO;AAAA,EAClD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,IAAM,4BAA4B,OAAO,OAAO;AAAA;AAAA,EAE9C,GAAG;AAAA;AAAA,EAEH,GAAG;AACL,CAAC;AAED,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,SAAS,cAAc,MAAM,OAAO,WAAW;AAC7C,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,cAAY,aAAa;AACzB,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK,KAAK,OAAO,QAAQ,SAAS,IAAI;AAAA,EAC/C;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,UAAU;AACtC,MAAI,aAAa,QAAQ,CAAC,SAAS,QAAQ;AACzC,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC;AAClB,MAAI,OAAO,SAAS,MAAM;AAC1B,MAAI,WAAW,UAAU,KAAK,IAAI;AAClC,SAAO,MAAM;AACX,QAAI,UAAU;AACZ,YAAM,MAAM,WAAW,IAAI;AAC3B,UAAI,MAAM,GAAG,GAAG;AACd,iBAAS,KAAK,IAAI;AAAA,MACpB,OAAO;AACL,iBAAS,KAAK,KAAK,KAAK,MAAM,QAAQ,SAAS,IAAI,SAAS;AAAA,MAC9D;AAAA,IACF,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,WAAO,SAAS,MAAM;AACtB,QAAI,SAAS,QAAQ;AACnB,aAAO,SAAS,KAAK,EAAE;AAAA,IACzB;AACA,eAAW,CAAC;AAAA,EACd;AACF;AAEA,SAAS,aAAa,SAAS,MAAM,QAAQ;AAC3C,MAAI,OAAO;AACX,QAAM,QAAQ,QAAQ,QAAQ,MAAM,GAAG;AACvC,SAAO,SAAS,GAAG;AACjB,UAAM,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACxC,UAAM,MAAM,QAAQ,QAAQ,OAAO,GAAG;AACtC,QAAI,UAAU,MAAM,QAAQ,IAAI;AAC9B;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,QAAQ,KAAK,GAAG;AACvC,QAAI,WAAW,IAAI;AACjB;AAAA,IACF;AACA,YAAQ,QAAQ,MAAM,QAAQ,GAAG,GAAG,EAAE,KAAK;AAC3C,cAAU,QAAQ,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,QAAQ,MAAM,SAAS,CAAC;AAAA,EACrE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,MAAM,SAAS;AAC1C,SAAO,OAAO,WAAW,OAAO,YAAY,UAAU;AACxD;AACA,SAAS,eAAe,MAAM,OAAO,KAAK;AACxC,QAAM,QAAQ,aAAa,IAAI;AAC/B,SAAO,oBAAoB,MAAM,MAAM,QAAQ,MAAM,UAAU,GAAG;AACpE;AAEA,IAAM,iBAAiB,CAAC,UAAU,UAAU,WAAW,UAAU,eAAe,UAAU;AAC1F,SAAS,UAAU,MAAM,gBAAgB;AACvC,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,qBAAqB;AAAA,IACzB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,MAAM;AAAA,IACV,MAAM,SAAS;AAAA,IACf,KAAK,SAAS;AAAA,IACd,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,EACnB;AACA,MAAI,OAAO,SAAS;AACpB,GAAC,UAAU,kBAAkB,EAAE,QAAQ,CAAC,UAAU;AAChD,UAAM,kBAAkB,CAAC;AACzB,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,MAAM;AACpB,QAAI,WAAW,MAAM;AACrB,QAAI,OAAO;AACT,UAAI,OAAO;AACT,oBAAY;AAAA,MACd,OAAO;AACL,wBAAgB;AAAA,UACd,gBAAgB,IAAI,QAAQ,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACtF;AACA,wBAAgB,KAAK,aAAa;AAClC,YAAI,MAAM,IAAI,OAAO;AAAA,MACvB;AAAA,IACF,WAAW,OAAO;AAChB,sBAAgB;AAAA,QACd,gBAAgB,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI;AAAA,MACvF;AACA,sBAAgB,KAAK,aAAa;AAClC,UAAI,MAAM,IAAI,OAAO;AAAA,IACvB;AACA,QAAI;AACJ,QAAI,WAAW,GAAG;AAChB,kBAAY,KAAK,MAAM,WAAW,CAAC,IAAI;AAAA,IACzC;AACA,eAAW,WAAW;AACtB,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,oBAAY,IAAI,SAAS,IAAI,IAAI;AACjC,wBAAgB;AAAA,UACd,eAAe,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACrE;AACA;AAAA,MACF,KAAK;AACH,wBAAgB;AAAA,UACd,iBAAiB,IAAI,QAAQ,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACxG;AACA;AAAA,MACF,KAAK;AACH,oBAAY,IAAI,QAAQ,IAAI,IAAI;AAChC,wBAAgB;AAAA,UACd,gBAAgB,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACtE;AACA;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,GAAG;AACtB,UAAI,IAAI,SAAS,IAAI,KAAK;AACxB,oBAAY,IAAI;AAChB,YAAI,OAAO,IAAI;AACf,YAAI,MAAM;AAAA,MACZ;AACA,UAAI,IAAI,UAAU,IAAI,QAAQ;AAC5B,oBAAY,IAAI;AAChB,YAAI,QAAQ,IAAI;AAChB,YAAI,SAAS;AAAA,MACf;AAAA,IACF;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO;AAAA,QACL;AAAA,QACA,mBAAmB,gBAAgB,KAAK,GAAG,IAAI;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,mBAAmB;AAC/C,QAAM,uBAAuB,mBAAmB;AAChD,QAAM,WAAW,IAAI;AACrB,QAAM,YAAY,IAAI;AACtB,MAAI;AACJ,MAAI;AACJ,MAAI,wBAAwB,MAAM;AAChC,aAAS,yBAAyB,OAAO,QAAQ,yBAAyB,SAAS,YAAY;AAC/F,YAAQ,cAAc,QAAQ,WAAW,SAAS;AAAA,EACpD,OAAO;AACL,YAAQ,wBAAwB,SAAS,WAAW;AACpD,aAAS,yBAAyB,OAAO,cAAc,OAAO,YAAY,QAAQ,IAAI,yBAAyB,SAAS,YAAY;AAAA,EACtI;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,CAAC,MAAM,UAAU;AAC/B,QAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,iBAAW,IAAI,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,EACF;AACA,UAAQ,SAAS,KAAK;AACtB,UAAQ,UAAU,MAAM;AACxB,QAAM,UAAU,CAAC,IAAI,MAAM,IAAI,KAAK,UAAU,SAAS;AACvD,aAAW,UAAU,QAAQ,KAAK,GAAG;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,QAAQ;AACd,IAAM,eAAe,cAAc,KAAK,IAAI,EAAE,SAAS,EAAE,KAAK,KAAK,OAAO,IAAI,WAAW,GAAG,SAAS,EAAE;AACvG,IAAI,UAAU;AACd,SAAS,WAAW,MAAM,SAAS,cAAc;AAC/C,QAAM,MAAM,CAAC;AACb,MAAI;AACJ,SAAO,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC/B,QAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EACnB;AACA,MAAI,CAAC,IAAI,QAAQ;AACf,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,KAAK,OAAO,IAAI,WAAW,KAAK,IAAI,GAAG,SAAS,EAAE;AAC7E,MAAI,QAAQ,CAAC,OAAO;AAClB,UAAM,QAAQ,OAAO,WAAW,aAAa,OAAO,EAAE,IAAI,UAAU,WAAW,SAAS;AACxF,UAAM,YAAY,GAAG,QAAQ,uBAAuB,MAAM;AAC1D,WAAO,KAAK;AAAA;AAAA;AAAA,MAGV,IAAI,OAAO,aAAa,YAAY,oBAAoB,GAAG;AAAA,MAC3D,OAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,SAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG,EAAE;AAC/C,SAAO;AACT;AAEA,IAAM,UAA0B,uBAAO,OAAO,IAAI;AAClD,SAAS,aAAa,UAAU,MAAM;AACpC,UAAQ,QAAQ,IAAI;AACtB;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,QAAQ,QAAQ,KAAK,QAAQ,EAAE;AACxC;AAEA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI;AACJ,MAAI,OAAO,OAAO,cAAc,UAAU;AACxC,gBAAY,CAAC,OAAO,SAAS;AAAA,EAC/B,OAAO;AACL,gBAAY,OAAO;AACnB,QAAI,EAAE,qBAAqB,UAAU,CAAC,UAAU,QAAQ;AACtD,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,SAAS;AAAA;AAAA,IAEb;AAAA;AAAA,IAEA,MAAM,OAAO,QAAQ;AAAA;AAAA,IAErB,QAAQ,OAAO,UAAU;AAAA;AAAA,IAEzB,QAAQ,OAAO,UAAU;AAAA;AAAA,IAEzB,SAAS,OAAO,WAAW;AAAA;AAAA,IAE3B,QAAQ,OAAO,WAAW;AAAA;AAAA,IAE1B,OAAO,OAAO,SAAS;AAAA;AAAA,IAEvB,kBAAkB,OAAO,qBAAqB;AAAA,EAChD;AACA,SAAO;AACT;AACA,IAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AACF;AACA,IAAM,cAAc,CAAC;AACrB,OAAO,mBAAmB,SAAS,GAAG;AACpC,MAAI,mBAAmB,WAAW,GAAG;AACnC,gBAAY,KAAK,mBAAmB,MAAM,CAAC;AAAA,EAC7C,OAAO;AACL,QAAI,KAAK,OAAO,IAAI,KAAK;AACvB,kBAAY,KAAK,mBAAmB,MAAM,CAAC;AAAA,IAC7C,OAAO;AACL,kBAAY,KAAK,mBAAmB,IAAI,CAAC;AAAA,IAC3C;AAAA,EACF;AACF;AACA,cAAc,EAAE,IAAI,gBAAgB;AAAA,EAClC,WAAW,CAAC,4BAA4B,EAAE,OAAO,WAAW;AAC9D,CAAC;AACD,SAAS,eAAe,UAAU,cAAc;AAC9C,QAAM,SAAS,gBAAgB,YAAY;AAC3C,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,EACT;AACA,gBAAc,QAAQ,IAAI;AAC1B,SAAO;AACT;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,cAAc,QAAQ;AAC/B;AACA,SAAS,mBAAmB;AAC1B,SAAO,OAAO,KAAK,aAAa;AAClC;AAEA,IAAM,cAAc,MAAM;AACxB,MAAI;AACJ,MAAI;AACF,eAAW;AACX,QAAI,OAAO,aAAa,YAAY;AAClC,aAAO;AAAA,IACT;AAAA,EACF,SAAS,KAAK;AAAA,EACd;AACF;AACA,IAAI,cAAc,YAAY;AAC9B,SAAS,SAAS,QAAQ;AACxB,gBAAc;AAChB;AACA,SAAS,WAAW;AAClB,SAAO;AACT;AACA,SAAS,mBAAmB,UAAU,QAAQ;AAC5C,QAAM,SAAS,aAAa,QAAQ;AACpC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,CAAC,OAAO,QAAQ;AAClB,aAAS;AAAA,EACX,OAAO;AACL,QAAI,gBAAgB;AACpB,WAAO,UAAU,QAAQ,CAAC,SAAS;AACjC,YAAM,OAAO;AACb,sBAAgB,KAAK,IAAI,eAAe,KAAK,MAAM;AAAA,IACrD,CAAC;AACD,UAAM,MAAM,SAAS;AACrB,aAAS,OAAO,SAAS,gBAAgB,OAAO,KAAK,SAAS,IAAI;AAAA,EACpE;AACA,SAAO;AACT;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,WAAW;AACpB;AACA,IAAM,UAAU,CAAC,UAAU,QAAQ,UAAU;AAC3C,QAAM,UAAU,CAAC;AACjB,QAAM,YAAY,mBAAmB,UAAU,MAAM;AACrD,QAAM,OAAO;AACb,MAAI,OAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,EACV;AACA,MAAI,SAAS;AACb,QAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,cAAU,KAAK,SAAS;AACxB,QAAI,UAAU,aAAa,QAAQ,GAAG;AACpC,cAAQ,KAAK,IAAI;AACjB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,CAAC;AAAA,MACV;AACA,eAAS,KAAK;AAAA,IAChB;AACA,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB,CAAC;AACD,UAAQ,KAAK,IAAI;AACjB,SAAO;AACT;AACA,SAAS,QAAQ,UAAU;AACzB,MAAI,OAAO,aAAa,UAAU;AAChC,UAAM,SAAS,aAAa,QAAQ;AACpC,QAAI,QAAQ;AACV,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,OAAO,CAAC,MAAM,QAAQ,aAAa;AACvC,MAAI,CAAC,aAAa;AAChB,aAAS,SAAS,GAAG;AACrB;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,OAAO,QAAQ;AAClC,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK,SAAS;AACZ,YAAM,SAAS,OAAO;AACtB,YAAM,QAAQ,OAAO;AACrB,YAAM,YAAY,MAAM,KAAK,GAAG;AAChC,YAAM,YAAY,IAAI,gBAAgB;AAAA,QACpC,OAAO;AAAA,MACT,CAAC;AACD,cAAQ,SAAS,WAAW,UAAU,SAAS;AAC/C;AAAA,IACF;AAAA,IACA,KAAK,UAAU;AACb,YAAM,MAAM,OAAO;AACnB,cAAQ,IAAI,MAAM,GAAG,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI;AACjD;AAAA,IACF;AAAA,IACA;AACE,eAAS,SAAS,GAAG;AACrB;AAAA,EACJ;AACA,MAAI,eAAe;AACnB,cAAY,OAAO,IAAI,EAAE,KAAK,CAAC,aAAa;AAC1C,UAAM,SAAS,SAAS;AACxB,QAAI,WAAW,KAAK;AAClB,iBAAW,MAAM;AACf,iBAAS,YAAY,MAAM,IAAI,UAAU,QAAQ,MAAM;AAAA,MACzD,CAAC;AACD;AAAA,IACF;AACA,mBAAe;AACf,WAAO,SAAS,KAAK;AAAA,EACvB,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,QAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,iBAAW,MAAM;AACf,YAAI,SAAS,KAAK;AAChB,mBAAS,SAAS,IAAI;AAAA,QACxB,OAAO;AACL,mBAAS,QAAQ,YAAY;AAAA,QAC/B;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,eAAW,MAAM;AACf,eAAS,WAAW,IAAI;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,EAAE,MAAM,MAAM;AACb,aAAS,QAAQ,YAAY;AAAA,EAC/B,CAAC;AACH;AACA,IAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,QAAM,SAAS;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,SAAS,CAAC;AAAA,EACZ;AACA,QAAMA,WAA0B,uBAAO,OAAO,IAAI;AAClD,QAAM,KAAK,CAAC,GAAG,MAAM;AACnB,QAAI,EAAE,aAAa,EAAE,UAAU;AAC7B,aAAO,EAAE,SAAS,cAAc,EAAE,QAAQ;AAAA,IAC5C;AACA,QAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,aAAO,EAAE,OAAO,cAAc,EAAE,MAAM;AAAA,IACxC;AACA,WAAO,EAAE,KAAK,cAAc,EAAE,IAAI;AAAA,EACpC,CAAC;AACD,MAAI,WAAW;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACA,QAAM,QAAQ,CAAC,SAAS;AACtB,QAAI,SAAS,SAAS,KAAK,QAAQ,SAAS,WAAW,KAAK,UAAU,SAAS,aAAa,KAAK,UAAU;AACzG;AAAA,IACF;AACA,eAAW;AACX,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,KAAK;AAClB,UAAM,kBAAkBA,SAAQ,QAAQ,MAAMA,SAAQ,QAAQ,IAAoB,uBAAO,OAAO,IAAI;AACpG,UAAM,eAAe,gBAAgB,MAAM,MAAM,gBAAgB,MAAM,IAAI,WAAW,UAAU,MAAM;AACtG,QAAI;AACJ,QAAI,QAAQ,aAAa,OAAO;AAC9B,aAAO,OAAO;AAAA,IAChB,WAAW,WAAW,MAAM,aAAa,QAAQ,IAAI,IAAI,GAAG;AAC1D,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,SAAK,KAAK,IAAI;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,UAAU,IAAI;AACpC,WAAS,QAAQ,CAACA,aAAY;AAC5B,UAAM,QAAQA,SAAQ;AACtB,QAAI,OAAO;AACT,MAAAA,SAAQ,kBAAkB,MAAM,OAAO,CAAC,QAAQ,IAAI,OAAO,EAAE;AAAA,IAC/D;AAAA,EACF,CAAC;AACH;AACA,SAAS,gBAAgBA,UAAS;AAChC,MAAI,CAACA,SAAQ,sBAAsB;AACjC,IAAAA,SAAQ,uBAAuB;AAC/B,eAAW,MAAM;AACf,MAAAA,SAAQ,uBAAuB;AAC/B,YAAM,QAAQA,SAAQ,kBAAkBA,SAAQ,gBAAgB,MAAM,CAAC,IAAI,CAAC;AAC5E,UAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,MACF;AACA,UAAI,aAAa;AACjB,YAAM,WAAWA,SAAQ;AACzB,YAAM,SAASA,SAAQ;AACvB,YAAM,QAAQ,CAAC,SAAS;AACtB,cAAM,QAAQ,KAAK;AACnB,cAAM,YAAY,MAAM,QAAQ;AAChC,cAAM,UAAU,MAAM,QAAQ,OAAO,CAAC,SAAS;AAC7C,cAAI,KAAK,WAAW,QAAQ;AAC1B,mBAAO;AAAA,UACT;AACA,gBAAM,OAAO,KAAK;AAClB,cAAIA,SAAQ,MAAM,IAAI,GAAG;AACvB,kBAAM,OAAO,KAAK;AAAA,cAChB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,WAAWA,SAAQ,QAAQ,IAAI,IAAI,GAAG;AACpC,kBAAM,QAAQ,KAAK;AAAA,cACjB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,yBAAa;AACb,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,CAAC;AACD,YAAI,MAAM,QAAQ,WAAW,WAAW;AACtC,cAAI,CAAC,YAAY;AACf,2BAAe,CAACA,QAAO,GAAG,KAAK,EAAE;AAAA,UACnC;AACA,eAAK;AAAA,YACH,MAAM,OAAO,MAAM,CAAC;AAAA,YACpB,MAAM,QAAQ,MAAM,CAAC;AAAA,YACrB,MAAM,QAAQ,MAAM,CAAC;AAAA,YACrB,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAI,YAAY;AAChB,SAAS,cAAc,UAAU,OAAO,gBAAgB;AACtD,QAAM,KAAK;AACX,QAAM,QAAQ,eAAe,KAAK,MAAM,gBAAgB,EAAE;AAC1D,MAAI,CAAC,MAAM,QAAQ,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,iBAAe,QAAQ,CAACA,aAAY;AAClC,KAACA,SAAQ,oBAAoBA,SAAQ,kBAAkB,CAAC,IAAI,KAAK,IAAI;AAAA,EACvE,CAAC;AACD,SAAO;AACT;AAEA,SAAS,YAAY,MAAM,WAAW,MAAMC,eAAc,OAAO;AAC/D,QAAM,SAAS,CAAC;AAChB,OAAK,QAAQ,CAAC,SAAS;AACrB,UAAM,OAAO,OAAO,SAAS,WAAW,aAAa,MAAM,UAAUA,YAAW,IAAI;AACpF,QAAI,MAAM;AACR,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,IAAI,gBAAgB;AAAA,EAClB,WAAW,CAAC;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,kBAAkB;AACpB;AAGA,SAAS,UAAU,QAAQ,SAAS,OAAO,MAAM;AAC/C,QAAM,iBAAiB,OAAO,UAAU;AACxC,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,cAAc,IAAI,OAAO;AACvF,MAAI;AACJ,MAAI,OAAO,QAAQ;AACjB,QAAI,OAAO,OAAO,UAAU,MAAM,CAAC;AACnC,gBAAY,CAAC;AACb,WAAO,KAAK,SAAS,GAAG;AACtB,YAAM,YAAY,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,MAAM;AACxD,gBAAU,KAAK,KAAK,SAAS,CAAC;AAC9B,aAAO,KAAK,MAAM,GAAG,SAAS,EAAE,OAAO,KAAK,MAAM,YAAY,CAAC,CAAC;AAAA,IAClE;AACA,gBAAY,UAAU,OAAO,IAAI;AAAA,EACnC,OAAO;AACL,gBAAY,OAAO,UAAU,MAAM,UAAU,EAAE,OAAO,OAAO,UAAU,MAAM,GAAG,UAAU,CAAC;AAAA,EAC7F;AACA,QAAM,YAAY,KAAK,IAAI;AAC3B,MAAI,SAAS;AACb,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,QAAQ,CAAC;AACb,MAAI,gBAAgB,CAAC;AACrB,MAAI,OAAO,SAAS,YAAY;AAC9B,kBAAc,KAAK,IAAI;AAAA,EACzB;AACA,WAAS,aAAa;AACpB,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,QAAQ;AACf,QAAI,WAAW,WAAW;AACxB,eAAS;AAAA,IACX;AACA,eAAW;AACX,UAAM,QAAQ,CAAC,SAAS;AACtB,UAAI,KAAK,WAAW,WAAW;AAC7B,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AACD,YAAQ,CAAC;AAAA,EACX;AACA,WAAS,UAAU,UAAU,WAAW;AACtC,QAAI,WAAW;AACb,sBAAgB,CAAC;AAAA,IACnB;AACA,QAAI,OAAO,aAAa,YAAY;AAClC,oBAAc,KAAK,QAAQ;AAAA,IAC7B;AAAA,EACF;AACA,WAAS,iBAAiB;AACxB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB,MAAM;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,WAAS,YAAY;AACnB,aAAS;AACT,kBAAc,QAAQ,CAAC,aAAa;AAClC,eAAS,QAAQ,SAAS;AAAA,IAC5B,CAAC;AAAA,EACH;AACA,WAAS,aAAa;AACpB,UAAM,QAAQ,CAAC,SAAS;AACtB,UAAI,KAAK,WAAW,WAAW;AAC7B,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AACD,YAAQ,CAAC;AAAA,EACX;AACA,WAAS,eAAe,MAAM,UAAU,MAAM;AAC5C,UAAM,UAAU,aAAa;AAC7B,YAAQ,MAAM,OAAO,CAAC,WAAW,WAAW,IAAI;AAChD,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH;AAAA,MACF,KAAK;AACH,YAAI,WAAW,CAAC,OAAO,kBAAkB;AACvC;AAAA,QACF;AACA;AAAA,MACF;AACE;AAAA,IACJ;AACA,QAAI,aAAa,SAAS;AACxB,kBAAY;AACZ,gBAAU;AACV;AAAA,IACF;AACA,QAAI,SAAS;AACX,kBAAY;AACZ,UAAI,CAAC,MAAM,QAAQ;AACjB,YAAI,CAAC,UAAU,QAAQ;AACrB,oBAAU;AAAA,QACZ,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MACF;AACA;AAAA,IACF;AACA,eAAW;AACX,eAAW;AACX,QAAI,CAAC,OAAO,QAAQ;AAClB,YAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,QAAQ;AACpD,UAAI,UAAU,MAAM,UAAU,OAAO,OAAO;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AACA,aAAS;AACT,kBAAc,QAAQ,CAAC,aAAa;AAClC,eAAS,IAAI;AAAA,IACf,CAAC;AAAA,EACH;AACA,WAAS,WAAW;AAClB,QAAI,WAAW,WAAW;AACxB;AAAA,IACF;AACA,eAAW;AACX,UAAM,WAAW,UAAU,MAAM;AACjC,QAAI,aAAa,QAAQ;AACvB,UAAI,MAAM,QAAQ;AAChB,gBAAQ,WAAW,MAAM;AACvB,qBAAW;AACX,cAAI,WAAW,WAAW;AACxB,uBAAW;AACX,sBAAU;AAAA,UACZ;AAAA,QACF,GAAG,OAAO,OAAO;AACjB;AAAA,MACF;AACA,gBAAU;AACV;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,QAAQ;AAAA,MACR;AAAA,MACA,UAAU,CAAC,SAAS,SAAS;AAC3B,uBAAe,MAAM,SAAS,IAAI;AAAA,MACpC;AAAA,IACF;AACA,UAAM,KAAK,IAAI;AACf;AACA,YAAQ,WAAW,UAAU,OAAO,MAAM;AAC1C,UAAM,UAAU,SAAS,KAAK,QAAQ;AAAA,EACxC;AACA,aAAW,QAAQ;AACnB,SAAO;AACT;AAGA,SAAS,eAAe,KAAK;AAC3B,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,MAAI,UAAU,CAAC;AACf,WAAS,UAAU;AACjB,cAAU,QAAQ,OAAO,CAAC,SAAS,KAAK,EAAE,WAAW,SAAS;AAAA,EAChE;AACA,WAAS,MAAM,SAAS,eAAe,cAAc;AACnD,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,MAAM,UAAU;AACf,gBAAQ;AACR,YAAI,cAAc;AAChB,uBAAa,MAAM,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,YAAQ,KAAK,MAAM;AACnB,WAAO;AAAA,EACT;AACA,WAAS,KAAK,UAAU;AACtB,WAAO,QAAQ,KAAK,CAAC,UAAU;AAC7B,aAAO,SAAS,KAAK;AAAA,IACvB,CAAC,KAAK;AAAA,EACR;AACA,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA,UAAU,CAAC,UAAU;AACnB,aAAO,QAAQ;AAAA,IACjB;AAAA,IACA,UAAU,MAAM,OAAO;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB;AAC3B;AACA,IAAM,kBAAkC,uBAAO,OAAO,IAAI;AAC1D,SAAS,mBAAmB,UAAU;AACpC,MAAI,CAAC,gBAAgB,QAAQ,GAAG;AAC9B,UAAM,SAAS,aAAa,QAAQ;AACpC,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,aAAa,eAAe,MAAM;AACxC,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AACA,oBAAgB,QAAQ,IAAI;AAAA,EAC9B;AACA,SAAO,gBAAgB,QAAQ;AACjC;AACA,SAAS,aAAa,QAAQ,OAAO,UAAU;AAC7C,MAAI;AACJ,MAAIC;AACJ,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,MAAM,aAAa,MAAM;AAC/B,QAAI,CAAC,KAAK;AACR,eAAS,QAAQ,GAAG;AACpB,aAAO;AAAA,IACT;AACA,IAAAA,QAAO,IAAI;AACX,UAAM,SAAS,mBAAmB,MAAM;AACxC,QAAI,QAAQ;AACV,mBAAa,OAAO;AAAA,IACtB;AAAA,EACF,OAAO;AACL,UAAM,SAAS,gBAAgB,MAAM;AACrC,QAAI,QAAQ;AACV,mBAAa,eAAe,MAAM;AAClC,YAAM,YAAY,OAAO,YAAY,OAAO,UAAU,CAAC,IAAI;AAC3D,YAAM,MAAM,aAAa,SAAS;AAClC,UAAI,KAAK;AACP,QAAAA,QAAO,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,cAAc,CAACA,OAAM;AACxB,aAAS,QAAQ,GAAG;AACpB,WAAO;AAAA,EACT;AACA,SAAO,WAAW,MAAM,OAAOA,OAAM,QAAQ,EAAE,EAAE;AACnD;AAEA,SAAS,gBAAgB;AACzB;AACA,SAAS,eAAeF,UAAS;AAC/B,MAAI,CAACA,SAAQ,iBAAiB;AAC5B,IAAAA,SAAQ,kBAAkB;AAC1B,eAAW,MAAM;AACf,MAAAA,SAAQ,kBAAkB;AAC1B,sBAAgBA,QAAO;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AACA,SAAS,qBAAqB,OAAO;AACnC,QAAM,QAAQ,CAAC;AACf,QAAM,UAAU,CAAC;AACjB,QAAM,QAAQ,CAAC,SAAS;AACtB,KAAC,KAAK,MAAM,aAAa,IAAI,QAAQ,SAAS,KAAK,IAAI;AAAA,EACzD,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,oBAAoBA,UAAS,OAAO,MAAM;AACjD,WAAS,eAAe;AACtB,UAAM,UAAUA,SAAQ;AACxB,UAAM,QAAQ,CAAC,SAAS;AACtB,UAAI,SAAS;AACX,gBAAQ,OAAO,IAAI;AAAA,MACrB;AACA,UAAI,CAACA,SAAQ,MAAM,IAAI,GAAG;AACxB,QAAAA,SAAQ,QAAQ,IAAI,IAAI;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,QAAI;AACF,YAAM,SAAS,WAAWA,UAAS,IAAI;AACvC,UAAI,CAAC,OAAO,QAAQ;AAClB,qBAAa;AACb;AAAA,MACF;AAAA,IACF,SAAS,KAAK;AACZ,cAAQ,MAAM,GAAG;AAAA,IACnB;AAAA,EACF;AACA,eAAa;AACb,iBAAeA,QAAO;AACxB;AACA,SAAS,2BAA2B,UAAU,UAAU;AACtD,MAAI,oBAAoB,SAAS;AAC/B,aAAS,KAAK,CAAC,SAAS;AACtB,eAAS,IAAI;AAAA,IACf,CAAC,EAAE,MAAM,MAAM;AACb,eAAS,IAAI;AAAA,IACf,CAAC;AAAA,EACH,OAAO;AACL,aAAS,QAAQ;AAAA,EACnB;AACF;AACA,SAAS,aAAaA,UAAS,OAAO;AACpC,MAAI,CAACA,SAAQ,aAAa;AACxB,IAAAA,SAAQ,cAAc;AAAA,EACxB,OAAO;AACL,IAAAA,SAAQ,cAAcA,SAAQ,YAAY,OAAO,KAAK,EAAE,KAAK;AAAA,EAC/D;AACA,MAAI,CAACA,SAAQ,gBAAgB;AAC3B,IAAAA,SAAQ,iBAAiB;AACzB,eAAW,MAAM;AACf,MAAAA,SAAQ,iBAAiB;AACzB,YAAM,EAAE,UAAU,OAAO,IAAIA;AAC7B,YAAM,SAASA,SAAQ;AACvB,aAAOA,SAAQ;AACf,UAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;AAC7B;AAAA,MACF;AACA,YAAM,mBAAmBA,SAAQ;AACjC,UAAIA,SAAQ,cAAc,OAAO,SAAS,KAAK,CAAC,mBAAmB;AACjE;AAAA,UACEA,SAAQ,UAAU,QAAQ,QAAQ,QAAQ;AAAA,UAC1C,CAAC,SAAS;AACR,gCAAoBA,UAAS,QAAQ,IAAI;AAAA,UAC3C;AAAA,QACF;AACA;AAAA,MACF;AACA,UAAI,kBAAkB;AACpB,eAAO,QAAQ,CAAC,SAAS;AACvB,gBAAM,WAAW,iBAAiB,MAAM,QAAQ,QAAQ;AACxD,qCAA2B,UAAU,CAAC,SAAS;AAC7C,kBAAM,UAAU,OAAO;AAAA,cACrB;AAAA,cACA,OAAO;AAAA,gBACL,CAAC,IAAI,GAAG;AAAA,cACV;AAAA,YACF,IAAI;AACJ,gCAAoBA,UAAS,CAAC,IAAI,GAAG,OAAO;AAAA,UAC9C,CAAC;AAAA,QACH,CAAC;AACD;AAAA,MACF;AACA,YAAM,EAAE,OAAO,QAAQ,IAAI,qBAAqB,MAAM;AACtD,UAAI,QAAQ,QAAQ;AAClB,4BAAoBA,UAAS,SAAS,IAAI;AAAA,MAC5C;AACA,UAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,MACF;AACA,YAAM,MAAM,OAAO,MAAM,aAAa,IAAI,aAAa,QAAQ,IAAI;AACnE,UAAI,CAAC,KAAK;AACR,4BAAoBA,UAAS,OAAO,IAAI;AACxC;AAAA,MACF;AACA,YAAM,SAAS,IAAI,QAAQ,UAAU,QAAQ,KAAK;AAClD,aAAO,QAAQ,CAAC,SAAS;AACvB,qBAAa,UAAU,MAAM,CAAC,SAAS;AACrC,8BAAoBA,UAAS,KAAK,OAAO,IAAI;AAAA,QAC/C,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAM,YAAY,CAAC,OAAO,aAAa;AACrC,QAAM,eAAe,YAAY,OAAO,MAAM,iBAAiB,CAAC;AAChE,QAAM,cAAc,UAAU,YAAY;AAC1C,MAAI,CAAC,YAAY,QAAQ,QAAQ;AAC/B,QAAI,eAAe;AACnB,QAAI,UAAU;AACZ,iBAAW,MAAM;AACf,YAAI,cAAc;AAChB;AAAA,YACE,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,MAAM;AACX,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,QAAM,UAAU,CAAC;AACjB,MAAI,cAAc;AAClB,cAAY,QAAQ,QAAQ,CAAC,SAAS;AACpC,UAAM,EAAE,UAAU,OAAO,IAAI;AAC7B,QAAI,WAAW,cAAc,aAAa,cAAc;AACtD;AAAA,IACF;AACA,mBAAe;AACf,iBAAa;AACb,YAAQ,KAAK,WAAW,UAAU,MAAM,CAAC;AACzC,UAAM,mBAAmB,SAAS,QAAQ,MAAM,SAAS,QAAQ,IAAoB,uBAAO,OAAO,IAAI;AACvG,QAAI,CAAC,iBAAiB,MAAM,GAAG;AAC7B,uBAAiB,MAAM,IAAI,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,cAAY,QAAQ,QAAQ,CAAC,SAAS;AACpC,UAAM,EAAE,UAAU,QAAQ,KAAK,IAAI;AACnC,UAAMA,WAAU,WAAW,UAAU,MAAM;AAC3C,UAAM,eAAeA,SAAQ,iBAAiBA,SAAQ,eAA+B,oBAAI,IAAI;AAC7F,QAAI,CAAC,aAAa,IAAI,IAAI,GAAG;AAC3B,mBAAa,IAAI,IAAI;AACrB,eAAS,QAAQ,EAAE,MAAM,EAAE,KAAK,IAAI;AAAA,IACtC;AAAA,EACF,CAAC;AACD,UAAQ,QAAQ,CAACA,aAAY;AAC3B,UAAM,OAAO,SAASA,SAAQ,QAAQ,EAAEA,SAAQ,MAAM;AACtD,QAAI,KAAK,QAAQ;AACf,mBAAaA,UAAS,IAAI;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,SAAO,WAAW,cAAc,UAAU,aAAa,OAAO,IAAI;AACpE;AACA,IAAM,WAAW,CAAC,SAAS;AACzB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,UAAU,OAAO,SAAS,WAAW,aAAa,MAAM,IAAI,IAAI;AACtE,QAAI,CAAC,SAAS;AACZ,aAAO,IAAI;AACX;AAAA,IACF;AACA,cAAU,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW;AACvC,UAAI,OAAO,UAAU,SAAS;AAC5B,cAAM,OAAO,YAAY,OAAO;AAChC,YAAI,MAAM;AACR,kBAAQ;AAAA,YACN,GAAG;AAAA,YACH,GAAG;AAAA,UACL,CAAC;AACD;AAAA,QACF;AAAA,MACF;AACA,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,qBAAqB,QAAQ,QAAQ,UAAU;AACtD,aAAW,YAAY,IAAI,MAAM,EAAE,YAAY;AACjD;AACA,SAAS,oBAAoB,QAAQ,QAAQ,UAAU;AACrD,aAAW,YAAY,IAAI,MAAM,EAAE,WAAW;AAChD;AAEA,SAAS,oBAAoB,UAAU,MAAM;AAC3C,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,EACL;AACA,aAAW,OAAO,MAAM;AACtB,UAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,YAAY,OAAO;AACzB,QAAI,OAAO,+BAA+B;AACxC,UAAI,UAAU,QAAQ,UAAU,cAAc,YAAY,cAAc,WAAW;AACjF,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF,WAAW,cAAc,OAAO,OAAO,GAAG,GAAG;AAC3C,aAAO,GAAG,IAAI,QAAQ,WAAW,QAAQ,IAAI;AAAA,IAC/C;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,YAAY;AAClB,SAAS,eAAe,QAAQ,MAAM;AACpC,OAAK,MAAM,SAAS,EAAE,QAAQ,CAAC,QAAQ;AACrC,UAAM,QAAQ,IAAI,KAAK;AACvB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,QAAQ;AACf;AAAA,MACF,KAAK;AACH,eAAO,QAAQ;AACf;AAAA,IACJ;AAAA,EACF,CAAC;AACH;AAEA,SAAS,iBAAiB,OAAO,eAAe,GAAG;AACjD,QAAM,QAAQ,MAAM,QAAQ,cAAc,EAAE;AAC5C,WAAS,QAAQ,QAAQ;AACvB,WAAO,SAAS,GAAG;AACjB,gBAAU;AAAA,IACZ;AACA,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,UAAU,IAAI;AAChB,UAAM,MAAM,SAAS,KAAK;AAC1B,WAAO,MAAM,GAAG,IAAI,IAAI,QAAQ,GAAG;AAAA,EACrC,WAAW,UAAU,OAAO;AAC1B,QAAI,QAAQ;AACZ,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,gBAAQ;AACR;AAAA,MACF,KAAK;AACH,gBAAQ;AAAA,IACZ;AACA,QAAI,OAAO;AACT,UAAI,MAAM,WAAW,MAAM,MAAM,GAAG,MAAM,SAAS,MAAM,MAAM,CAAC;AAChE,UAAI,MAAM,GAAG,GAAG;AACd,eAAO;AAAA,MACT;AACA,YAAM,MAAM;AACZ,aAAO,MAAM,MAAM,IAAI,QAAQ,GAAG,IAAI;AAAA,IACxC;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,WAAW,MAAM,YAAY;AACpC,MAAI,oBAAoB,KAAK,QAAQ,QAAQ,MAAM,KAAK,KAAK;AAC7D,aAAW,QAAQ,YAAY;AAC7B,yBAAqB,MAAM,OAAO,OAAO,WAAW,IAAI,IAAI;AAAA,EAC9D;AACA,SAAO,4CAA4C,oBAAoB,MAAM,OAAO;AACtF;AAEA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,QAAQ,GAAG;AACvI;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,wBAAwB,gBAAgB,GAAG;AACpD;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,UAAU,UAAU,GAAG,IAAI;AACpC;AAEA,IAAM,oCAAoC;AAAA,EACtC,GAAG;AAAA,EACH,QAAQ;AACZ;AAKA,IAAM,cAAc;AAAA,EAChB,SAAS;AAAA,EACT,eAAe;AAAA,EACf,eAAe;AAAA,EACf,QAAQ;AACZ;AAIA,IAAM,cAAc;AAAA,EAChB,SAAS;AACb;AACA,IAAM,gBAAgB;AAAA,EAClB,iBAAiB;AACrB;AACA,IAAM,eAAe;AAAA,EACjB,iBAAiB;AACrB;AAEA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACV;AACA,IAAM,eAAe;AAAA,EACjB,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,YAAY;AAChB;AACA,WAAW,UAAU,cAAc;AAC/B,QAAM,OAAO,aAAa,MAAM;AAChC,aAAW,QAAQ,YAAY;AAC3B,SAAK,SAAS,IAAI,IAAI,WAAW,IAAI;AAAA,EACzC;AACJ;AAKA,IAAM,uBAAuB,CAAC;AAC9B,CAAC,cAAc,UAAU,EAAE,QAAQ,CAAC,WAAW;AAC3C,QAAM,OAAO,OAAO,MAAM,GAAG,CAAC,IAAI;AAElC,uBAAqB,SAAS,OAAO,IAAI;AAEzC,uBAAqB,OAAO,MAAM,GAAG,CAAC,IAAI,OAAO,IAAI;AAErD,uBAAqB,SAAS,MAAM,IAAI;AAC5C,CAAC;AAID,SAAS,QAAQ,OAAO;AACpB,SAAO,SAAS,MAAM,MAAM,YAAY,IAAI,OAAO;AACvD;AAIA,IAAM,SAAS,CAEf,MAEA,UAAU;AAEN,QAAM,iBAAiB,oBAAoB,mCAAmC,KAAK;AACnF,QAAM,iBAAiB,EAAE,GAAG,YAAY;AAExC,QAAM,OAAO,MAAM,QAAQ;AAE3B,QAAM,QAAQ,CAAC;AACf,QAAM,aAAa,MAAM;AACzB,QAAM,cAAc,OAAO,eAAe,YAAY,EAAE,sBAAsB,SACxE,aACA,CAAC;AAEP,WAAS,OAAO,OAAO;AACnB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,QAAQ;AAClB;AAAA,IACJ;AACA,YAAQ,KAAK;AAAA,MAET,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,uBAAe,GAAG,IACd,UAAU,QAAQ,UAAU,UAAU,UAAU;AACpD;AAAA,MAEJ,KAAK;AACD,YAAI,OAAO,UAAU,UAAU;AAC3B,yBAAe,gBAAgB,KAAK;AAAA,QACxC;AACA;AAAA,MAEJ,KAAK;AACD,cAAM,QAAQ;AACd;AAAA,MAEJ,KAAK;AACD,YAAI,OAAO,UAAU,UAAU;AAC3B,yBAAe,GAAG,IAAI,iBAAiB,KAAK;AAAA,QAChD,WACS,OAAO,UAAU,UAAU;AAChC,yBAAe,GAAG,IAAI;AAAA,QAC1B;AACA;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AAED,YAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,iBAAO,eAAe,aAAa;AAAA,QACvC;AACA;AAAA,MACJ,SAAS;AACL,cAAM,QAAQ,qBAAqB,GAAG;AACtC,YAAI,OAAO;AAEP,cAAI,UAAU,QAAQ,UAAU,UAAU,UAAU,GAAG;AACnD,2BAAe,KAAK,IAAI;AAAA,UAC5B;AAAA,QACJ,WACS,kCAAkC,GAAG,MAAM,QAAQ;AAExD,yBAAe,GAAG,IAAI;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM,OAAO,UAAU,MAAM,cAAc;AAC3C,QAAM,gBAAgB,KAAK;AAE3B,MAAI,eAAe,QAAQ;AACvB,UAAM,gBAAgB;AAAA,EAC1B;AACA,MAAI,SAAS,OAAO;AAEhB,mBAAe,QAAQ;AAAA,MACnB,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AAEA,WAAO,OAAO,gBAAgB,aAAa;AAE3C,QAAI,eAAe;AACnB,QAAI,KAAK,MAAM;AACf,QAAI,OAAO,OAAO,UAAU;AAExB,WAAK,GAAG,QAAQ,MAAM,GAAG;AAAA,IAC7B;AAEA,mBAAe,WAAW,IAAI,WAAW,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO,iBAAiB,YAAY;AAExG,WAAO,EAAE,OAAO,cAAc;AAAA,EAClC;AAEA,QAAM,EAAE,MAAM,OAAO,OAAO,IAAI;AAChC,QAAM,UAAU,SAAS,WACpB,SAAS,OAAO,QAAQ,KAAK,QAAQ,cAAc,MAAM;AAE9D,QAAM,OAAO,WAAW,MAAM;AAAA,IAC1B,GAAG;AAAA,IACH,OAAO,QAAQ;AAAA,IACf,QAAQ,SAAS;AAAA,EACrB,CAAC;AAED,iBAAe,QAAQ;AAAA,IACnB,GAAG;AAAA,IACH,SAAS,SAAS,IAAI;AAAA,IACtB,SAAS,QAAQ,cAAc,KAAK;AAAA,IACpC,UAAU,QAAQ,cAAc,MAAM;AAAA,IACtC,GAAG;AAAA,IACH,GAAI,UAAU,gBAAgB;AAAA,IAC9B,GAAG;AAAA,EACP;AACA,SAAO,EAAE,QAAQ,cAAc;AACnC;AAOA,SAAS,YAAYA,UAAS;AAE9B;AAMA,SAAS,aAAaA,UAAS;AAE/B;AAKA,iBAAiB,IAAI;AAErB,aAAa,IAAI,cAAc;AAI/B,IAAI,OAAO,aAAa,eAAe,OAAO,WAAW,aAAa;AAClE,QAAM,UAAU;AAEhB,MAAI,QAAQ,mBAAmB,QAAQ;AACnC,UAAM,UAAU,QAAQ;AACxB,UAAM,MAAM;AACZ,QAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACjD,OAAC,mBAAmB,QAAQ,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS;AAC/D,YAAI;AACA;AAAA;AAAA,YAEA,OAAO,SAAS,YACZ,SAAS,QACT,gBAAgB;AAAA,YAEhB,OAAO,KAAK,UAAU,YACtB,OAAO,KAAK,WAAW;AAAA,YAEvB,CAAC,cAAc,IAAI;AAAA,YAAG;AACtB,oBAAQ,MAAM,GAAG;AAAA,UACrB;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,GAAG;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,MAAI,QAAQ,qBAAqB,QAAQ;AACrC,UAAM,YAAY,QAAQ;AAC1B,QAAI,OAAO,cAAc,YAAY,cAAc,MAAM;AACrD,eAAS,OAAO,WAAW;AACvB,cAAM,MAAM,sBAAsB,MAAM;AACxC,YAAI;AACA,gBAAM,QAAQ,UAAU,GAAG;AAC3B,cAAI,OAAO,UAAU,YACjB,CAAC,SACD,MAAM,cAAc,QAAQ;AAC5B;AAAA,UACJ;AACA,cAAI,CAAC,eAAe,KAAK,KAAK,GAAG;AAC7B,oBAAQ,MAAM,GAAG;AAAA,UACrB;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,GAAG;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAIA,IAAM,YAAY;AAAA,EACd,GAAG;AAAA,EACH,MAAM;AACV;AACA,IAAM,OAAO,gBAAgB;AAAA;AAAA,EAEzB,cAAc;AAAA;AAAA,EAEd,OAAO;AACH,WAAO;AAAA;AAAA,MAEH,OAAO;AAAA;AAAA,MAEP,cAAc;AAAA;AAAA,MAEd,aAAa;AAAA;AAAA,MAEb,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,UAAU;AAEN,SAAK,cAAc;AAAA,EACvB;AAAA,EACA,YAAY;AACR,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,SAAS;AAAA,IACL,eAAe;AACX,UAAI,KAAK,cAAc;AACnB,aAAK,aAAa,MAAM;AACxB,aAAK,eAAe;AAAA,MACxB;AAAA,IACJ;AAAA;AAAA,IAEA,QAAQ,MAAM,QAAQ,WAAW;AAE7B,UAAI,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,KAAK,SAAS,UAAU;AAE/B,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,eAAO;AAAA,UACH,MAAM;AAAA,QACV;AAAA,MACJ;AAEA,UAAI;AACJ,UAAI,OAAO,SAAS,aACf,WAAW,aAAa,MAAM,OAAO,IAAI,OAAO,MAAM;AACvD,aAAK,aAAa;AAClB,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,CAAC,MAAM;AAEP,YAAI,CAAC,KAAK,gBAAgB,KAAK,aAAa,SAAS,MAAM;AAEvD,eAAK,aAAa;AAClB,eAAK,QAAQ;AACb,cAAI,SAAS,MAAM;AAEf,iBAAK,eAAe;AAAA,cAChB,MAAM;AAAA,cACN,OAAO,UAAU,CAAC,QAAQ,GAAG,MAAM;AAC/B,qBAAK;AAAA,cACT,CAAC;AAAA,YACL;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,WAAK,aAAa;AAClB,UAAI,KAAK,UAAU,MAAM;AACrB,aAAK,QAAQ;AACb,YAAI,QAAQ;AACR,iBAAO,IAAI;AAAA,QACf;AAAA,MACJ;AAEA,UAAI,WAAW;AAEX,eAAO,OAAO,OAAO,CAAC,GAAG,IAAI;AAC7B,cAAM,aAAa,UAAU,KAAK,MAAM,SAAS,MAAM,SAAS,QAAQ,SAAS,QAAQ;AACzF,YAAI,OAAO,eAAe,UAAU;AAChC,eAAK,OAAO;AAAA,QAChB;AAAA,MACJ;AAEA,YAAM,UAAU,CAAC,SAAS;AAC1B,UAAI,SAAS,WAAW,IAAI;AACxB,gBAAQ,KAAK,cAAc,SAAS,MAAM;AAAA,MAC9C;AACA,UAAI,SAAS,aAAa,IAAI;AAC1B,gBAAQ,KAAK,cAAc,SAAS,QAAQ;AAAA,MAChD;AACA,aAAO,EAAE,MAAM,QAAQ;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAEL,SAAK;AACL,UAAM,QAAQ,KAAK;AAEnB,UAAM,OAAO,KAAK,eAAe,MAAM,MACjC,KAAK,QAAQ,MAAM,MAAM,MAAM,QAAQ,MAAM,SAAS,IACtD;AAEN,QAAI,CAAC,MAAM;AACP,aAAO,OAAO,WAAW,KAAK;AAAA,IAClC;AAEA,QAAI,WAAW;AACf,QAAI,KAAK,SAAS;AACd,iBAAW;AAAA,QACP,GAAG;AAAA,QACH,QAAQ,OAAO,MAAM,OAAO,MAAM,WAC5B,MAAM,OAAO,IAAI,MACjB,MAAM,KAAK,QAAQ,KAAK,GAAG;AAAA,MACrC;AAAA,IACJ;AAEA,WAAO,OAAO;AAAA,MACV,GAAG;AAAA,MACH,GAAG,KAAK;AAAA,IACZ,GAAG,QAAQ;AAAA,EACf;AACJ,CAAC;AAID,IAAM,OAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": ["storage", "simpleNames", "send"]}