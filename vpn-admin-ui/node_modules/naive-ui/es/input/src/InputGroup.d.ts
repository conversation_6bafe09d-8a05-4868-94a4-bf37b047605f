import type { ExtractPublicPropTypes } from '../../_utils';
export declare const inputGroupProps: {
    [key in any]: never;
};
export type InputGroupProps = ExtractPublicPropTypes<typeof inputGroupProps>;
declare const _default: import("vue").DefineComponent<{}, {
    mergedClsPrefix: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
