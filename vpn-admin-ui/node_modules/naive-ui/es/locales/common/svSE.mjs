const svSE = {
  name: 'sv-<PERSON>',
  global: {
    undo: '<PERSON><PERSON><PERSON>',
    redo: '<PERSON><PERSON><PERSON> o<PERSON>',
    confirm: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    clear: '<PERSON><PERSON>'
  },
  Popconfirm: {
    positiveText: '<PERSON><PERSON><PERSON><PERSON>ft<PERSON>',
    negativeText: 'Av<PERSON><PERSON><PERSON>'
  },
  Cascader: {
    placeholder: 'Vänligen välj',
    loading: 'Laddar',
    loadingRequiredMessage: label => `Vänligen ladda alla underpunkter till ${label} innan du väljer punkten.`
  },
  Time: {
    dateFormat: 'yyyy-MM-dd',
    dateTimeFormat: 'yyyy-MM-dd HH:mm:ss'
  },
  DatePicker: {
    yearFormat: 'yyyy',
    monthFormat: 'MMM',
    dayFormat: 'eeeeee',
    yearTypeFormat: 'yyyy',
    monthTypeFormat: 'yyyy-MM',
    dateFormat: 'yyyy-MM-dd',
    dateTimeFormat: 'yyyy-MM-dd HH:mm:ss',
    quarterFormat: 'yyyy-qqq',
    weekFormat: 'YYYY-w',
    clear: 'Ren<PERSON>',
    now: 'Nu',
    confirm: 'Bekräfta',
    selectTime: 'Välj tid',
    selectDate: 'Välj datum',
    datePlaceholder: 'Välj datum',
    datetimePlaceholder: 'Välj datum och tid',
    monthPlaceholder: 'Välj månad',
    yearPlaceholder: 'Välj år',
    quarterPlaceholder: 'Välj kvartal',
    weekPlaceholder: 'Select Week',
    startDatePlaceholder: 'Startdatum',
    endDatePlaceholder: 'Slutdatum',
    startDatetimePlaceholder: 'Startdatum och -tid',
    endDatetimePlaceholder: 'Slutdatum och -tid',
    startMonthPlaceholder: 'Startmånad',
    endMonthPlaceholder: 'Slutmånad',
    monthBeforeYear: true,
    firstDayOfWeek: 0,
    today: 'I dag'
  },
  DataTable: {
    checkTableAll: 'Välj allt',
    uncheckTableAll: 'Välj inget',
    confirm: 'Bekräfta',
    clear: 'Rensa'
  },
  LegacyTransfer: {
    sourceTitle: 'Källa',
    targetTitle: 'Mål'
  },
  Transfer: {
    selectAll: 'Välj allt',
    unselectAll: 'Välj inget',
    clearAll: 'Rensa',
    total: num => `Totalt ${num} rader`,
    selected: num => `${num} rader valda`
  },
  Empty: {
    description: 'Ingen data'
  },
  Select: {
    placeholder: 'Vänligen välj'
  },
  TimePicker: {
    placeholder: 'Välj tid',
    positiveText: 'OK',
    negativeText: 'Avbryt',
    now: 'Nu',
    clear: 'Rensa'
  },
  Pagination: {
    goto: 'Gå till',
    selectionSuffix: 'sida'
  },
  DynamicTags: {
    add: 'Lägg till'
  },
  Log: {
    loading: 'Laddar'
  },
  Input: {
    placeholder: 'Vänligen fyll i'
  },
  InputNumber: {
    placeholder: 'Vänligen fyll i'
  },
  DynamicInput: {
    create: 'Skapa'
  },
  ThemeEditor: {
    title: 'Temaverktyg',
    clearAllVars: 'Nollställ alla variabler',
    clearSearch: 'Rensa sökning',
    filterCompName: 'Filtrera efter komponentnamn',
    filterVarName: 'Filtrera efter variabelnamn',
    import: 'Importera',
    export: 'Exportera',
    restore: 'Nollställ till ursprungsval'
  },
  Image: {
    tipPrevious: 'Förgående bild (←)',
    tipNext: 'Nästa bild (→)',
    tipCounterclockwise: 'Moturs',
    tipClockwise: 'Medurs',
    tipZoomOut: 'Zooma in',
    tipZoomIn: 'Zooma ut',
    tipDownload: 'Ladda ned',
    tipClose: 'Stäng (Esc)',
    tipOriginalSize: 'Zooma till ursprunglig storlek'
  }
};
export default svSE;