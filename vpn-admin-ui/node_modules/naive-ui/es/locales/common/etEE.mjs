const etEE = {
  name: 'et-E<PERSON>',
  global: {
    undo: '<PERSON><PERSON><PERSON> tagasi',
    redo: '<PERSON><PERSON><PERSON> uuesti',
    confirm: '<PERSON><PERSON><PERSON>',
    clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
  },
  Popconfirm: {
    positiveText: '<PERSON>nn<PERSON>',
    negativeText: '<PERSON><PERSON><PERSON>'
  },
  Cascader: {
    placeholder: 'Vali',
    loading: '<PERSON><PERSON>',
    loadingRequiredMessage: label => `Enne kontrollimist pead elemendi ${label} kõik alamad laadima.`
  },
  Time: {
    dateFormat: 'dd.MM.yyyy',
    dateTimeFormat: 'dd.MM.yyyy HH:mm:ss'
  },
  DatePicker: {
    yearFormat: 'yyyy',
    monthFormat: 'MMM',
    dayFormat: 'eeeeee',
    yearTypeFormat: 'yyyy',
    monthTypeFormat: 'MM-yyyy',
    dateFormat: 'dd.MM.yyyy',
    dateTimeFormat: 'dd.MM.yyyy HH:mm:ss',
    quarterFormat: 'yyyy-qqq',
    weekFormat: 'YYYY-w',
    clear: 'Tühjenda',
    now: 'Nüüd',
    confirm: 'Kinnita',
    selectTime: 'Vali aeg',
    selectDate: 'Vali kuupäev',
    datePlaceholder: 'Vali kuupäev',
    datetimePlaceholder: 'Vali aeg ja kuupäev',
    monthPlaceholder: 'Vali kuu',
    yearPlaceholder: 'Vali aasta',
    quarterPlaceholder: 'Vali kvartal',
    weekPlaceholder: 'Vali nädal',
    startDatePlaceholder: 'Alguskpv',
    endDatePlaceholder: 'Lõppkpv',
    startDatetimePlaceholder: 'Alguskpv ja -aeg',
    endDatetimePlaceholder: 'Lõppkpv ja -aeg',
    startMonthPlaceholder: 'Alguskuu',
    endMonthPlaceholder: 'Lõppkuu',
    monthBeforeYear: true,
    firstDayOfWeek: 0,
    today: 'Täna'
  },
  DataTable: {
    checkTableAll: 'Vali tabelis kõik',
    uncheckTableAll: 'Tühista tabeli valik',
    confirm: 'Kinnita',
    clear: 'Tühjenda'
  },
  LegacyTransfer: {
    sourceTitle: 'Kust',
    targetTitle: 'Kuhu'
  },
  Transfer: {
    selectAll: 'Vali kõik',
    unselectAll: 'Tühista valik',
    clearAll: 'Tühjenda',
    total: num => `Kokku ${num} rida`,
    selected: num => `${num} rida valitud`
  },
  Empty: {
    description: 'Andmeid pole'
  },
  Select: {
    placeholder: 'Vali'
  },
  TimePicker: {
    placeholder: 'Vali aeg',
    positiveText: 'OK',
    negativeText: 'Katkesta',
    now: 'Nüüd',
    clear: 'Tühjenda'
  },
  Pagination: {
    goto: 'Mine',
    selectionSuffix: 'lk'
  },
  DynamicTags: {
    add: 'Lisa'
  },
  Log: {
    loading: 'Laeb'
  },
  Input: {
    placeholder: 'Sisesta'
  },
  InputNumber: {
    placeholder: 'Sisesta'
  },
  DynamicInput: {
    create: 'Loo'
  },
  ThemeEditor: {
    title: 'Teemaredaktor',
    clearAllVars: 'Tühjenda kõik muutujad',
    clearSearch: 'Tühjenda otsing',
    filterCompName: 'Filter komponendi nimega',
    filterVarName: 'Filter muutuja nimega',
    import: 'Import',
    export: 'Eksport',
    restore: 'Taasta originaal'
  },
  Image: {
    tipPrevious: 'Eelmine pilt (←)',
    tipNext: 'Järgmine pilt (→)',
    tipCounterclockwise: 'Vastupäeva',
    tipClockwise: 'Päripäeva',
    tipZoomOut: 'Suumi välja',
    tipZoomIn: 'Suumi sisse',
    tipDownload: 'Lae alla',
    tipClose: 'Sulge (Esc)',
    tipOriginalSize: 'Algsuurus'
  }
};
export default etEE;