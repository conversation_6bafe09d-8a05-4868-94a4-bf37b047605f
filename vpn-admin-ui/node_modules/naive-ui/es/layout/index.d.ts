export type { LayoutInst as LayoutContentInst, LayoutInst, LayoutSiderInst } from './src/interface';
export { layoutProps as layoutContentProps, layoutProps, default as NLayout } from './src/Layout';
export type { LayoutProps as LayoutContentProps, LayoutProps } from './src/Layout';
export { default as NLayoutContent } from './src/LayoutContent';
export { layoutFooterProps, default as NLayoutFooter } from './src/LayoutFooter';
export type { LayoutFooterProps } from './src/LayoutFooter';
export { headerProps as layoutHeaderProps, default as NLayoutHeader } from './src/LayoutHeader';
export type { LayoutHeaderProps } from './src/LayoutHeader';
export { layoutSiderProps, default as NLayoutSider } from './src/LayoutSider';
export type { LayoutSiderProps } from './src/LayoutSider';
