import type { ExtractPublicPropTypes } from '../../_utils';
import { type PropType, type SlotsType, type VNode } from 'vue';
export declare const resultProps: {
    size: {
        type: PropType<"small" | "medium" | "large" | "huge">;
        default: string;
    };
    status: {
        type: PropType<"info" | "success" | "warning" | "error" | "404" | "403" | "500" | "418">;
        default: string;
    };
    title: StringConstructor;
    description: StringConstructor;
    theme: PropType<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>>;
};
export type ResultProps = ExtractPublicPropTypes<typeof resultProps>;
export interface ResultSlots {
    default?: () => VNode[];
    footer?: () => VNode[];
    icon?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    size: {
        type: PropType<"small" | "medium" | "large" | "huge">;
        default: string;
    };
    status: {
        type: PropType<"info" | "success" | "warning" | "error" | "404" | "403" | "500" | "418">;
        default: string;
    };
    title: StringConstructor;
    description: StringConstructor;
    theme: PropType<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>>;
}>, {
    mergedClsPrefix: import("vue").Ref<string, string>;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-font-size': string;
        '--n-icon-size': string;
        '--n-line-height': string;
        '--n-text-color': string;
        '--n-title-font-size': string;
        '--n-title-font-weight': string;
        '--n-title-text-color': string;
        '--n-icon-color': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    size: {
        type: PropType<"small" | "medium" | "large" | "huge">;
        default: string;
    };
    status: {
        type: PropType<"info" | "success" | "warning" | "error" | "404" | "403" | "500" | "418">;
        default: string;
    };
    title: StringConstructor;
    description: StringConstructor;
    theme: PropType<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Result", {
        lineHeight: string;
        titleFontWeight: string;
        titleTextColor: string;
        textColor: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorInfo: string;
        iconColorWarning: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
        iconColor418: undefined;
        iconColor404: undefined;
        iconColor403: undefined;
        iconColor500: undefined;
    }, any>>>;
}>> & Readonly<{}>, {
    size: "small" | "medium" | "large" | "huge";
    status: "error" | "500" | "info" | "success" | "warning" | "404" | "403" | "418";
}, SlotsType<ResultSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
