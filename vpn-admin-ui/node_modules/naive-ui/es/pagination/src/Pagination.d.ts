import type { ExtractPublicPropTypes, MaybeArray } from '../../_utils';
import type { Size as InputSize } from '../../input/src/interface';
import type { SelectProps } from '../../select';
import type { Size as SelectSize } from '../../select/src/interface';
import type { PaginationInfo, PaginationLabelInfo, PaginationRenderLabel, PaginationSizeOption, RenderGoto, RenderNext, RenderPrefix, RenderPrev, RenderSuffix, Size } from './interface';
import type { PageItem } from './utils';
import { type PropType, type SlotsType, type VNode } from 'vue';
export declare const paginationProps: {
    readonly simple: BooleanConstructor;
    readonly page: NumberConstructor;
    readonly defaultPage: {
        readonly type: NumberConstructor;
        readonly default: 1;
    };
    readonly itemCount: NumberConstructor;
    readonly pageCount: NumberConstructor;
    readonly defaultPageCount: {
        readonly type: NumberConstructor;
        readonly default: 1;
    };
    readonly showSizePicker: BooleanConstructor;
    readonly pageSize: NumberConstructor;
    readonly defaultPageSize: NumberConstructor;
    readonly pageSizes: {
        readonly type: PropType<Array<number | PaginationSizeOption>>;
        readonly default: () => number[];
    };
    readonly showQuickJumper: BooleanConstructor;
    readonly size: {
        readonly type: PropType<Size>;
        readonly default: "medium";
    };
    readonly disabled: BooleanConstructor;
    readonly pageSlot: {
        readonly type: NumberConstructor;
        readonly default: 9;
    };
    readonly selectProps: PropType<SelectProps>;
    readonly prev: PropType<RenderPrev>;
    readonly next: PropType<RenderNext>;
    readonly goto: PropType<RenderGoto>;
    readonly prefix: PropType<RenderPrefix>;
    readonly suffix: PropType<RenderSuffix>;
    readonly label: PropType<PaginationRenderLabel>;
    readonly displayOrder: {
        readonly type: PropType<Array<"pages" | "size-picker" | "quick-jumper">>;
        readonly default: readonly ["pages", "size-picker", "quick-jumper"];
    };
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly showQuickJumpDropdown: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly 'onUpdate:page': PropType<MaybeArray<(page: number) => void>>;
    readonly onUpdatePage: PropType<MaybeArray<(page: number) => void>>;
    readonly 'onUpdate:pageSize': PropType<MaybeArray<(pageSize: number) => void>>;
    readonly onUpdatePageSize: PropType<MaybeArray<(pageSize: number) => void>>;
    /** @deprecated */
    readonly onPageSizeChange: PropType<MaybeArray<(pageSize: number) => void>>;
    /** @deprecated */
    readonly onChange: PropType<MaybeArray<(page: number) => void>>;
    readonly theme: PropType<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>>;
};
export type PaginationProps = ExtractPublicPropTypes<typeof paginationProps>;
export interface PaginationSlots {
    default?: () => VNode[];
    goto?: () => VNode[];
    label?: (props: PaginationLabelInfo) => VNode[];
    next?: (props: PaginationInfo) => VNode;
    prev?: (props: PaginationInfo) => VNode;
    prefix?: (props: PaginationInfo) => VNode;
    suffix?: (props: PaginationInfo) => VNode;
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly simple: BooleanConstructor;
    readonly page: NumberConstructor;
    readonly defaultPage: {
        readonly type: NumberConstructor;
        readonly default: 1;
    };
    readonly itemCount: NumberConstructor;
    readonly pageCount: NumberConstructor;
    readonly defaultPageCount: {
        readonly type: NumberConstructor;
        readonly default: 1;
    };
    readonly showSizePicker: BooleanConstructor;
    readonly pageSize: NumberConstructor;
    readonly defaultPageSize: NumberConstructor;
    readonly pageSizes: {
        readonly type: PropType<Array<number | PaginationSizeOption>>;
        readonly default: () => number[];
    };
    readonly showQuickJumper: BooleanConstructor;
    readonly size: {
        readonly type: PropType<Size>;
        readonly default: "medium";
    };
    readonly disabled: BooleanConstructor;
    readonly pageSlot: {
        readonly type: NumberConstructor;
        readonly default: 9;
    };
    readonly selectProps: PropType<SelectProps>;
    readonly prev: PropType<RenderPrev>;
    readonly next: PropType<RenderNext>;
    readonly goto: PropType<RenderGoto>;
    readonly prefix: PropType<RenderPrefix>;
    readonly suffix: PropType<RenderSuffix>;
    readonly label: PropType<PaginationRenderLabel>;
    readonly displayOrder: {
        readonly type: PropType<Array<"pages" | "size-picker" | "quick-jumper">>;
        readonly default: readonly ["pages", "size-picker", "quick-jumper"];
    };
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly showQuickJumpDropdown: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly 'onUpdate:page': PropType<MaybeArray<(page: number) => void>>;
    readonly onUpdatePage: PropType<MaybeArray<(page: number) => void>>;
    readonly 'onUpdate:pageSize': PropType<MaybeArray<(pageSize: number) => void>>;
    readonly onUpdatePageSize: PropType<MaybeArray<(pageSize: number) => void>>;
    /** @deprecated */
    readonly onPageSizeChange: PropType<MaybeArray<(pageSize: number) => void>>;
    /** @deprecated */
    readonly onChange: PropType<MaybeArray<(page: number) => void>>;
    readonly theme: PropType<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>>;
}>, {
    rtlEnabled: import("vue").Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    mergedClsPrefix: import("vue").Ref<string, string>;
    locale: import("vue").Ref<{
        goto: string;
        selectionSuffix: string;
    }, {
        goto: string;
        selectionSuffix: string;
    }>;
    selfRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    mergedPage: import("vue").ComputedRef<number>;
    pageItems: import("vue").ComputedRef<PageItem[]>;
    mergedItemCount: import("vue").ComputedRef<number>;
    jumperValue: import("vue").Ref<string, string>;
    pageSizeOptions: import("vue").ComputedRef<PaginationSizeOption[]>;
    mergedPageSize: import("vue").ComputedRef<number>;
    inputSize: import("vue").ComputedRef<InputSize>;
    selectSize: import("vue").ComputedRef<SelectSize>;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            buttonColor: string;
            buttonColorHover: string;
            buttonColorPressed: string;
            buttonBorder: string;
            buttonBorderHover: string;
            buttonBorderPressed: string;
            buttonIconColor: string;
            buttonIconColorHover: string;
            buttonIconColorPressed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorPressed: string;
            itemTextColorActive: string;
            itemTextColorDisabled: string;
            itemColor: string;
            itemColorHover: string;
            itemColorPressed: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorDisabled: string;
            itemBorder: string;
            itemBorderHover: string;
            itemBorderPressed: string;
            itemBorderActive: string;
            itemBorderDisabled: string;
            itemBorderRadius: string;
            itemSizeSmall: string;
            itemSizeMedium: string;
            itemSizeLarge: string;
            itemFontSizeSmall: string;
            itemFontSizeMedium: string;
            itemFontSizeLarge: string;
            jumperFontSizeSmall: string;
            jumperFontSizeMedium: string;
            jumperFontSizeLarge: string;
            jumperTextColor: string;
            jumperTextColorDisabled: string;
            itemPaddingSmall: string;
            itemMarginSmall: string;
            itemMarginSmallRtl: string;
            itemPaddingMedium: string;
            itemMarginMedium: string;
            itemMarginMediumRtl: string;
            itemPaddingLarge: string;
            itemMarginLarge: string;
            itemMarginLargeRtl: string;
            buttonIconSizeSmall: string;
            buttonIconSizeMedium: string;
            buttonIconSizeLarge: string;
            inputWidthSmall: string;
            selectWidthSmall: string;
            inputMarginSmall: string;
            inputMarginSmallRtl: string;
            selectMarginSmall: string;
            prefixMarginSmall: string;
            suffixMarginSmall: string;
            inputWidthMedium: string;
            selectWidthMedium: string;
            inputMarginMedium: string;
            inputMarginMediumRtl: string;
            selectMarginMedium: string;
            prefixMarginMedium: string;
            suffixMarginMedium: string;
            inputWidthLarge: string;
            selectWidthLarge: string;
            inputMarginLarge: string;
            inputMarginLargeRtl: string;
            selectMarginLarge: string;
            prefixMarginLarge: string;
            suffixMarginLarge: string;
        };
        peers: {
            Select: import("../../_mixins").Theme<"Select", {
                menuBoxShadow: string;
            }, {
                InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    borderRadius: string;
                    fontWeight: string;
                    textColor: string;
                    textColorDisabled: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorActive: string;
                    border: string;
                    borderHover: string;
                    borderActive: string;
                    borderFocus: string;
                    boxShadowHover: string;
                    boxShadowActive: string;
                    boxShadowFocus: string;
                    caretColor: string;
                    arrowColor: string;
                    arrowColorDisabled: string;
                    loadingColor: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    borderActiveWarning: string;
                    borderFocusWarning: string;
                    boxShadowHoverWarning: string;
                    boxShadowActiveWarning: string;
                    boxShadowFocusWarning: string;
                    colorActiveWarning: string;
                    caretColorWarning: string;
                    borderError: string;
                    borderHoverError: string;
                    borderActiveError: string;
                    borderFocusError: string;
                    boxShadowHoverError: string;
                    boxShadowActiveError: string;
                    boxShadowFocusError: string;
                    colorActiveError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    paddingSingle: string;
                    paddingMultiple: string;
                    clearSize: string;
                    arrowSize: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
            Popselect: import("../../_mixins").Theme<"Popselect", {
                menuBoxShadow: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
        };
        peerOverrides: {
            Select?: {
                peers?: {
                    InternalSelection?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"InternalSelection", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        heightTiny: string;
                        heightSmall: string;
                        heightMedium: string;
                        heightLarge: string;
                        borderRadius: string;
                        fontWeight: string;
                        textColor: string;
                        textColorDisabled: string;
                        placeholderColor: string;
                        placeholderColorDisabled: string;
                        color: string;
                        colorDisabled: string;
                        colorActive: string;
                        border: string;
                        borderHover: string;
                        borderActive: string;
                        borderFocus: string;
                        boxShadowHover: string;
                        boxShadowActive: string;
                        boxShadowFocus: string;
                        caretColor: string;
                        arrowColor: string;
                        arrowColorDisabled: string;
                        loadingColor: string;
                        borderWarning: string;
                        borderHoverWarning: string;
                        borderActiveWarning: string;
                        borderFocusWarning: string;
                        boxShadowHoverWarning: string;
                        boxShadowActiveWarning: string;
                        boxShadowFocusWarning: string;
                        colorActiveWarning: string;
                        caretColorWarning: string;
                        borderError: string;
                        borderHoverError: string;
                        borderActiveError: string;
                        borderFocusError: string;
                        boxShadowHoverError: string;
                        boxShadowActiveError: string;
                        boxShadowFocusError: string;
                        colorActiveError: string;
                        caretColorError: string;
                        clearColor: string;
                        clearColorHover: string;
                        clearColorPressed: string;
                        paddingSingle: string;
                        paddingMultiple: string;
                        clearSize: string;
                        arrowSize: string;
                    }, {
                        Popover: import("../../_mixins").Theme<"Popover", {
                            fontSize: string;
                            borderRadius: string;
                            color: string;
                            dividerColor: string;
                            textColor: string;
                            boxShadow: string;
                            space: string;
                            spaceArrow: string;
                            arrowOffset: string;
                            arrowOffsetVertical: string;
                            arrowHeight: string;
                            padding: string;
                        }, any>;
                    }>> | undefined;
                    InternalSelectMenu?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"InternalSelectMenu", {
                        optionFontSizeTiny: string;
                        optionFontSizeSmall: string;
                        optionFontSizeMedium: string;
                        optionFontSizeLarge: string;
                        optionFontSizeHuge: string;
                        optionHeightTiny: string;
                        optionHeightSmall: string;
                        optionHeightMedium: string;
                        optionHeightLarge: string;
                        optionHeightHuge: string;
                        borderRadius: string;
                        color: string;
                        groupHeaderTextColor: string;
                        actionDividerColor: string;
                        optionTextColor: string;
                        optionTextColorPressed: string;
                        optionTextColorDisabled: string;
                        optionTextColorActive: string;
                        optionOpacityDisabled: string;
                        optionCheckColor: string;
                        optionColorPending: string;
                        optionColorActive: string;
                        optionColorActivePending: string;
                        actionTextColor: string;
                        loadingColor: string;
                        height: string;
                        paddingTiny: string;
                        paddingSmall: string;
                        paddingMedium: string;
                        paddingLarge: string;
                        paddingHuge: string;
                        optionPaddingTiny: string;
                        optionPaddingSmall: string;
                        optionPaddingMedium: string;
                        optionPaddingLarge: string;
                        optionPaddingHuge: string;
                        loadingSize: string;
                    }, {
                        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                            height: string;
                            width: string;
                            borderRadius: string;
                            color: string;
                            colorHover: string;
                            railInsetHorizontalBottom: string;
                            railInsetHorizontalTop: string;
                            railInsetVerticalRight: string;
                            railInsetVerticalLeft: string;
                            railColor: string;
                        }, any>;
                        Empty: import("../../_mixins").Theme<"Empty", {
                            fontSizeTiny: string;
                            fontSizeSmall: string;
                            fontSizeMedium: string;
                            fontSizeLarge: string;
                            fontSizeHuge: string;
                            textColor: string;
                            iconColor: string;
                            extraTextColor: string;
                            iconSizeTiny: string;
                            iconSizeSmall: string;
                            iconSizeMedium: string;
                            iconSizeLarge: string;
                            iconSizeHuge: string;
                        }, any>;
                    }>> | undefined;
                } | undefined;
            } | undefined;
            Input?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Popselect?: {
                peers?: {
                    Popover?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>> | undefined;
                    InternalSelectMenu?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"InternalSelectMenu", {
                        optionFontSizeTiny: string;
                        optionFontSizeSmall: string;
                        optionFontSizeMedium: string;
                        optionFontSizeLarge: string;
                        optionFontSizeHuge: string;
                        optionHeightTiny: string;
                        optionHeightSmall: string;
                        optionHeightMedium: string;
                        optionHeightLarge: string;
                        optionHeightHuge: string;
                        borderRadius: string;
                        color: string;
                        groupHeaderTextColor: string;
                        actionDividerColor: string;
                        optionTextColor: string;
                        optionTextColorPressed: string;
                        optionTextColorDisabled: string;
                        optionTextColorActive: string;
                        optionOpacityDisabled: string;
                        optionCheckColor: string;
                        optionColorPending: string;
                        optionColorActive: string;
                        optionColorActivePending: string;
                        actionTextColor: string;
                        loadingColor: string;
                        height: string;
                        paddingTiny: string;
                        paddingSmall: string;
                        paddingMedium: string;
                        paddingLarge: string;
                        paddingHuge: string;
                        optionPaddingTiny: string;
                        optionPaddingSmall: string;
                        optionPaddingMedium: string;
                        optionPaddingLarge: string;
                        optionPaddingHuge: string;
                        loadingSize: string;
                    }, {
                        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                            height: string;
                            width: string;
                            borderRadius: string;
                            color: string;
                            colorHover: string;
                            railInsetHorizontalBottom: string;
                            railInsetHorizontalTop: string;
                            railInsetVerticalRight: string;
                            railInsetVerticalLeft: string;
                            railColor: string;
                        }, any>;
                        Empty: import("../../_mixins").Theme<"Empty", {
                            fontSizeTiny: string;
                            fontSizeSmall: string;
                            fontSizeMedium: string;
                            fontSizeLarge: string;
                            fontSizeHuge: string;
                            textColor: string;
                            iconColor: string;
                            extraTextColor: string;
                            iconSizeTiny: string;
                            iconSizeSmall: string;
                            iconSizeMedium: string;
                            iconSizeLarge: string;
                            iconSizeHuge: string;
                        }, any>;
                    }>> | undefined;
                } | undefined;
            } | undefined;
        };
    }>;
    mergedPageCount: import("vue").ComputedRef<number>;
    startIndex: import("vue").ComputedRef<number>;
    endIndex: import("vue").ComputedRef<number>;
    showFastForwardMenu: import("vue").Ref<boolean, boolean>;
    showFastBackwardMenu: import("vue").Ref<boolean, boolean>;
    fastForwardActive: import("vue").Ref<boolean, boolean>;
    fastBackwardActive: import("vue").Ref<boolean, boolean>;
    handleMenuSelect: (value: number) => void;
    handleFastForwardMouseenter: () => void;
    handleFastForwardMouseleave: () => void;
    handleFastBackwardMouseenter: () => void;
    handleFastBackwardMouseleave: () => void;
    handleJumperInput: (value: string) => void;
    handleBackwardClick: () => void;
    handleForwardClick: () => void;
    handlePageItemClick: (pageItem: PageItem) => void;
    handleSizePickerChange: (value: number) => void;
    handleQuickJumperChange: () => void;
    cssVars: import("vue").ComputedRef<{
        '--n-prefix-margin': string;
        '--n-suffix-margin': string;
        '--n-item-font-size': string;
        '--n-select-width': string;
        '--n-select-margin': string;
        '--n-input-width': string;
        '--n-input-margin': string;
        '--n-input-margin-rtl': string;
        '--n-item-size': string;
        '--n-item-text-color': string;
        '--n-item-text-color-disabled': string;
        '--n-item-text-color-hover': string;
        '--n-item-text-color-active': string;
        '--n-item-text-color-pressed': string;
        '--n-item-color': string;
        '--n-item-color-hover': string;
        '--n-item-color-disabled': string;
        '--n-item-color-active': string;
        '--n-item-color-active-hover': string;
        '--n-item-color-pressed': string;
        '--n-item-border': string;
        '--n-item-border-hover': string;
        '--n-item-border-disabled': string;
        '--n-item-border-active': string;
        '--n-item-border-pressed': string;
        '--n-item-padding': string;
        '--n-item-border-radius': string;
        '--n-bezier': string;
        '--n-jumper-font-size': string;
        '--n-jumper-text-color': string;
        '--n-jumper-text-color-disabled': string;
        '--n-item-margin': string;
        '--n-item-margin-rtl': string;
        '--n-button-icon-size': string;
        '--n-button-icon-color': string;
        '--n-button-icon-color-hover': string;
        '--n-button-icon-color-pressed': string;
        '--n-button-color-hover': string;
        '--n-button-color': string;
        '--n-button-color-pressed': string;
        '--n-button-border': string;
        '--n-button-border-hover': string;
        '--n-button-border-pressed': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly simple: BooleanConstructor;
    readonly page: NumberConstructor;
    readonly defaultPage: {
        readonly type: NumberConstructor;
        readonly default: 1;
    };
    readonly itemCount: NumberConstructor;
    readonly pageCount: NumberConstructor;
    readonly defaultPageCount: {
        readonly type: NumberConstructor;
        readonly default: 1;
    };
    readonly showSizePicker: BooleanConstructor;
    readonly pageSize: NumberConstructor;
    readonly defaultPageSize: NumberConstructor;
    readonly pageSizes: {
        readonly type: PropType<Array<number | PaginationSizeOption>>;
        readonly default: () => number[];
    };
    readonly showQuickJumper: BooleanConstructor;
    readonly size: {
        readonly type: PropType<Size>;
        readonly default: "medium";
    };
    readonly disabled: BooleanConstructor;
    readonly pageSlot: {
        readonly type: NumberConstructor;
        readonly default: 9;
    };
    readonly selectProps: PropType<SelectProps>;
    readonly prev: PropType<RenderPrev>;
    readonly next: PropType<RenderNext>;
    readonly goto: PropType<RenderGoto>;
    readonly prefix: PropType<RenderPrefix>;
    readonly suffix: PropType<RenderSuffix>;
    readonly label: PropType<PaginationRenderLabel>;
    readonly displayOrder: {
        readonly type: PropType<Array<"pages" | "size-picker" | "quick-jumper">>;
        readonly default: readonly ["pages", "size-picker", "quick-jumper"];
    };
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly showQuickJumpDropdown: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly 'onUpdate:page': PropType<MaybeArray<(page: number) => void>>;
    readonly onUpdatePage: PropType<MaybeArray<(page: number) => void>>;
    readonly 'onUpdate:pageSize': PropType<MaybeArray<(pageSize: number) => void>>;
    readonly onUpdatePageSize: PropType<MaybeArray<(pageSize: number) => void>>;
    /** @deprecated */
    readonly onPageSizeChange: PropType<MaybeArray<(pageSize: number) => void>>;
    /** @deprecated */
    readonly onChange: PropType<MaybeArray<(page: number) => void>>;
    readonly theme: PropType<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Pagination", {
        buttonColor: string;
        buttonColorHover: string;
        buttonColorPressed: string;
        buttonBorder: string;
        buttonBorderHover: string;
        buttonBorderPressed: string;
        buttonIconColor: string;
        buttonIconColorHover: string;
        buttonIconColorPressed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorPressed: string;
        itemTextColorActive: string;
        itemTextColorDisabled: string;
        itemColor: string;
        itemColorHover: string;
        itemColorPressed: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorDisabled: string;
        itemBorder: string;
        itemBorderHover: string;
        itemBorderPressed: string;
        itemBorderActive: string;
        itemBorderDisabled: string;
        itemBorderRadius: string;
        itemSizeSmall: string;
        itemSizeMedium: string;
        itemSizeLarge: string;
        itemFontSizeSmall: string;
        itemFontSizeMedium: string;
        itemFontSizeLarge: string;
        jumperFontSizeSmall: string;
        jumperFontSizeMedium: string;
        jumperFontSizeLarge: string;
        jumperTextColor: string;
        jumperTextColorDisabled: string;
        itemPaddingSmall: string;
        itemMarginSmall: string;
        itemMarginSmallRtl: string;
        itemPaddingMedium: string;
        itemMarginMedium: string;
        itemMarginMediumRtl: string;
        itemPaddingLarge: string;
        itemMarginLarge: string;
        itemMarginLargeRtl: string;
        buttonIconSizeSmall: string;
        buttonIconSizeMedium: string;
        buttonIconSizeLarge: string;
        inputWidthSmall: string;
        selectWidthSmall: string;
        inputMarginSmall: string;
        inputMarginSmallRtl: string;
        selectMarginSmall: string;
        prefixMarginSmall: string;
        suffixMarginSmall: string;
        inputWidthMedium: string;
        selectWidthMedium: string;
        inputMarginMedium: string;
        inputMarginMediumRtl: string;
        selectMarginMedium: string;
        prefixMarginMedium: string;
        suffixMarginMedium: string;
        inputWidthLarge: string;
        selectWidthLarge: string;
        inputMarginLarge: string;
        inputMarginLargeRtl: string;
        selectMarginLarge: string;
        prefixMarginLarge: string;
        suffixMarginLarge: string;
    }, {
        Select: import("../../_mixins").Theme<"Select", {
            menuBoxShadow: string;
        }, {
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
        Popselect: import("../../_mixins").Theme<"Popselect", {
            menuBoxShadow: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
        }>;
    }>>>;
}>> & Readonly<{}>, {
    readonly size: Size;
    readonly to: string | boolean | HTMLElement;
    readonly disabled: boolean;
    readonly simple: boolean;
    readonly defaultPage: number;
    readonly defaultPageCount: number;
    readonly showSizePicker: boolean;
    readonly pageSizes: (number | PaginationSizeOption)[];
    readonly showQuickJumper: boolean;
    readonly pageSlot: number;
    readonly displayOrder: ("pages" | "size-picker" | "quick-jumper")[];
    readonly showQuickJumpDropdown: boolean;
}, SlotsType<PaginationSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
