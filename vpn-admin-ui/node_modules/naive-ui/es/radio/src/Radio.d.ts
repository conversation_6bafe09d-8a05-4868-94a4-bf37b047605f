import type { ExtractPublicPropTypes } from '../../_utils';
export declare const radioProps: {
    readonly name: StringConstructor;
    readonly value: {
        readonly type: import("vue").PropType<string | number | boolean>;
        readonly default: "on";
    };
    readonly checked: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly defaultChecked: BooleanConstructor;
    readonly disabled: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly label: StringConstructor;
    readonly size: import("vue").PropType<"small" | "medium" | "large">;
    readonly onUpdateChecked: import("vue").PropType<undefined | import("../../_utils").MaybeArray<(value: boolean) => void>>;
    readonly 'onUpdate:checked': import("vue").PropType<undefined | import("../../_utils").MaybeArray<(value: boolean) => void>>;
    readonly checkedValue: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
};
export type RadioProps = ExtractPublicPropTypes<typeof radioProps>;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly name: StringConstructor;
    readonly value: {
        readonly type: import("vue").PropType<string | number | boolean>;
        readonly default: "on";
    };
    readonly checked: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly defaultChecked: BooleanConstructor;
    readonly disabled: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly label: StringConstructor;
    readonly size: import("vue").PropType<"small" | "medium" | "large">;
    readonly onUpdateChecked: import("vue").PropType<undefined | import("../../_utils").MaybeArray<(value: boolean) => void>>;
    readonly 'onUpdate:checked': import("vue").PropType<undefined | import("../../_utils").MaybeArray<(value: boolean) => void>>;
    readonly checkedValue: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
}>, import("./use-radio").UseRadio & {
    rtlEnabled: import("vue").Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-label-line-height': string;
        '--n-label-font-weight': string;
        '--n-box-shadow': string;
        '--n-box-shadow-active': string;
        '--n-box-shadow-disabled': string;
        '--n-box-shadow-focus': string;
        '--n-box-shadow-hover': string;
        '--n-color': string;
        '--n-color-active': string;
        '--n-color-disabled': string;
        '--n-dot-color-active': string;
        '--n-dot-color-disabled': string;
        '--n-font-size': string;
        '--n-radio-size': string;
        '--n-text-color': string;
        '--n-text-color-disabled': string;
        '--n-label-padding': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly name: StringConstructor;
    readonly value: {
        readonly type: import("vue").PropType<string | number | boolean>;
        readonly default: "on";
    };
    readonly checked: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly defaultChecked: BooleanConstructor;
    readonly disabled: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly label: StringConstructor;
    readonly size: import("vue").PropType<"small" | "medium" | "large">;
    readonly onUpdateChecked: import("vue").PropType<undefined | import("../../_utils").MaybeArray<(value: boolean) => void>>;
    readonly 'onUpdate:checked': import("vue").PropType<undefined | import("../../_utils").MaybeArray<(value: boolean) => void>>;
    readonly checkedValue: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Radio", {
        labelLineHeight: string;
        buttonHeightSmall: string;
        buttonHeightMedium: string;
        buttonHeightLarge: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        boxShadow: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        boxShadowHover: string;
        boxShadowDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        textColor: string;
        textColorDisabled: string;
        dotColorActive: string;
        dotColorDisabled: string;
        buttonBorderColor: string;
        buttonBorderColorActive: string;
        buttonBorderColorHover: string;
        buttonColor: string;
        buttonColorActive: string;
        buttonTextColor: string;
        buttonTextColorActive: string;
        buttonTextColorHover: string;
        opacityDisabled: string;
        buttonBoxShadowFocus: string;
        buttonBoxShadowHover: string;
        buttonBoxShadow: string;
        buttonBorderRadius: string;
        radioSizeSmall: string;
        radioSizeMedium: string;
        radioSizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
}>> & Readonly<{}>, {
    readonly value: string | number | boolean;
    readonly disabled: boolean | undefined;
    readonly checked: boolean | undefined;
    readonly defaultChecked: boolean;
    readonly checkedValue: boolean | undefined;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
