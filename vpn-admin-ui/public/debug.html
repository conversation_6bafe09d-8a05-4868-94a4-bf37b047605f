<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端 API 调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>前端 API 调试</h1>
    <button onclick="testDirectFetch()">测试直接 fetch</button>
    <button onclick="testAxios()">测试 Axios</button>
    <button onclick="testCreateSubnet()">测试创建子网</button>
    
    <div id="results"></div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        const API_BASE_URL = 'http://**************:3000';
        const resultsDiv = document.getElementById('results');

        function addResult(title, content, isError = false) {
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(div);
        }

        async function testDirectFetch() {
            try {
                console.log('测试直接 fetch...');
                const response = await fetch(`${API_BASE_URL}/api/subnets`);
                const data = await response.json();
                
                addResult('✅ 直接 fetch 成功', JSON.stringify({
                    status: response.status,
                    data: data
                }, null, 2));
            } catch (error) {
                console.error('直接 fetch 失败:', error);
                addResult('❌ 直接 fetch 失败', error.message, true);
            }
        }

        async function testAxios() {
            try {
                console.log('测试 Axios...');
                const response = await axios.get(`${API_BASE_URL}/api/subnets`);
                
                addResult('✅ Axios 成功', JSON.stringify({
                    status: response.status,
                    data: response.data
                }, null, 2));
            } catch (error) {
                console.error('Axios 失败:', error);
                addResult('❌ Axios 失败', error.message, true);
            }
        }

        async function testCreateSubnet() {
            try {
                console.log('测试创建子网...');
                const subnetData = {
                    name: 'debug-subnet',
                    cidr: '***********/24',
                    port: 10099,
                    mtu: 1500,
                    identifier: 'debug-subnet-001',
                    description: '调试页面创建的子网',
                    status: 'active'
                };

                const response = await axios.post(`${API_BASE_URL}/api/subnets`, subnetData);
                
                addResult('✅ 创建子网成功', JSON.stringify({
                    status: response.status,
                    data: response.data
                }, null, 2));
            } catch (error) {
                console.error('创建子网失败:', error);
                addResult('❌ 创建子网失败', `${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`, true);
            }
        }

        // 页面加载时显示环境信息
        window.addEventListener('load', () => {
            addResult('🔧 环境信息', `
API 基础地址: ${API_BASE_URL}
当前页面: ${window.location.href}
User Agent: ${navigator.userAgent}
            `);
        });
    </script>
</body>
</html>
