<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 10px; padding: 10px 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>简单 API 测试</h1>
    
    <button onclick="testFetch()">测试 Fetch</button>
    <button onclick="testXHR()">测试 XMLHttpRequest</button>
    <button onclick="testCORS()">测试 CORS</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://**************:3000';
        const resultsDiv = document.getElementById('results');

        function addResult(title, content, isSuccess = true) {
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<h3>${title}</h3><pre>${JSON.stringify(content, null, 2)}</pre>`;
            resultsDiv.appendChild(div);
        }

        async function testFetch() {
            try {
                console.log('测试 Fetch API...');
                const response = await fetch(`${API_BASE}/api/subnets`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Fetch 响应状态:', response.status);
                const data = await response.json();
                console.log('Fetch 响应数据:', data);
                
                addResult('✅ Fetch 成功', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                });
            } catch (error) {
                console.error('Fetch 失败:', error);
                addResult('❌ Fetch 失败', {
                    message: error.message,
                    stack: error.stack
                }, false);
            }
        }

        function testXHR() {
            const xhr = new XMLHttpRequest();
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            console.log('XHR 成功:', data);
                            addResult('✅ XMLHttpRequest 成功', {
                                status: xhr.status,
                                data: data
                            });
                        } catch (e) {
                            console.error('XHR 解析失败:', e);
                            addResult('❌ XMLHttpRequest 解析失败', {
                                status: xhr.status,
                                responseText: xhr.responseText,
                                error: e.message
                            }, false);
                        }
                    } else {
                        console.error('XHR 失败:', xhr.status, xhr.statusText);
                        addResult('❌ XMLHttpRequest 失败', {
                            status: xhr.status,
                            statusText: xhr.statusText,
                            responseText: xhr.responseText
                        }, false);
                    }
                }
            };
            
            xhr.onerror = function() {
                console.error('XHR 网络错误');
                addResult('❌ XMLHttpRequest 网络错误', {
                    message: 'Network error occurred'
                }, false);
            };
            
            try {
                xhr.open('GET', `${API_BASE}/api/subnets`, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send();
                console.log('XHR 请求已发送');
            } catch (error) {
                console.error('XHR 发送失败:', error);
                addResult('❌ XMLHttpRequest 发送失败', {
                    message: error.message
                }, false);
            }
        }

        async function testCORS() {
            try {
                console.log('测试 CORS 预检请求...');
                const response = await fetch(`${API_BASE}/api/subnets`, {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                console.log('CORS 预检响应:', response.status);
                addResult('✅ CORS 预检成功', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries())
                });
            } catch (error) {
                console.error('CORS 预检失败:', error);
                addResult('❌ CORS 预检失败', {
                    message: error.message
                }, false);
            }
        }

        // 页面加载时显示基本信息
        window.addEventListener('load', () => {
            addResult('🔧 测试环境', {
                apiBase: API_BASE,
                userAgent: navigator.userAgent,
                location: window.location.href
            });
        });
    </script>
</body>
</html>
