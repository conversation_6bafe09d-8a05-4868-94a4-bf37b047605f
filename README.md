# Tinc Rust VPN

基于 Rust 语言重写的 Tinc VPN 系统，提供高性能、安全的点对点 VPN 解决方案。

## 功能特性

- 用户管理：注册、登录、权限控制
- 节点管理：添加、删除、状态监控
- 连接管理：自动建立、维护、监控连接
- 配置管理：版本控制、配置同步
- NAT 穿透：支持多种 NAT 类型
- 安全特性：加密通信、认证机制
- 日志系统：多级别日志、日志轮转
- 监控告警：性能监控、异常告警

## 系统要求

- Rust 1.70.0 或更高版本
- PostgreSQL 13 或更高版本
- Linux 系统（推荐 Ubuntu 20.04 LTS 或更高版本）

## 快速开始

### 使用 Docker 部署

1. 克隆仓库：
```bash
git clone https://github.com/your-username/tinc-rust.git
cd tinc-rust
```

2. 配置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的配置
```

3. 使用 Docker Compose 启动服务：
```bash
./deploy.sh
```

### 手动安装

1. 安装依赖：
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y build-essential pkg-config libssl-dev postgresql postgresql-contrib
```

2. 安装 Rust：
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

3. 构建项目：
```bash
make build
```

4. 安装服务：
```bash
sudo make install
```

## 配置说明

### 环境变量

主要配置项（完整配置请参考 `.env.example`）：

- `DATABASE_URL`: 数据库连接 URL
- `JWT_SECRET`: JWT 密钥
- `SERVER_HOST`: 服务器主机地址
- `SERVER_PORT`: 服务器端口
- `LOG_LEVEL`: 日志级别

### 配置文件

配置文件位于 `/var/lib/tinc-rust/configs/` 目录下：

- `config.json`: 主配置文件
- `nodes/`: 节点配置目录
- `connections/`: 连接配置目录

## 使用说明

### 启动服务

```bash
sudo systemctl start tinc-rust
```

### 查看状态

```bash
sudo systemctl status tinc-rust
```

### 查看日志

```bash
sudo journalctl -u tinc-rust
```

### 停止服务

```bash
sudo systemctl stop tinc-rust
```

## API 文档

API 文档可通过以下方式访问：

- 开发环境：http://localhost:3000/docs
- 生产环境：https://your-domain/docs

## 开发指南

### 项目结构

```
tinc-rust/
├── src/
│   ├── api/        # API 接口
│   ├── config/     # 配置管理
│   ├── db/         # 数据库操作
│   ├── log/        # 日志管理
│   ├── net/        # 网络功能
│   └── service/    # 业务逻辑
├── tests/          # 测试文件
├── migrations/     # 数据库迁移
└── scripts/        # 脚本文件
```

### 开发命令

```bash
# 运行测试
make test

# 代码检查
make check

# 生成文档
make doc

# 运行服务
make run
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目维护者：[Your Name](mailto:<EMAIL>)
- 项目主页：[GitHub Repository](https://github.com/your-username/tinc-rust) 