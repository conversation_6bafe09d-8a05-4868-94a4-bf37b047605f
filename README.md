# Rust Tinc VPN 系统

基于 Rust 语言重写的现代化 Tinc VPN 系统，具备 NAT 穿透、多用户管理、配置热更新等商业化功能。

## 🚀 项目特性

### 核心功能
- **NAT 穿透**: 支持 STUN 协议探测 NAT 类型，自动选择最优打洞策略
- **多用户管理**: 基于 JWT + RBAC 的权限管理系统
- **配置热更新**: 支持配置文件加密打包与实时推送
- **连接监控**: 实时监控连接状态与网络质量
- **API 控制**: 完整的 REST API 接口，支持 Web 管理界面

### 技术架构
- **后端框架**: Axum + Tokio + SQLx + Tracing
- **数据库**: MySQL
- **认证**: JWT + RBAC 权限模型
- **配置**: TOML + 加密 tar.gz
- **监控**: Prometheus metrics
- **日志**: 结构化日志与事件流

## 📋 系统要求

### 运行环境
- Rust 1.70+
- MySQL 8.0+
- Linux/macOS/Windows

### 依赖工具
- `curl` (API 测试)
- `jq` (JSON 处理)

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/rust-tinc-vpn.git
cd rust-tinc-vpn
```

### 2. 配置数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE tinc_vpn;"

# 配置数据库连接
export DATABASE_URL="mysql://root:password@localhost:3306/tinc_vpn"
```

### 3. 编译运行
```bash
# 编译项目
cargo build --release

# 运行服务
cargo run
```

### 4. 测试 API
```bash
# 运行 API 测试脚本
./test_api.sh
```

## 配置说明

### 环境变量

主要配置项（完整配置请参考 `.env.example`）：

- `DATABASE_URL`: 数据库连接 URL
- `JWT_SECRET`: JWT 密钥
- `SERVER_HOST`: 服务器主机地址
- `SERVER_PORT`: 服务器端口
- `LOG_LEVEL`: 日志级别

### 配置文件

配置文件位于 `/var/lib/tinc-rust/configs/` 目录下：

- `config.json`: 主配置文件
- `nodes/`: 节点配置目录
- `connections/`: 连接配置目录

## 使用说明

### 启动服务

```bash
sudo systemctl start tinc-rust
```

### 查看状态

```bash
sudo systemctl status tinc-rust
```

### 查看日志

```bash
sudo journalctl -u tinc-rust
```

### 停止服务

```bash
sudo systemctl stop tinc-rust
```

## API 文档

API 文档可通过以下方式访问：

- 开发环境：http://localhost:3000/docs
- 生产环境：https://your-domain/docs

## 开发指南

### 项目结构

```
tinc-rust/
├── src/
│   ├── api/        # API 接口
│   ├── config/     # 配置管理
│   ├── db/         # 数据库操作
│   ├── log/        # 日志管理
│   ├── net/        # 网络功能
│   └── service/    # 业务逻辑
├── tests/          # 测试文件
├── migrations/     # 数据库迁移
└── scripts/        # 脚本文件
```

### 开发命令

```bash
# 运行测试
make test

# 代码检查
make check

# 生成文档
make doc

# 运行服务
make run
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目维护者：[Your Name](mailto:<EMAIL>)
- 项目主页：[GitHub Repository](https://github.com/your-username/tinc-rust) 