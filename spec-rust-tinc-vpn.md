项目开发文档：Rust 重写版 Tinc VPN 系统

一、项目概述

1.1 项目背景

当前的 Tinc 1.1.8 版本使用 C 语言实现，存在以下问题：
	•	内存安全风险
	•	缺乏现代异步模型支持
	•	模块化程度低，不利于商业化扩展

因此本项目基于 Rust 语言进行重写，结合现代网络需求，构建一套可用于商业化部署的高性能、可扩展、易管控的 Tinc VPN 系统。

1.2 项目目标
	•	用 Rust 重写 Tinc 1.1.8 的全部核心逻辑
	•	支持自动 NAT 类型识别及多种打洞策略切换
	•	提供标准化 API 接口供 Web 管理界面调用
	•	支持多用户、多服务端、多客户端、多终端
	•	模块化设计，支持后续插件、策略、流量计费等扩展

1.3 技术选型

类别	技术	说明
后端语言	Rust	安全、并发强、适合网络编程
框架	Axum	异步、现代、组件化强
数据库	MySQL	成熟、高性能、易于管理
存储格式	TOML + Encrypted tar.gz	简洁且便于加密配置管理
通信协议	WebSocket + REST API + TUN	控制平面和数据平面分离
加密	TLS / Noise	现代加密协议，安全高效

二、系统架构设计

2.1 系统组件
	•	VPN 核心服务（Rust）
	•	Web 控制 API（Axum）
	•	配置管理与同步模块
	•	NAT 探测与打洞策略引擎
	•	状态监控与事件中心
	•	MySQL 数据持久化层

2.2 架构图

[前端管理] <--REST/WebSocket--> [Axum 控制平面]
                                         |
                             +-----------+-----------+
                             |                       |
                 [VPN 服务端核心]         [客户端 agent (Rust)]
                             |                       |
                 [TUN接口 + NAT穿透]       [TUN + Config解包]

2.3 模块划分
	•	config：配置加载与生成
	•	crypto：加密与密钥交换
	•	net：打洞、隧道通信模块
	•	tunnel：连接生命周期管理
	•	api：提供控制接口
	•	db：与 MySQL 通信
	•	log：日志与事件
	•	daemon：主程序与 systemd 集成

三、功能模块说明

3.1 NAT 类型识别与打洞策略
	•	支持 STUN 探测 NAT 类型（Full Cone, Symmetric 等）
	•	支持 UDP Hole Punching、TCP simultaneous open
	•	按策略排序自动尝试方式直至连接成功
	•	若失败可回退到 relay 中转（可选）

3.2 客户端配置与控制
	•	支持配置加密打包为 tar.gz
	•	客户端自动解包、对比配置变更、备份旧配置
	•	支持 WebSocket 通道接收配置并报告状态

3.3 API 控制能力
	•	多用户登录 + JWT 授权
	•	管理客户端/服务端/配置/连接状态
	•	触发重连、推送配置、断开会话
	•	获取 NAT 类型与打洞日志
	•	接入策略与权限模型控制

3.4 多用户/多终端支持
	•	用户支持 RBAC 权限系统
	•	每个用户下可绑定多个客户端设备（mac, ip）
	•	支持查询终端在线状态、下线控制、异常拦截

四、接口设计（Axum API）

4.1 鉴权接口
	•	POST /api/login：获取 JWT Token
	•	GET /api/profile/me：获取当前用户信息

4.2 节点管理
	•	GET /api/nodes：获取所有节点信息
	•	POST /api/nodes：添加节点
	•	PUT /api/nodes/{id}：编辑节点
	•	DELETE /api/nodes/{id}：删除节点

4.3 客户端连接状态
	•	GET /api/connections：查看连接会话
	•	POST /api/connections/{id}/test：测试打洞成功性
	•	POST /api/connections/{id}/terminate：断开连接

4.4 NAT 探测与打洞状态
	•	GET /api/nat/{client_id}：获取 NAT 类型与策略
	•	POST /api/nat/{client_id}/detect：重新检测 NAT 类型

4.5 配置管理
	•	GET /api/configs/{client_id}：下载配置
	•	POST /api/configs/{client_id}/push：重新推送配置
	•	GET /api/configs/{client_id}/history：查看配置历史
	•	POST /api/configs/{client_id}/rollback：回滚配置版本

4.6 用户与权限
	•	GET /api/users：获取用户列表
	•	POST /api/users：添加用户
	•	PUT /api/users/{id}：编辑用户
	•	DELETE /api/users/{id}：删除用户
	•	POST /api/users/{id}/roles：设置角色
	•	GET /api/roles：查询所有角色

4.7 监控与日志
	•	GET /api/logs：系统日志
	•	GET /api/events：打洞/上线等事件流
	•	GET /metrics：Prometheus 监控接口

五、数据库模型设计

users
	•	id / username / email / password / role / created_at / last_login

nodes
	•	id / name / type (client/server) / ip / mac / status / tunnel_type / last_active

connections
	•	id / client_id / server_id / method_used / latency / status / updated_at

configs
	•	id / client_id / version / hash / created_at / content

policies
	•	id / name / description / bandwidth_limit / allow_ips / allow_ports

logs/events
	•	id / type / message / source / created_at

六、安全策略
	•	所有配置文件必须使用对称加密（客户端密钥写入 .env）
	•	所有 API 通过 JWT + RBAC 控制访问
	•	客户端 WebSocket 信道仅允许心跳与状态上报，配置下发需验证签名
	•	所有日志记录关键行为（登录、配置变更、连接失败）

七、部署规范
	•	使用 Cargo 构建，支持 cross 编译（多平台支持）
	•	后端支持 systemd 启动为守护进程
	•	前端部署建议使用 Vite + Nginx
	•	配置分为 dev/test/prod 三套
	•	支持一键部署脚本 + Dockerfile

八、测试建议
	•	单元测试：配置解析、连接生命周期、打洞模块
	•	集成测试：服务端与多个客户端模拟连通
	•	NAT 模拟测试：使用虚拟 NAT 模拟器运行 UDP 打洞
	•	性能测试：多连接并发、配置热更新
	•	接口测试：Postman 或 REST Assured 自动化


⸻

