#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印带颜色的消息
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        error "$1 未安装，请先安装"
    fi
}

# 检查必要的命令
check_command docker
check_command docker-compose

# 创建必要的目录
log "创建必要的目录..."
mkdir -p data/{configs,logs}

# 检查环境文件
if [ ! -f .env ]; then
    log "创建 .env 文件..."
    cat > .env << EOF
# 数据库配置
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=tinc

# 应用配置
RUST_LOG=info
DATABASE_URL=************************************

# JWT 配置
JWT_SECRET=your-secret-key
JWT_EXPIRATION=86400

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=3000
EOF
    warn "请修改 .env 文件中的配置"
fi

# 创建 docker-compose.yml
log "创建 docker-compose.yml..."
cat > docker-compose.yml << EOF
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data
    environment:
      - DATABASE_URL=************************************
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=tinc
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
EOF

# 构建和启动服务
log "构建和启动服务..."
docker-compose up -d --build

# 等待数据库就绪
log "等待数据库就绪..."
sleep 10

# 运行数据库迁移
log "运行数据库迁移..."
docker-compose exec app ./tinc_rust migrate

log "部署完成！"
log "服务地址: http://localhost:3000"
log "健康检查: http://localhost:3000/health" 