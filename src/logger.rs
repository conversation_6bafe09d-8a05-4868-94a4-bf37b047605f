use std::path::PathBuf;
use tracing::{info, error, warn, debug};
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter,
};

/// 日志配置
#[derive(Debug, <PERSON>lone)]
pub struct LoggerConfig {
    /// 日志文件路径
    pub log_dir: PathBuf,
    /// 日志文件名
    pub log_file: String,
    /// 日志级别
    pub log_level: String,
    /// 是否输出到控制台
    pub console_output: bool,
    /// 是否输出到文件
    pub file_output: bool,
    /// 日志文件大小限制（字节）
    pub max_file_size: usize,
    /// 保留的日志文件数量
    pub max_files: usize,
}

impl Default for LoggerConfig {
    fn default() -> Self {
        Self {
            log_dir: PathBuf::from("logs"),
            log_file: "tinc.log".to_string(),
            log_level: "info".to_string(),
            console_output: true,
            file_output: true,
            max_file_size: 10 * 1024 * 1024, // 10MB
            max_files: 5,
        }
    }
}

/// 初始化日志系统
pub fn init_logger(config: LoggerConfig) -> Result<(), Box<dyn std::error::Error>> {
    // 创建日志目录
    if !config.log_dir.exists() {
        std::fs::create_dir_all(&config.log_dir)?;
    }

    // 设置日志过滤器
    let env_filter = EnvFilter::try_from_default_env()
        .or_else(|_| EnvFilter::try_new(&config.log_level))
        .unwrap_or_else(|_| EnvFilter::new("info"));

    // 创建文件输出
    let file_appender = if config.file_output {
        let file_appender = RollingFileAppender::new(
            Rotation::DAILY,
            &config.log_dir,
            &config.log_file,
        );
        Some(fmt::layer()
            .with_ansi(false)
            .with_writer(file_appender)
            .with_span_events(FmtSpan::CLOSE))
    } else {
        None
    };

    // 创建控制台输出
    let console_layer = if config.console_output {
        Some(fmt::layer()
            .with_ansi(true)
            .with_span_events(FmtSpan::CLOSE))
    } else {
        None
    };

    // 初始化日志系统
    tracing_subscriber::registry()
        .with(env_filter)
        .with(file_appender)
        .with(console_layer)
        .init();

    info!("日志系统初始化完成");
    Ok(())
}

/// 记录错误日志
pub fn log_error(error: &dyn std::error::Error) {
    error!("错误: {}", error);
}

/// 记录警告日志
pub fn log_warning(message: &str) {
    warn!("警告: {}", message);
}

/// 记录信息日志
pub fn log_info(message: &str) {
    info!("信息: {}", message);
}

/// 记录调试日志
pub fn log_debug(message: &str) {
    debug!("调试: {}", message);
}

/// 记录 API 请求日志
pub fn log_api_request(method: &str, path: &str, status: u16, duration: std::time::Duration) {
    info!(
        "API请求: {} {} - 状态: {} - 耗时: {:?}",
        method, path, status, duration
    );
}

/// 记录数据库操作日志
pub fn log_db_operation(operation: &str, table: &str, duration: std::time::Duration) {
    info!(
        "数据库操作: {} {} - 耗时: {:?}",
        operation, table, duration
    );
}

/// 记录配置操作日志
pub fn log_config_operation(operation: &str, client_id: &str, version: i32) {
    info!(
        "配置操作: {} - 客户端: {} - 版本: {}",
        operation, client_id, version
    );
}

/// 记录节点操作日志
pub fn log_node_operation(operation: &str, node_id: &str, status: &str) {
    info!(
        "节点操作: {} - 节点: {} - 状态: {}",
        operation, node_id, status
    );
}

/// 记录连接操作日志
pub fn log_connection_operation(operation: &str, connection_id: &str, status: &str) {
    info!(
        "连接操作: {} - 连接: {} - 状态: {}",
        operation, connection_id, status
    );
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_logger_config() {
        let config = LoggerConfig::default();
        assert_eq!(config.log_level, "info");
        assert!(config.console_output);
        assert!(config.file_output);
    }

    #[test]
    fn test_logger_init() {
        let temp_dir = tempdir().unwrap();
        let config = LoggerConfig {
            log_dir: temp_dir.path().to_path_buf(),
            ..Default::default()
        };

        init_logger(config).unwrap();
        log_info("测试日志");
        log_error(&std::io::Error::new(std::io::ErrorKind::Other, "测试错误"));
        log_warning("测试警告");
        log_debug("测试调试");
    }

    #[test]
    fn test_operation_logs() {
        let temp_dir = tempdir().unwrap();
        let config = LoggerConfig {
            log_dir: temp_dir.path().to_path_buf(),
            ..Default::default()
        };

        init_logger(config).unwrap();

        log_api_request("GET", "/api/users", 200, std::time::Duration::from_millis(100));
        log_db_operation("SELECT", "users", std::time::Duration::from_millis(50));
        log_config_operation("push", "test-client", 1);
        log_node_operation("create", "test-node", "online");
        log_connection_operation("test", "test-connection", "connected");
    }
} 