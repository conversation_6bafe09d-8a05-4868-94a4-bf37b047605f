use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use sha2::{Sha256, Digest};
use base64::{Engine as _, engine::general_purpose::STANDARD as BASE64};

/// 生成唯一ID
pub fn generate_id() -> String {
    Uuid::new_v4().to_string()
}

/// 获取当前时间戳（毫秒）
pub fn get_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64
}

/// 计算字符串的SHA256哈希
pub fn sha256(input: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(input.as_bytes());
    let result = hasher.finalize();
    BASE64.encode(result)
}

/// 验证输入字符串
pub fn validate_input(input: &str, min_length: usize, max_length: usize) -> bool {
    let length = input.chars().count();
    length >= min_length && length <= max_length
}

/// 格式化错误信息
pub fn format_error(error: &impl std::fmt::Display) -> String {
    format!("错误: {}", error)
}

/// 检查IP地址格式
pub fn is_valid_ip(ip: &str) -> bool {
    ip.parse::<std::net::IpAddr>().is_ok()
}

/// 检查端口号范围
pub fn is_valid_port(port: u32) -> bool {
    port > 0 && port < 65536
}

/// 检查MAC地址格式
pub fn is_valid_mac(mac: &str) -> bool {
    let parts: Vec<&str> = mac.split(':').collect();
    if parts.len() != 6 {
        return false;
    }
    parts.iter().all(|part| {
        part.len() == 2 && part.chars().all(|c| c.is_ascii_hexdigit())
    })
}

/// 检查CIDR格式
pub fn is_valid_cidr(cidr: &str) -> bool {
    cidr.parse::<ipnetwork::IpNetwork>().is_ok()
}

/// 检查MTU范围
pub fn is_valid_mtu(mtu: u16) -> bool {
    mtu >= 1280 && mtu <= 9000
}

/// 检查超时时间范围
pub fn is_valid_timeout(timeout: u64) -> bool {
    timeout >= 5 && timeout <= 300
}

/// 检查心跳间隔范围
pub fn is_valid_heartbeat(heartbeat: u64) -> bool {
    heartbeat >= 1 && heartbeat <= 60
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_id() {
        let id1 = generate_id();
        let id2 = generate_id();
        assert_ne!(id1, id2);
    }

    #[test]
    fn test_get_timestamp() {
        let ts1 = get_timestamp();
        std::thread::sleep(std::time::Duration::from_millis(1));
        let ts2 = get_timestamp();
        assert!(ts2 > ts1);
    }

    #[test]
    fn test_sha256() {
        let input = "test";
        let hash = sha256(input);
        assert_eq!(hash.len(), 44); // Base64 encoded SHA256 hash length
    }

    #[test]
    fn test_validate_input() {
        assert!(validate_input("test", 1, 10));
        assert!(!validate_input("", 1, 10));
        assert!(!validate_input("too_long_string", 1, 10));
    }

    #[test]
    fn test_is_valid_ip() {
        assert!(is_valid_ip("127.0.0.1"));
        assert!(is_valid_ip("::1"));
        assert!(!is_valid_ip("invalid"));
    }

    #[test]
    fn test_is_valid_port() {
        assert!(is_valid_port(80));
        assert!(!is_valid_port(0));
        assert!(!is_valid_port(65536));
    }

    #[test]
    fn test_is_valid_mac() {
        assert!(is_valid_mac("00:11:22:33:44:55"));
        assert!(!is_valid_mac("invalid"));
        assert!(!is_valid_mac("00:11:22:33:44"));
    }

    #[test]
    fn test_is_valid_cidr() {
        assert!(is_valid_cidr("***********/24"));
        assert!(is_valid_cidr("2001:db8::/32"));
        assert!(!is_valid_cidr("invalid"));
    }

    #[test]
    fn test_is_valid_mtu() {
        assert!(is_valid_mtu(1500));
        assert!(!is_valid_mtu(1000));
        assert!(!is_valid_mtu(10000));
    }

    #[test]
    fn test_is_valid_timeout() {
        assert!(is_valid_timeout(30));
        assert!(!is_valid_timeout(1));
        assert!(!is_valid_timeout(400));
    }

    #[test]
    fn test_is_valid_heartbeat() {
        assert!(is_valid_heartbeat(5));
        assert!(!is_valid_heartbeat(0));
        assert!(!is_valid_heartbeat(100));
    }
} 