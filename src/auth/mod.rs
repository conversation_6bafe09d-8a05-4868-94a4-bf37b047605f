use std::collections::HashMap;
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, Header, Validation};
use serde::{Deserialize, Serialize};
use thiserror::Error;
use uuid::Uuid;
use bcrypt::{hash, verify, DEFAULT_COST};

use crate::models::{User, Claims};

/// JWT 密钥
const JWT_SECRET: &str = "your-secret-key-here"; // 实际部署时应从环境变量读取

/// Token 过期时间（小时）
const TOKEN_EXPIRY_HOURS: i64 = 24;

/// 认证服务
#[derive(Clone)]
pub struct AuthService {
    encoding_key: EncodingKey,
    decoding_key: Decoding<PERSON>ey,
    validation: Validation,
}

impl AuthService {
    /// 创建新的认证服务
    pub fn new() -> Self {
        let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| JWT_SECRET.to_string());
        let encoding_key = EncodingKey::from_secret(secret.as_ref());
        let decoding_key = DecodingKey::from_secret(secret.as_ref());
        
        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = true;
        validation.validate_nbf = false;
        validation.validate_aud = false;

        Self {
            encoding_key,
            decoding_key,
            validation,
        }
    }

    /// 生成 JWT Token
    pub fn generate_token(&self, user: &User) -> Result<String, AuthError> {
        let now = Utc::now();
        let exp = now + Duration::hours(TOKEN_EXPIRY_HOURS);

        let claims = Claims {
            sub: user.id.to_string(),
            exp: exp.timestamp() as usize,
            iat: now.timestamp() as usize,
            role: user.role.clone(),
        };

        encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(AuthError::TokenGeneration)
    }

    /// 验证 JWT Token
    pub fn verify_token(&self, token: &str) -> Result<Claims, AuthError> {
        decode::<Claims>(token, &self.decoding_key, &self.validation)
            .map(|data| data.claims)
            .map_err(AuthError::TokenValidation)
    }

    /// 哈希密码
    pub fn hash_password(&self, password: &str) -> Result<String, AuthError> {
        hash(password, DEFAULT_COST).map_err(AuthError::PasswordHashing)
    }

    /// 验证密码
    pub fn verify_password(&self, password: &str, hash: &str) -> Result<bool, AuthError> {
        verify(password, hash).map_err(AuthError::PasswordVerification)
    }

    /// 从 Token 中提取用户 ID
    pub fn extract_user_id(&self, token: &str) -> Result<Uuid, AuthError> {
        let claims = self.verify_token(token)?;
        Uuid::parse_str(&claims.sub).map_err(|_| AuthError::InvalidUserId)
    }

    /// 检查用户权限
    pub fn check_permission(&self, claims: &Claims, required_role: &str) -> bool {
        match (claims.role.as_str(), required_role) {
            ("admin", _) => true,
            ("user", "user") => true,
            ("guest", "guest") => true,
            _ => false,
        }
    }
}

impl Default for AuthService {
    fn default() -> Self {
        Self::new()
    }
}

/// 用户角色
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum UserRole {
    Admin,
    User,
    Guest,
}

impl UserRole {
    pub fn as_str(&self) -> &'static str {
        match self {
            UserRole::Admin => "admin",
            UserRole::User => "user",
            UserRole::Guest => "guest",
        }
    }

    pub fn from_str(s: &str) -> Self {
        match s {
            "admin" => UserRole::Admin,
            "user" => UserRole::User,
            "guest" => UserRole::Guest,
            _ => UserRole::Guest,
        }
    }
}

/// 权限管理器
#[derive(Clone)]
pub struct PermissionManager {
    /// 角色权限映射
    role_permissions: HashMap<String, Vec<String>>,
}

impl PermissionManager {
    /// 创建新的权限管理器
    pub fn new() -> Self {
        let mut role_permissions = HashMap::new();
        
        // 管理员权限
        role_permissions.insert(
            "admin".to_string(),
            vec![
                "users:read".to_string(),
                "users:write".to_string(),
                "users:delete".to_string(),
                "nodes:read".to_string(),
                "nodes:write".to_string(),
                "nodes:delete".to_string(),
                "connections:read".to_string(),
                "connections:write".to_string(),
                "connections:delete".to_string(),
                "configs:read".to_string(),
                "configs:write".to_string(),
                "configs:delete".to_string(),
                "logs:read".to_string(),
                "system:admin".to_string(),
            ],
        );

        // 普通用户权限
        role_permissions.insert(
            "user".to_string(),
            vec![
                "nodes:read".to_string(),
                "connections:read".to_string(),
                "configs:read".to_string(),
                "profile:read".to_string(),
                "profile:write".to_string(),
            ],
        );

        // 访客权限
        role_permissions.insert(
            "guest".to_string(),
            vec![
                "profile:read".to_string(),
            ],
        );

        Self { role_permissions }
    }

    /// 检查权限
    pub fn has_permission(&self, role: &str, permission: &str) -> bool {
        self.role_permissions
            .get(role)
            .map(|perms| perms.contains(&permission.to_string()))
            .unwrap_or(false)
    }

    /// 获取角色权限列表
    pub fn get_role_permissions(&self, role: &str) -> Vec<String> {
        self.role_permissions
            .get(role)
            .cloned()
            .unwrap_or_default()
    }

    /// 添加权限
    pub fn add_permission(&mut self, role: &str, permission: &str) {
        self.role_permissions
            .entry(role.to_string())
            .or_insert_with(Vec::new)
            .push(permission.to_string());
    }

    /// 移除权限
    pub fn remove_permission(&mut self, role: &str, permission: &str) {
        if let Some(perms) = self.role_permissions.get_mut(role) {
            perms.retain(|p| p != permission);
        }
    }
}

impl Default for PermissionManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 认证错误
#[derive(Debug, Error)]
pub enum AuthError {
    #[error("Token 生成失败: {0}")]
    TokenGeneration(#[from] jsonwebtoken::errors::Error),
    
    #[error("Token 验证失败: {0}")]
    TokenValidation(jsonwebtoken::errors::Error),
    
    #[error("密码哈希失败: {0}")]
    PasswordHashing(#[from] bcrypt::BcryptError),
    
    #[error("密码验证失败: {0}")]
    PasswordVerification(bcrypt::BcryptError),
    
    #[error("无效的用户 ID")]
    InvalidUserId,
    
    #[error("权限不足")]
    InsufficientPermissions,
    
    #[error("用户未找到")]
    UserNotFound,
    
    #[error("密码错误")]
    InvalidPassword,
    
    #[error("Token 已过期")]
    TokenExpired,
    
    #[error("无效的 Token")]
    InvalidToken,
}

/// 登录请求
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// 登录响应
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserInfo,
    pub expires_at: i64,
}

/// 用户信息
#[derive(Debug, Serialize)]
pub struct UserInfo {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub role: String,
    pub permissions: Vec<String>,
}

impl From<User> for UserInfo {
    fn from(user: User) -> Self {
        let permission_manager = PermissionManager::new();
        let permissions = permission_manager.get_role_permissions(&user.role);
        
        Self {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            permissions,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_auth_service() {
        let auth = AuthService::new();
        
        // 测试密码哈希
        let password = "test_password";
        let hash = auth.hash_password(password).unwrap();
        assert!(auth.verify_password(password, &hash).unwrap());
        assert!(!auth.verify_password("wrong_password", &hash).unwrap());

        // 测试 Token 生成和验证
        let user = User {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: "<EMAIL>".to_string(),
            password: hash,
            role: "user".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_login: None,
        };

        let token = auth.generate_token(&user).unwrap();
        let claims = auth.verify_token(&token).unwrap();
        assert_eq!(claims.sub, user.id.to_string());
        assert_eq!(claims.role, user.role);
    }

    #[test]
    fn test_permission_manager() {
        let manager = PermissionManager::new();
        
        // 测试管理员权限
        assert!(manager.has_permission("admin", "users:read"));
        assert!(manager.has_permission("admin", "system:admin"));
        
        // 测试普通用户权限
        assert!(manager.has_permission("user", "nodes:read"));
        assert!(!manager.has_permission("user", "users:delete"));
        
        // 测试访客权限
        assert!(manager.has_permission("guest", "profile:read"));
        assert!(!manager.has_permission("guest", "nodes:read"));
    }
}
