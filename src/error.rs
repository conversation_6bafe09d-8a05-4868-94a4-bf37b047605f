use thiserror::Error;

/// 应用错误类型
#[derive(Error, Debug)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    DbError(#[from] DbError),

    #[error("配置错误: {0}")]
    ConfigError(#[from] crate::error::ConfigError),

    #[error("网络错误: {0}")]
    NetworkError(#[from] NetworkError),

    #[error("认证错误: {0}")]
    AuthError(#[from] AuthError),

    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("其他错误: {0}")]
    Other(String),
}

/// 数据库错误类型
#[derive(Error, Debug)]
pub enum DbError {
    #[error("未找到记录")]
    NotFound,

    #[error("重复记录")]
    Duplicate,

    #[error("数据库连接错误: {0}")]
    Connection(String),

    #[error("查询错误: {0}")]
    Query(String),

    #[error("事务错误: {0}")]
    Transaction(String),

    #[error("其他错误: {0}")]
    Other(String),
}

/// 配置错误类型
#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("配置文件不存在")]
    NotFound,

    #[error("配置文件格式错误: {0}")]
    Format(String),

    #[error("配置验证错误: {0}")]
    Validation(String),

    #[error("配置保存错误: {0}")]
    Save(String),

    #[error("配置加载错误: {0}")]
    Load(String),

    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("其他错误: {0}")]
    Other(String),
}

/// 网络错误类型
#[derive(Error, Debug)]
pub enum NetworkError {
    #[error("连接错误: {0}")]
    Connection(String),

    #[error("超时错误: {0}")]
    Timeout(String),

    #[error("NAT错误: {0}")]
    Nat(String),

    #[error("其他错误: {0}")]
    Other(String),
}

/// 认证错误类型
#[derive(Error, Debug)]
pub enum AuthError {
    #[error("无效的凭证")]
    InvalidCredentials,

    #[error("令牌过期")]
    TokenExpired,

    #[error("令牌无效")]
    InvalidToken,

    #[error("权限不足")]
    InsufficientPermissions,

    #[error("其他错误: {0}")]
    Other(String),
}

impl From<toml::de::Error> for ConfigError {
    fn from(err: toml::de::Error) -> Self {
        ConfigError::Validation(format!("TOML 解析错误: {}", err))
    }
}

impl From<toml::ser::Error> for ConfigError {
    fn from(err: toml::ser::Error) -> Self {
        ConfigError::Validation(format!("TOML 序列化错误: {}", err))
    }
}

impl From<toml::de::Error> for AppError {
    fn from(err: toml::de::Error) -> Self {
        AppError::ConfigError(crate::error::ConfigError::Validation(format!("TOML 解析错误: {}", err)))
    }
}

impl From<toml::ser::Error> for AppError {
    fn from(err: toml::ser::Error) -> Self {
        AppError::ConfigError(crate::error::ConfigError::Validation(format!("TOML 序列化错误: {}", err)))
    }
} 