use std::path::{Path, PathBuf};
use std::fs::{self, File, OpenOptions};
use std::io::{self, Write, BufWriter, BufRead, BufReader};
use std::sync::{Arc, Mutex};
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use tracing::{info, error, warn, Level};
use uuid::Uuid;
use thiserror::Error;
use tracing_subscriber::{
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter,
};
use tracing_appender::rolling::{RollingFileAppender, Rotation};

/// 日志错误
#[derive(Debug, Error)]
pub enum LogError {
    #[error("IO 错误: {0}")]
    Io(#[from] io::Error),
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    #[error("日志文件不存在: {0}")]
    NotFound(String),
    #[error("日志文件已存在: {0}")]
    AlreadyExists(String),
    #[error("无效的日志级别: {0}")]
    InvalidLevel(String),
    #[error("初始化错误: {0}")]
    InitError(String),
}

/// 日志级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LogLevel {
    /// 错误
    Error,
    /// 警告
    Warn,
    /// 信息
    Info,
    /// 调试
    Debug,
    /// 跟踪
    Trace,
}

impl From<Level> for LogLevel {
    fn from(level: Level) -> Self {
        match level {
            Level::ERROR => LogLevel::Error,
            Level::WARN => LogLevel::Warn,
            Level::INFO => LogLevel::Info,
            Level::DEBUG => LogLevel::Debug,
            Level::TRACE => LogLevel::Trace,
        }
    }
}

/// 日志条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    /// 日志 ID
    pub id: Uuid,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 日志级别
    pub level: LogLevel,
    /// 模块
    pub module: String,
    /// 消息
    pub message: String,
    /// 元数据
    pub metadata: Option<serde_json::Value>,
}

/// 日志管理器
#[derive(Clone)]
pub struct LogManager {
    /// 日志目录
    pub log_dir: PathBuf,
    /// 最大文件大小（字节）
    pub max_file_size: u64,
    /// 最大文件数量
    pub max_file_count: usize,
    /// 当前日志文件
    pub current_file: Arc<Mutex<BufWriter<File>>>,
}

impl LogManager {
    /// 创建新的日志管理器
    pub fn new(
        log_dir: Option<impl AsRef<Path>>,
        max_file_size: Option<u64>,
        max_file_count: Option<usize>,
    ) -> Result<Self, LogError> {
        let log_dir = log_dir
            .map(|p| p.as_ref().to_path_buf())
            .unwrap_or_else(|| PathBuf::from("logs"));
        
        let max_file_size = max_file_size.unwrap_or(10 * 1024 * 1024); // 默认 10MB
        let max_file_count = max_file_count.unwrap_or(10); // 默认保留 10 个文件
        
        if !log_dir.exists() {
            std::fs::create_dir_all(&log_dir)?;
        }
        
        let current_file = Arc::new(Mutex::new(BufWriter::new(
            Self::create_log_file(&log_dir)?
        )));
        
        Ok(Self {
            log_dir,
            max_file_size,
            max_file_count,
            current_file,
        })
    }

    pub fn init(&self) -> Result<(), LogError> {
        let file_appender = RollingFileAppender::new(
            Rotation::DAILY,
            &self.log_dir,
            "tinc-rust.log",
        );

        let (non_blocking, _guard) = tracing_appender::non_blocking(file_appender);

        let subscriber = tracing_subscriber::registry()
            .with(EnvFilter::from_default_env())
            .with(tracing_subscriber::fmt::Layer::default().with_writer(non_blocking));

        subscriber.try_init().map_err(|e| LogError::InitError(e.to_string()))?;

        Ok(())
    }

    /// 写入日志
    pub fn write_log(&self, entry: LogEntry) -> Result<(), LogError> {
        let mut writer = self.current_file.lock().unwrap();
        let json = serde_json::to_string(&entry)?;
        writer.write_all(json.as_bytes())?;
        writer.write_all(b"\n")?;
        writer.flush()?;

        // 检查文件大小
        if let Ok(metadata) = writer.get_ref().metadata() {
            if metadata.len() >= self.max_file_size {
                self.rotate_log()?;
            }
        }

        Ok(())
    }

    /// 轮转日志
    fn rotate_log(&self) -> Result<(), LogError> {
        // 创建新文件
        let new_file = Self::create_log_file(&self.log_dir)?;
        let new_writer = BufWriter::new(new_file);

        // 替换当前文件
        let mut current = self.current_file.lock().unwrap();
        *current = new_writer;

        // 清理旧文件
        self.cleanup_old_logs()?;

        Ok(())
    }

    /// 清理旧日志
    fn cleanup_old_logs(&self) -> Result<(), LogError> {
        let mut entries = Vec::new();
        for entry in fs::read_dir(&self.log_dir)? {
            let entry = entry?;
            if entry.path().extension().and_then(|s| s.to_str()) == Some("log") {
                if let Ok(metadata) = entry.metadata() {
                    entries.push((entry.path(), metadata.modified()?));
                }
            }
        }

        // 按修改时间排序
        entries.sort_by(|a, b| b.1.cmp(&a.1));

        // 删除超出限制的文件
        for (path, _) in entries.into_iter().skip(self.max_file_count) {
            fs::remove_file(path)?;
        }

        Ok(())
    }

    /// 读取日志
    pub fn read_logs(&self, start_time: Option<DateTime<Utc>>, end_time: Option<DateTime<Utc>>, level: Option<LogLevel>) -> Result<Vec<LogEntry>, LogError> {
        let mut entries = Vec::new();

        for entry in fs::read_dir(&self.log_dir)? {
            let entry = entry?;
            if entry.path().extension().and_then(|s| s.to_str()) == Some("log") {
                let file = File::open(entry.path())?;
                let reader = BufReader::new(file);

                for line in reader.lines() {
                    let line = line?;
                    if let Ok(entry) = serde_json::from_str::<LogEntry>(&line) {
                        // 检查时间范围
                        if let Some(start) = start_time {
                            if entry.timestamp < start {
                                continue;
                            }
                        }
                        if let Some(end) = end_time {
                            if entry.timestamp > end {
                                continue;
                            }
                        }

                        // 检查日志级别
                        if let Some(level) = level {
                            if entry.level != level {
                                continue;
                            }
                        }

                        entries.push(entry);
                    }
                }
            }
        }

        // 按时间排序
        entries.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

        Ok(entries)
    }

    /// 清理日志
    pub fn clear_logs(&self) -> Result<(), LogError> {
        for entry in fs::read_dir(&self.log_dir)? {
            let entry = entry?;
            if entry.path().extension().and_then(|s| s.to_str()) == Some("log") {
                fs::remove_file(entry.path())?;
            }
        }

        // 创建新的日志文件
        let new_file = Self::create_log_file(&self.log_dir)?;
        let new_writer = BufWriter::new(new_file);

        let mut current = self.current_file.lock().unwrap();
        *current = new_writer;

        Ok(())
    }

    /// 创建新的日志文件
    fn create_log_file(log_dir: &Path) -> Result<File, LogError> {
        let timestamp = chrono::Local::now().format("%Y%m%d_%H%M%S");
        let filename = format!("tinc-rust_{}.log", timestamp);
        let file = OpenOptions::new()
            .create(true)
            .write(true)
            .open(log_dir.join(filename))?;
        Ok(file)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_log_manager() {
        let temp_dir = tempdir().unwrap();
        let manager = LogManager::new(temp_dir.path(), 1024 * 1024, 5).unwrap();

        // 创建测试日志
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            level: LogLevel::Info,
            module: "test".to_string(),
            message: "Test log message".to_string(),
            metadata: Some(serde_json::json!({
                "key": "value"
            })),
        };

        // 测试写入日志
        manager.write_log(entry.clone()).unwrap();

        // 测试读取日志
        let logs = manager.read_logs(None, None, None).unwrap();
        assert_eq!(logs.len(), 1);
        assert_eq!(logs[0].id, entry.id);
        assert_eq!(logs[0].level, entry.level);
        assert_eq!(logs[0].module, entry.module);
        assert_eq!(logs[0].message, entry.message);
        assert_eq!(logs[0].metadata, entry.metadata);

        // 测试按级别过滤
        let logs = manager.read_logs(None, None, Some(LogLevel::Error)).unwrap();
        assert_eq!(logs.len(), 0);

        // 测试清理日志
        manager.clear_logs().unwrap();
        let logs = manager.read_logs(None, None, None).unwrap();
        assert_eq!(logs.len(), 0);
    }
} 