mod config;

pub use config::{NetworkConfig, NetworkConfigError, NetworkConfigManager};

/// 网络模块错误类型
#[derive(Debug, thiserror::Error)]
pub enum NetworkError {
    #[error("配置错误: {0}")]
    ConfigError(#[from] NetworkConfigError),
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    #[error("网络错误: {0}")]
    NetworkError(String),
}

/// 网络模块结果类型
pub type NetworkResult<T> = Result<T, NetworkError>;

/// 初始化网络模块
pub async fn init_network() -> NetworkResult<()> {
    // TODO: 实现网络模块初始化逻辑
    Ok(())
}

/// 关闭网络模块
pub async fn shutdown_network() -> NetworkResult<()> {
    // TODO: 实现网络模块关闭逻辑
    Ok(())
} 