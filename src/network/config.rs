use serde::{Deserialize, Serialize};
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use thiserror::Error;

/// 网络配置错误类型
#[derive(Debug, Error)]
pub enum NetworkConfigError {
    #[error("无效的配置: {0}")]
    InvalidConfig(String),
    #[error("配置加载失败: {0}")]
    LoadError(String),
    #[error("配置保存失败: {0}")]
    SaveError(String),
    #[error("配置验证失败: {0}")]
    ValidationError(String),
}

/// 网络配置数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// 本地监听地址
    pub listen_addr: SocketAddr,
    /// 虚拟网络接口名称
    pub interface_name: String,
    /// 虚拟网络CIDR
    pub virtual_network: String,
    /// MTU大小
    pub mtu: u16,
    /// 是否启用NAT穿透
    pub enable_nat: bool,
    /// 是否启用压缩
    pub enable_compression: bool,
    /// 是否启用加密
    pub enable_encryption: bool,
    /// 连接超时时间（秒）
    pub connection_timeout: u64,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            listen_addr: SocketAddr::new(IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0)), 655),
            interface_name: "tinc0".to_string(),
            virtual_network: "10.0.0.0/24".to_string(),
            mtu: 1500,
            enable_nat: true,
            enable_compression: true,
            enable_encryption: true,
            connection_timeout: 30,
            heartbeat_interval: 5,
        }
    }
}

/// 网络配置管理器
#[derive(Debug)]
pub struct NetworkConfigManager {
    config: NetworkConfig,
    config_path: String,
}

impl NetworkConfigManager {
    /// 创建新的网络配置管理器
    pub fn new(config_path: Option<String>) -> Self {
        Self {
            config: NetworkConfig::default(),
            config_path: config_path.unwrap_or_else(|| "config/network.json".to_string()),
        }
    }

    /// 加载网络配置
    pub async fn load_config(&mut self) -> Result<(), NetworkConfigError> {
        match tokio::fs::read_to_string(&self.config_path).await {
            Ok(content) => {
                match serde_json::from_str(&content) {
                    Ok(config) => {
                        self.config = config;
                        Ok(())
                    }
                    Err(e) => Err(NetworkConfigError::LoadError(e.to_string())),
                }
            }
            Err(e) => Err(NetworkConfigError::LoadError(e.to_string())),
        }
    }

    /// 保存网络配置
    pub async fn save_config(&self) -> Result<(), NetworkConfigError> {
        match serde_json::to_string_pretty(&self.config) {
            Ok(content) => {
                match tokio::fs::write(&self.config_path, content).await {
                    Ok(_) => Ok(()),
                    Err(e) => Err(NetworkConfigError::SaveError(e.to_string())),
                }
            }
            Err(e) => Err(NetworkConfigError::SaveError(e.to_string())),
        }
    }

    /// 验证网络配置
    pub fn validate_config(&self) -> Result<(), NetworkConfigError> {
        // 验证MTU
        if self.config.mtu < 1280 || self.config.mtu > 9000 {
            return Err(NetworkConfigError::ValidationError(
                "MTU必须在1280-9000之间".to_string(),
            ));
        }

        // 验证虚拟网络CIDR
        if !self.config.virtual_network.contains('/') {
            return Err(NetworkConfigError::ValidationError(
                "虚拟网络必须是有效的CIDR格式".to_string(),
            ));
        }

        // 验证超时时间
        if self.config.connection_timeout < 5 || self.config.connection_timeout > 300 {
            return Err(NetworkConfigError::ValidationError(
                "连接超时时间必须在5-300秒之间".to_string(),
            ));
        }

        // 验证心跳间隔
        if self.config.heartbeat_interval < 1 || self.config.heartbeat_interval > 60 {
            return Err(NetworkConfigError::ValidationError(
                "心跳间隔必须在1-60秒之间".to_string(),
            ));
        }

        Ok(())
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &NetworkConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, new_config: NetworkConfig) -> Result<(), NetworkConfigError> {
        self.config = new_config;
        self.validate_config()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_network_config_manager() {
        // 创建临时配置文件
        let temp_file = NamedTempFile::new().unwrap();
        let config_path = temp_file.path().to_str().unwrap().to_string();

        // 创建配置管理器
        let mut manager = NetworkConfigManager::new(Some(config_path.clone()));

        // 测试默认配置
        let default_config = manager.get_config();
        assert_eq!(default_config.mtu, 1500);
        assert_eq!(default_config.enable_nat, true);

        // 测试配置更新
        let mut new_config = NetworkConfig::default();
        new_config.mtu = 2000;
        assert!(manager.update_config(new_config).is_ok());

        // 测试配置保存和加载
        assert!(manager.save_config().await.is_ok());
        assert!(manager.load_config().await.is_ok());
        assert_eq!(manager.get_config().mtu, 2000);

        // 测试配置验证
        let mut invalid_config = NetworkConfig::default();
        invalid_config.mtu = 1000; // 无效的MTU
        assert!(manager.update_config(invalid_config).is_err());
    }
} 