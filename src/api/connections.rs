use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error};
use uuid::Uuid;
use std::sync::Arc;

use crate::{
    db::Database,
    api::{success_response, error_response, ApiState},
    models::{Connection, ApiResponse, PaginationParams, PaginatedResponse},
    db::DbError,
};

/// 连接服务
pub struct ConnectionService {
    db: Arc<Database>,
}

impl ConnectionService {
    /// 创建新的连接服务
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    /// 创建连接
    pub async fn create_connection(
        &self,
        source_id: Uuid,
        target_id: Uuid,
        connection_type: &str,
        status: &str,
    ) -> Result<Connection, crate::db::DbError> {
        let dao = ConnectionDao::new(self.db.clone());
        dao.create(source_id, target_id, connection_type, status).await
    }

    /// 获取连接列表
    pub async fn list_connections(&self) -> Result<Vec<Connection>, crate::db::DbError> {
        let dao = ConnectionDao::new(self.db.clone());
        dao.list().await
    }

    /// 获取单个连接
    pub async fn get_connection(&self, id: Uuid) -> Result<Connection, crate::db::DbError> {
        let dao = ConnectionDao::new(self.db.clone());
        dao.get(id).await
    }

    /// 更新连接状态
    pub async fn update_connection_status(&self, id: Uuid, status: &str) -> Result<Connection, crate::db::DbError> {
        let dao = ConnectionDao::new(self.db.clone());
        dao.update_status(id, status).await
    }

    /// 删除连接
    pub async fn delete_connection(&self, id: Uuid) -> Result<(), crate::db::DbError> {
        let dao = ConnectionDao::new(self.db.clone());
        dao.delete(id).await
    }
}

/// 创建连接请求
#[derive(Debug, Deserialize)]
pub struct CreateConnectionRequest {
    pub source_id: Uuid,
    pub target_id: Uuid,
    pub connection_type: String,
    pub status: String,
}

/// 更新连接状态请求
#[derive(Debug, Deserialize)]
pub struct UpdateConnectionStatusRequest {
    pub status: String,
}

/// 创建连接处理函数
pub async fn create_connection(
    State(service): State<Arc<ConnectionService>>,
    Json(req): Json<CreateConnectionRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let connection = service
        .create_connection(
            req.source_id,
            req.target_id,
            &req.connection_type,
            &req.status,
        )
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::CREATED, Json(connection)))
}

/// 获取连接列表处理函数
pub async fn list_connections(
    State(service): State<Arc<ConnectionService>>,
) -> Result<impl IntoResponse, StatusCode> {
    let connections = service
        .list_connections()
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(connections)))
}

/// 获取单个连接处理函数
pub async fn get_connection(
    State(service): State<Arc<ConnectionService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    let connection = service
        .get_connection(id)
        .await
        .map_err(|_| StatusCode::NOT_FOUND)?;

    Ok((StatusCode::OK, Json(connection)))
}

/// 更新连接状态处理函数
pub async fn update_connection_status(
    State(service): State<Arc<ConnectionService>>,
    Path(id): Path<Uuid>,
    Json(req): Json<UpdateConnectionStatusRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let connection = service
        .update_connection_status(id, &req.status)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(connection)))
}

/// 删除连接处理函数
pub async fn delete_connection(
    State(service): State<Arc<ConnectionService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    service
        .delete_connection(id)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(StatusCode::NO_CONTENT)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AppConfig;
    use std::net::SocketAddr;

    #[tokio::test]
    async fn test_connection_operations() {
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        
        let local_addr = "127.0.0.1:0".parse::<SocketAddr>().unwrap();
        let remote_addr = "127.0.0.1:8080".parse::<SocketAddr>().unwrap();
        let connection_tester = ConnectionTester::new(local_addr, remote_addr);
        
        let connection_service = ConnectionService::new(Arc::new(db));

        // 测试连接列表
        let connections = connection_service.list_connections().await.unwrap();
        assert_eq!(connections.len(), 0);

        // 测试连接测试
        let connection_id = Uuid::new_v4();
        let test_result = connection_service.test_connection(connection_id).await;
        assert!(test_result.is_ok());

        // 测试断开连接
        let terminate_result = connection_service.terminate_connection(connection_id).await;
        assert!(terminate_result.is_ok());
    }
} 