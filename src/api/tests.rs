use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
use tower::ServiceExt;
use uuid::Uuid;
use chrono::Utc;

use crate::{
    api::{ApiState, success_response, error_response},
    models::{User, Node, Connection, Config, ApiResponse, PaginationParams},
    db::Database,
    config::{AppConfig, ConfigManager},
};

/// 测试辅助函数：创建测试用户
pub async fn create_test_user(db: &Database) -> User {
    let user = User {
        id: Uuid::new_v4(),
        username: format!("test_user_{}", Uuid::new_v4()),
        password_hash: "test_hash".to_string(),
        email: format!("test_{}@example.com", Uuid::new_v4()),
        role: "user".to_string(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    sqlx::query!(
        r#"
        INSERT INTO users (id, username, password_hash, email, role, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        "#,
        user.id,
        user.username,
        user.password_hash,
        user.email,
        user.role,
        user.created_at,
        user.updated_at
    )
    .execute(db.pool())
    .await
    .unwrap();

    user
}

/// 测试辅助函数：创建测试节点
pub async fn create_test_node(db: &Database) -> Node {
    let node = Node {
        id: Uuid::new_v4(),
        name: format!("test_node_{}", Uuid::new_v4()),
        ip_address: "127.0.0.1".to_string(),
        port: 655,
        public_key: "test_key".to_string(),
        status: "online".to_string(),
        last_seen: Some(Utc::now()),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    sqlx::query!(
        r#"
        INSERT INTO nodes (id, name, ip_address, port, public_key, status, last_seen, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        "#,
        node.id,
        node.name,
        node.ip_address,
        node.port,
        node.public_key,
        node.status,
        node.last_seen,
        node.created_at,
        node.updated_at
    )
    .execute(db.pool())
    .await
    .unwrap();

    node
}

/// 测试辅助函数：创建测试连接
pub async fn create_test_connection(db: &Database, client_id: Uuid, server_id: Uuid) -> Connection {
    let connection = Connection {
        id: Uuid::new_v4(),
        client_id,
        server_id,
        method_used: "tcp".to_string(),
        latency: Some(100),
        status: "connected".to_string(),
        updated_at: Utc::now(),
    };

    sqlx::query!(
        r#"
        INSERT INTO connections (id, client_id, server_id, method_used, latency, status, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        "#,
        connection.id,
        connection.client_id,
        connection.server_id,
        connection.method_used,
        connection.latency,
        connection.status,
        connection.updated_at
    )
    .execute(db.pool())
    .await
    .unwrap();

    connection
}

/// 测试辅助函数：创建测试配置
pub async fn create_test_config(db: &Database, client_id: Uuid) -> Config {
    let config = Config {
        id: Uuid::new_v4(),
        client_id,
        version: 1,
        hash: "test_hash".to_string(),
        created_at: Utc::now(),
        content: r#"
            [network]
            name = "test"
            port = 655
            mode = "switch"
        "#.to_string(),
    };

    sqlx::query!(
        r#"
        INSERT INTO configs (id, client_id, version, hash, created_at, content)
        VALUES ($1, $2, $3, $4, $5, $6)
        "#,
        config.id,
        config.client_id,
        config.version,
        config.hash,
        config.created_at,
        config.content
    )
    .execute(db.pool())
    .await
    .unwrap();

    config
}

/// 测试辅助函数：创建测试应用状态
pub async fn create_test_app_state() -> ApiState {
    let config = AppConfig::default();
    let db = Database::new(config.database_url()).await.unwrap();
    let config_manager = ConfigManager::new("/tmp/tinc-configs");
    config_manager.init().await.unwrap();

    ApiState {
        db: Arc::new(db),
        config_manager: Arc::new(config_manager),
        user_service: Arc::new(crate::api::users::UserService::new(Arc::clone(&db))),
        node_service: Arc::new(crate::api::nodes::NodeService::new(Arc::clone(&db))),
        connection_service: Arc::new(crate::api::connections::ConnectionService::new(Arc::clone(&db))),
        config_service: Arc::new(crate::api::configs::ConfigService::new(Arc::clone(&db), Arc::clone(&config_manager))),
    }
}

/// 测试辅助函数：创建测试路由
pub async fn create_test_router() -> Router {
    let state = create_test_app_state().await;
    crate::api::create_router(state)
}

/// 测试辅助函数：发送测试请求
pub async fn send_test_request(
    router: Router,
    method: &str,
    path: &str,
    body: Option<Body>,
) -> (StatusCode, String) {
    let request = Request::builder()
        .method(method)
        .uri(path)
        .body(body.unwrap_or(Body::empty()))
        .unwrap();

    let response = router.oneshot(request).await.unwrap();
    let status = response.status();
    let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
    let body = String::from_utf8(body.to_vec()).unwrap();

    (status, body)
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[tokio::test]
    async fn test_user_operations() {
        let router = create_test_router().await;
        let db = Database::new(AppConfig::default().database_url()).await.unwrap();

        // 创建测试用户
        let user = create_test_user(&db).await;

        // 测试获取用户
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            &format!("/api/users/{}", user.id),
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<User> = serde_json::from_str(&body).unwrap();
        assert_eq!(response.data.id, user.id);

        // 测试用户列表
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            "/api/users",
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<Vec<User>> = serde_json::from_str(&body).unwrap();
        assert!(!response.data.is_empty());
    }

    #[tokio::test]
    async fn test_node_operations() {
        let router = create_test_router().await;
        let db = Database::new(AppConfig::default().database_url()).await.unwrap();

        // 创建测试节点
        let node = create_test_node(&db).await;

        // 测试获取节点
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            &format!("/api/nodes/{}", node.id),
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<Node> = serde_json::from_str(&body).unwrap();
        assert_eq!(response.data.id, node.id);

        // 测试节点列表
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            "/api/nodes",
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<Vec<Node>> = serde_json::from_str(&body).unwrap();
        assert!(!response.data.is_empty());
    }

    #[tokio::test]
    async fn test_connection_operations() {
        let router = create_test_router().await;
        let db = Database::new(AppConfig::default().database_url()).await.unwrap();

        // 创建测试用户和节点
        let user = create_test_user(&db).await;
        let node = create_test_node(&db).await;

        // 创建测试连接
        let connection = create_test_connection(&db, user.id, node.id).await;

        // 测试获取连接
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            &format!("/api/connections/{}", connection.id),
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<Connection> = serde_json::from_str(&body).unwrap();
        assert_eq!(response.data.id, connection.id);

        // 测试连接列表
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            "/api/connections",
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<Vec<Connection>> = serde_json::from_str(&body).unwrap();
        assert!(!response.data.is_empty());
    }

    #[tokio::test]
    async fn test_config_operations() {
        let router = create_test_router().await;
        let db = Database::new(AppConfig::default().database_url()).await.unwrap();

        // 创建测试用户
        let user = create_test_user(&db).await;

        // 创建测试配置
        let config = create_test_config(&db, user.id).await;

        // 测试获取配置
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            &format!("/api/configs/{}", user.id),
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<Config> = serde_json::from_str(&body).unwrap();
        assert_eq!(response.data.id, config.id);

        // 测试配置历史
        let (status, body) = send_test_request(
            router.clone(),
            "GET",
            &format!("/api/configs/{}/history", user.id),
            None,
        )
        .await;

        assert_eq!(status, StatusCode::OK);
        let response: ApiResponse<Vec<Config>> = serde_json::from_str(&body).unwrap();
        assert!(!response.data.is_empty());
    }
} 