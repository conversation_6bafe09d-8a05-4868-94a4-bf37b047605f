use axum::{
    extract::{Path, Query, State, FromRequestParts},
    http::{StatusCode, request::Parts},
    response::IntoResponse,
    Json,
    async_trait,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error};
use uuid::Uuid;
use std::sync::Arc;
use jsonwebtoken::{encode, decode, Head<PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Validation, errors::Error as JwtError, errors::ErrorKind};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Utc, Duration};

use crate::{
    db::Database,
    models::{User, ApiResponse, PaginationParams, PaginatedResponse, Claims},
    auth::{AuthService, AuthError, UserInfo, LoginRequest, LoginResponse},
};

/// 创建用户请求
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: Option<String>,
}

/// 更新用户请求
#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    pub password: Option<String>,
    pub role: Option<String>,
}

/// 设置用户角色请求
#[derive(Debug, Deserialize)]
pub struct SetRoleRequest {
    pub role: String,
}



/// 认证中间件
#[derive(Debug, Clone)]
pub struct Auth {
    pub user_id: Uuid,
    pub role: String,
}

/// 用户服务
pub struct UserService {
    db: Arc<Database>,
    auth: AuthService,
}

impl UserService {
    /// 创建新的用户服务
    pub fn new(db: Arc<Database>) -> Self {
        Self {
            db,
            auth: AuthService::new(),
        }
    }

    /// 用户登录
    pub async fn login(&self, username: &str, password: &str) -> Result<LoginResponse, AuthError> {
        // 根据用户名查找用户
        let user = self.db.get_user_by_username(username).await
            .map_err(|_| AuthError::UserNotFound)?;

        // 验证密码
        if !self.auth.verify_password(password, &user.password)? {
            return Err(AuthError::InvalidPassword);
        }

        // 生成 JWT Token
        let token = self.auth.generate_token(&user)?;
        let expires_at = (Utc::now() + Duration::hours(24)).timestamp();

        // 更新最后登录时间
        let _ = self.db.update_user_last_login(user.id).await;

        Ok(LoginResponse {
            token,
            user: UserInfo::from(user),
            expires_at,
        })
    }

    /// 创建用户
    pub async fn create_user(&self, req: &CreateUserRequest) -> Result<User, AuthError> {
        // 哈希密码
        let password_hash = self.auth.hash_password(&req.password)?;

        // 创建用户
        let user = User {
            id: Uuid::new_v4(),
            username: req.username.clone(),
            email: req.email.clone(),
            password: password_hash,
            role: req.role.clone().unwrap_or_else(|| "user".to_string()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_login: None,
        };

        self.db.create_user(&user).await
            .map_err(|_| AuthError::UserNotFound)?;

        Ok(user)
    }

    /// 获取用户列表
    pub async fn list_users(&self, params: &PaginationParams) -> Result<PaginatedResponse<User>, AuthError> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        let users = self.db.list_users(page_size, offset).await
            .map_err(|_| AuthError::UserNotFound)?;

        let total = self.db.count_users().await
            .map_err(|_| AuthError::UserNotFound)?;

        Ok(PaginatedResponse {
            total,
            page,
            page_size,
            data: users,
        })
    }

    /// 获取单个用户
    pub async fn get_user(&self, id: Uuid) -> Result<User, AuthError> {
        self.db.get_user(id).await
            .map_err(|_| AuthError::UserNotFound)
    }

    /// 更新用户
    pub async fn update_user(&self, id: Uuid, req: &UpdateUserRequest) -> Result<User, AuthError> {
        let mut user = self.get_user(id).await?;

        if let Some(username) = &req.username {
            user.username = username.clone();
        }
        if let Some(email) = &req.email {
            user.email = email.clone();
        }
        if let Some(password) = &req.password {
            user.password = self.auth.hash_password(password)?;
        }
        if let Some(role) = &req.role {
            user.role = role.clone();
        }
        user.updated_at = Utc::now();

        self.db.update_user(&user).await
            .map_err(|_| AuthError::UserNotFound)?;

        Ok(user)
    }

    /// 删除用户
    pub async fn delete_user(&self, id: Uuid) -> Result<(), AuthError> {
        self.db.delete_user(id).await
            .map_err(|_| AuthError::UserNotFound)
    }

    /// 验证 Token
    pub fn verify_token(&self, token: &str) -> Result<Claims, AuthError> {
        self.auth.verify_token(token)
    }

    /// 获取用户权限
    pub async fn get_user_permissions(&self, user_id: Uuid) -> Result<Vec<String>, AuthError> {
        let user = self.get_user(user_id).await?;
        // 简单的权限映射
        let permissions = match user.role.as_str() {
            "admin" => vec!["read".to_string(), "write".to_string(), "delete".to_string()],
            "user" => vec!["read".to_string()],
            _ => vec![],
        };
        Ok(permissions)
    }
}

/// 用户登录
pub async fn login(
    State(service): State<Arc<UserService>>,
    Json(req): Json<LoginRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    match service.login(&req.username, &req.password).await {
        Ok(response) => Ok((StatusCode::OK, Json(ApiResponse {
            code: 0,
            message: "登录成功".to_string(),
            data: Some(response),
        }))),
        Err(AuthError::UserNotFound) => Ok((StatusCode::UNAUTHORIZED, Json(ApiResponse {
            code: 401,
            message: "用户不存在".to_string(),
            data: None::<()>,
        }))),
        Err(AuthError::InvalidPassword) => Ok((StatusCode::UNAUTHORIZED, Json(ApiResponse {
            code: 401,
            message: "密码错误".to_string(),
            data: None::<()>,
        }))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 创建用户
pub async fn create_user(
    State(service): State<Arc<UserService>>,
    Json(req): Json<CreateUserRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    match service.create_user(&req).await {
        Ok(user) => Ok((StatusCode::CREATED, Json(ApiResponse {
            code: 0,
            message: "用户创建成功".to_string(),
            data: Some(user),
        }))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 获取用户列表
pub async fn list_users(
    State(service): State<Arc<UserService>>,
    Query(params): Query<PaginationParams>,
) -> Result<impl IntoResponse, StatusCode> {
    match service.list_users(&params).await {
        Ok(users) => Ok((StatusCode::OK, Json(ApiResponse {
            code: 0,
            message: "获取用户列表成功".to_string(),
            data: Some(users),
        }))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 获取单个用户
pub async fn get_user(
    State(service): State<Arc<UserService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    match service.get_user(id).await {
        Ok(user) => Ok((StatusCode::OK, Json(ApiResponse {
            code: 0,
            message: "获取用户成功".to_string(),
            data: Some(user),
        }))),
        Err(AuthError::UserNotFound) => Ok((StatusCode::NOT_FOUND, Json(ApiResponse {
            code: 404,
            message: "用户不存在".to_string(),
            data: None::<()>,
        }))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 更新用户
pub async fn update_user(
    State(service): State<Arc<UserService>>,
    Path(id): Path<Uuid>,
    Json(req): Json<UpdateUserRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    match service.update_user(id, &req).await {
        Ok(user) => Ok((StatusCode::OK, Json(ApiResponse {
            code: 0,
            message: "用户更新成功".to_string(),
            data: Some(user),
        }))),
        Err(AuthError::UserNotFound) => Ok((StatusCode::NOT_FOUND, Json(ApiResponse {
            code: 404,
            message: "用户不存在".to_string(),
            data: None::<()>,
        }))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 删除用户
pub async fn delete_user(
    State(service): State<Arc<UserService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    match service.delete_user(id).await {
        Ok(_) => Ok((StatusCode::OK, Json(ApiResponse {
            code: 0,
            message: "用户删除成功".to_string(),
            data: None::<()>,
        }))),
        Err(AuthError::UserNotFound) => Ok((StatusCode::NOT_FOUND, Json(ApiResponse {
            code: 404,
            message: "用户不存在".to_string(),
            data: None::<()>,
        }))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 设置用户角色
pub async fn set_user_role(
    State(service): State<Arc<UserService>>,
    Path(id): Path<Uuid>,
    Json(payload): Json<SetRoleRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    // TODO: 实现角色设置逻辑
    Err(StatusCode::NOT_IMPLEMENTED)
}

/// 获取角色列表
pub async fn list_roles() -> Result<impl IntoResponse, StatusCode> {
    // TODO: 实现角色列表获取逻辑
    let roles = vec!["admin", "user"];
    Ok((StatusCode::OK, Json(ApiResponse {
        code: 0,
        message: "获取角色列表成功".to_string(),
        data: Some(roles),
    })))
}

impl From<CreateUserRequest> for User {
    fn from(req: CreateUserRequest) -> Self {
        User {
            id: Uuid::new_v4(),
            username: req.username,
            password: req.password,
            role: req.role.unwrap_or_else(|| "user".to_string()),
            email: String::new(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login: None,
        }
    }
}

impl From<UpdateUserRequest> for User {
    fn from(req: UpdateUserRequest) -> Self {
        User {
            id: Uuid::new_v4(),
            username: req.username.unwrap_or_default(),
            password: req.password.unwrap_or_default(),
            role: req.role.unwrap_or_default(),
            email: String::new(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login: None,
        }
    }
}

impl Auth {
    pub async fn from_token(token: &str, user_service: &UserService) -> Result<Self, JwtError> {
        let claims = user_service.verify_token(token)?;
        Ok(Self {
            user_id: Uuid::parse_str(&claims.sub).map_err(|_| JwtError::from(ErrorKind::InvalidToken))?,
            role: claims.role,
        })
    }
}

#[async_trait]
impl<S> FromRequestParts<S> for Auth
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, Json<ApiResponse<()>>);

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<Auth>()
            .cloned()
            .ok_or_else(|| {
                (
                    StatusCode::UNAUTHORIZED,
                    Json(ApiResponse {
                        code: 401,
                        message: "未认证".to_string(),
                        data: None,
                    }),
                )
            })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AppConfig;

    #[tokio::test]
    async fn test_user_operations() {
        let config = AppConfig::default();
        let db = Database::new(&config.database_url()).await.unwrap();
        let user_service = UserService::new(Arc::new(db));

        // 测试创建用户
        let create_req = CreateUserRequest {
            username: "test_user".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            role: Some("user".to_string()),
        };

        let user = user_service.create_user(&create_req).await.unwrap();
        assert_eq!(user.username, "test_user");
        assert_eq!(user.role, "user");

        // 测试获取用户
        let fetched_user = user_service.get_user(user.id).await.unwrap();
        assert_eq!(fetched_user.id, user.id);

        // 测试更新用户
        let update_req = UpdateUserRequest {
            username: Some("new_user".to_string()),
            email: None,
            password: None,
            role: None,
        };

        let updated_user = user_service.update_user(user.id, &update_req).await.unwrap();
        assert_eq!(updated_user.username, "new_user");

        // 测试删除用户
        user_service.delete_user(user.id).await.unwrap();
    }
}