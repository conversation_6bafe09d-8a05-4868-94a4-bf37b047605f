use axum::{
    extract::{Path, Query, State, FromRequestParts},
    http::{StatusCode, request::Parts},
    response::IntoResponse,
    Json,
    async_trait,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error};
use uuid::Uuid;
use std::sync::Arc;
use jsonwebtoken::{encode, decode, <PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Validation, errors::Error as JwtError, errors::ErrorKind};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Utc, Duration};

use crate::{
    db::Database,
    models::{User, ApiResponse, PaginationParams, PaginatedResponse, Claims},
    auth::{AuthService, AuthError, UserInfo},
};

/// 创建用户请求
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: Option<String>,
}

/// 更新用户请求
#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    pub password: Option<String>,
    pub role: Option<String>,
}

/// 设置用户角色请求
#[derive(Debug, Deserialize)]
pub struct SetRoleRequest {
    pub role: String,
}

/// 登录请求
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// 登录响应
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserInfo,
    pub expires_at: i64,
}

/// 认证中间件
#[derive(Debug, Clone)]
pub struct Auth {
    pub user_id: Uuid,
    pub role: String,
}

/// 用户服务
pub struct UserService {
    db: Arc<Database>,
}

impl UserService {
    /// 创建新的用户服务
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    /// 创建用户
    pub async fn create_user(&self, username: &str, password: &str, role: &str) -> Result<User, crate::db::DbError> {
        let dao = UserDao::new(self.db.clone());
        let password_hash = bcrypt::hash(password, bcrypt::DEFAULT_COST)?;
        dao.create(username, &password_hash, role).await
    }

    /// 获取用户列表
    pub async fn list_users(&self) -> Result<Vec<User>, crate::db::DbError> {
        let dao = UserDao::new(self.db.clone());
        dao.list().await
    }

    /// 获取单个用户
    pub async fn get_user(&self, id: Uuid) -> Result<User, crate::db::DbError> {
        let dao = UserDao::new(self.db.clone());
        dao.get(id).await
    }

    /// 更新用户角色
    pub async fn update_user_role(&self, id: Uuid, role: &str) -> Result<User, crate::db::DbError> {
        let dao = UserDao::new(self.db.clone());
        dao.update_role(id, role).await
    }

    /// 删除用户
    pub async fn delete_user(&self, id: Uuid) -> Result<(), crate::db::DbError> {
        let dao = UserDao::new(self.db.clone());
        dao.delete(id).await
    }
}

/// 创建用户
pub async fn create_user(
    State(service): State<Arc<UserService>>,
    Json(req): Json<CreateUserRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let user = service
        .create_user(&req.username, &req.password, &req.role)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::CREATED, Json(user)))
}

/// 获取用户列表
pub async fn list_users(
    State(service): State<Arc<UserService>>,
) -> Result<impl IntoResponse, StatusCode> {
    let users = service
        .list_users()
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(users)))
}

/// 获取单个用户
pub async fn get_user(
    State(service): State<Arc<UserService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    let user = service
        .get_user(id)
        .await
        .map_err(|_| StatusCode::NOT_FOUND)?;

    Ok((StatusCode::OK, Json(user)))
}

/// 更新用户角色
pub async fn update_user_role(
    State(service): State<Arc<UserService>>,
    Path(id): Path<Uuid>,
    Json(req): Json<UpdateUserRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let user = service
        .update_user_role(id, &req.role)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(user)))
}

/// 删除用户
pub async fn delete_user(
    State(service): State<Arc<UserService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    service
        .delete_user(id)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(StatusCode::NO_CONTENT)
}

/// 设置用户角色
pub async fn set_role(
    State(state): State<ApiState>,
    Path(id): Path<Uuid>,
    Json(payload): Json<SetRoleRequest>,
) -> impl IntoResponse {
    // TODO: 实现角色设置逻辑
    Err(StatusCode::NOT_IMPLEMENTED)
}

/// 获取角色列表
pub async fn list_roles(
    State(state): State<ApiState>,
) -> impl IntoResponse {
    // TODO: 实现角色列表获取逻辑
    Err(StatusCode::NOT_IMPLEMENTED)
}

/// 用户登录
pub async fn login(
    State(state): State<ApiState>,
    Json(payload): Json<LoginRequest>,
) -> impl IntoResponse {
    // TODO: 实现登录逻辑
    Err(StatusCode::NOT_IMPLEMENTED)
}

/// 获取当前用户信息
pub async fn get_profile(
    State(state): State<ApiState>,
    auth: Auth,
) -> impl IntoResponse {
    // TODO: 实现获取当前用户信息逻辑
    Err(StatusCode::NOT_IMPLEMENTED)
}

impl From<CreateUserRequest> for User {
    fn from(req: CreateUserRequest) -> Self {
        User {
            id: Uuid::new_v4(),
            username: req.username,
            password: req.password,
            role: req.role,
            email: String::new(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login: None,
        }
    }
}

impl From<UpdateUserRequest> for User {
    fn from(req: UpdateUserRequest) -> Self {
        User {
            id: Uuid::new_v4(),
            username: req.username.unwrap_or_default(),
            password: req.password.unwrap_or_default(),
            role: req.role.unwrap_or_default(),
            email: String::new(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login: None,
        }
    }
}

impl Auth {
    pub async fn from_token(token: &str, user_service: &UserService) -> Result<Self, JwtError> {
        let claims = user_service.verify_token(token)?;
        Ok(Self {
            user_id: Uuid::parse_str(&claims.sub).map_err(|_| JwtError::from(ErrorKind::InvalidToken))?,
            role: claims.role,
        })
    }
}

#[async_trait]
impl<S> FromRequestParts<S> for Auth
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, Json<ApiResponse<()>>);

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<Auth>()
            .cloned()
            .ok_or_else(|| {
                (
                    StatusCode::UNAUTHORIZED,
                    Json(ApiResponse {
                        code: 401,
                        message: "未认证".to_string(),
                        data: None,
                    }),
                )
            })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AppConfig;

    #[tokio::test]
    async fn test_user_operations() {
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let user_service = UserService::new(Arc::new(db));

        // 测试创建用户
        let create_req = CreateUserRequest {
            username: "test_user".to_string(),
            password: "password123".to_string(),
            role: "user".to_string(),
        };

        let user = user_service.create_user(&create_req.username, &create_req.password, &create_req.role).await.unwrap();
        assert_eq!(user.username, "test_user");
        assert_eq!(user.role, "user");

        // 测试获取用户
        let fetched_user = user_service.get_user(user.id).await.unwrap();
        assert_eq!(fetched_user.id, user.id);

        // 测试更新用户
        let update_req = UpdateUserRequest {
            username: Some("new_user".to_string()),
            password: None,
            role: None,
        };

        let updated_user = user_service.update_user_role(user.id, &update_req.role).await.unwrap();
        assert_eq!(updated_user.username, "new_user");

        // 测试删除用户
        user_service.delete_user(user.id).await.unwrap();
    }
} 