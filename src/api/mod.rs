pub mod users;
pub mod nodes;
pub mod connections;
pub mod configs;

use axum::{
    routing::{get, post, put, delete},
    Router,
    extract::{Path, State, Json, Query, FromRequestParts},
    response::IntoResponse,
    http::{StatusCode, Request, header::AUTHORIZATION},
    middleware::{self, Next},
    async_trait,
    body::Body,
    response::Response,
};
use serde::{Deserialize, Serialize};
use tracing::info;
use uuid::Uuid;
use std::sync::Arc;
use tower::ServiceExt;
use tower_http::trace::TraceLayer;

use crate::{
    models::{User, Node, Connection, Config, Policy, Log, ApiResponse, PaginationParams, PaginatedResponse, Claims},
    db::Database,
    config::ConfigManager,
    log::LogManager,
    api::users::{UserService, Auth},
    api::nodes::NodeService,
    api::connections::ConnectionService,
    api::configs::ConfigService,
};

/// API 状态
#[derive(Clone)]
pub struct ApiState {
    pub user_service: Arc<UserService>,
    pub node_service: Arc<NodeService>,
    pub connection_service: Arc<ConnectionService>,
    pub config_service: Arc<ConfigService>,
}

/// 认证中间件
pub async fn auth_middleware(
    request: Request<Body>,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_header = request
        .headers()
        .get("Authorization")
        .and_then(|v| v.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    if !auth_header.starts_with("Bearer ") {
        return Err(StatusCode::UNAUTHORIZED);
    }

    let token = &auth_header[7..];
    // TODO: 验证 token
    if token != "test-token" {
        return Err(StatusCode::UNAUTHORIZED);
    }

    Ok(next.run(request).await)
}

/// 创建 API 路由
pub fn create_router(db: Arc<Database>) -> Router {
    // 创建服务实例
    let user_service = Arc::new(UserService::new(db.clone()));
    let node_service = Arc::new(NodeService::new(db.clone()));
    let connection_service = Arc::new(ConnectionService::new(db.clone()));
    let config_service = Arc::new(ConfigService::new(db.clone()));

    // 用户管理路由
    let user_routes = Router::new()
        .route("/users", post(create_user))
        .route("/users", get(list_users))
        .route("/users/:id", get(get_user))
        .route("/users/:id/role", put(update_user_role))
        .route("/users/:id", delete(delete_user))
        .with_state(user_service);

    // 节点管理路由
    let node_routes = Router::new()
        .route("/nodes", post(create_node))
        .route("/nodes", get(list_nodes))
        .route("/nodes/:id", get(get_node))
        .route("/nodes/:id/status", put(update_node_status))
        .route("/nodes/:id/nat-type", put(update_node_nat_type))
        .route("/nodes/:id", delete(delete_node))
        .with_state(node_service);

    // 连接管理路由
    let connection_routes = Router::new()
        .route("/connections", post(create_connection))
        .route("/connections", get(list_connections))
        .route("/connections/:id", get(get_connection))
        .route("/connections/:id/status", put(update_connection_status))
        .route("/connections/:id", delete(delete_connection))
        .with_state(connection_service);

    // 配置管理路由
    let config_routes = Router::new()
        .route("/configs", post(create_config))
        .route("/configs", get(list_configs))
        .route("/configs/:id", get(get_config))
        .route("/configs/:id", put(update_config))
        .route("/configs/:id", delete(delete_config))
        .with_state(config_service);

    // 合并所有路由
    Router::new()
        .merge(user_routes)
        .merge(node_routes)
        .merge(connection_routes)
        .merge(config_routes)
        .layer(TraceLayer::new_for_http())
}

// 鉴权处理器
async fn login(
    State(state): State<ApiState>,
    Json(payload): Json<LoginRequest>,
) -> impl IntoResponse {
    // TODO: 实现登录逻辑
    StatusCode::NOT_IMPLEMENTED
}

async fn get_profile(
    State(state): State<ApiState>,
) -> impl IntoResponse {
    // TODO: 实现获取用户信息逻辑
    StatusCode::NOT_IMPLEMENTED
}

// 节点处理器
async fn list_nodes(
    State(state): State<ApiState>,
    Query(params): Query<PaginationParams>,
) -> impl IntoResponse {
    // TODO: 实现节点列表逻辑
    StatusCode::NOT_IMPLEMENTED
}

async fn create_node(
    State(state): State<ApiState>,
    Json(payload): Json<CreateNodeRequest>,
) -> impl IntoResponse {
    // TODO: 实现创建节点逻辑
    StatusCode::NOT_IMPLEMENTED
}

// 连接处理器
async fn list_connections(
    State(state): State<ApiState>,
    Query(params): Query<PaginationParams>,
) -> impl IntoResponse {
    // TODO: 实现连接列表逻辑
    StatusCode::NOT_IMPLEMENTED
}

async fn test_connection(
    State(state): State<ApiState>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    // TODO: 实现连接测试逻辑
    StatusCode::NOT_IMPLEMENTED
}

// 请求/响应结构
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateNodeRequest {
    pub name: String,
    pub node_type: String,
    pub ip: String,
    pub mac: String,
    pub tunnel_type: String,
}

// 辅助函数
fn success_response<T>(data: T) -> ApiResponse<T> {
    ApiResponse {
        code: 0,
        message: "success".to_string(),
        data: Some(data),
    }
}

fn error_response(code: i32, message: &str) -> ApiResponse<()> {
    ApiResponse {
        code,
        message: message.to_string(),
        data: None,
    }
}

pub use nodes::{create_node, list_nodes, get_node, update_node, delete_node, detect_nat};
pub use users::{list_users, create_user, get_user, update_user, delete_user, set_user_role, list_roles};
pub use connections::{list_connections, get_connection, test_connection, terminate_connection};
pub use configs::{get_config, push_config, get_config_history, rollback_config, pack_config, unpack_config}; 