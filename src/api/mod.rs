pub mod users;
pub mod nodes;
pub mod connections;
pub mod configs;

use axum::{
    routing::{get, post, put, delete},
    Router,
    extract::{Path, State, Json, Query, FromRequestParts},
    response::IntoResponse,
    http::{StatusCode, Request, header::AUTHORIZATION},
    middleware::{self, Next},
    async_trait,
    body::Body,
    response::Response,
};
use serde::{Deserialize, Serialize};
use tracing::info;
use uuid::Uuid;
use std::sync::Arc;
use tower::ServiceExt;
use tower_http::trace::TraceLayer;

use crate::{
    models::{User, Node, Connection, Config, Policy, Log, ApiResponse, PaginationParams, PaginatedResponse, Claims},
    db::Database,
    auth::AuthService,
    api::users::{UserService, Auth},
    api::nodes::NodeService,
    api::connections::ConnectionService,
    api::configs::ConfigService,
};

/// API 状态
#[derive(Clone)]
pub struct ApiState {
    pub user_service: Arc<UserService>,
    pub node_service: Arc<NodeService>,
    pub connection_service: Arc<ConnectionService>,
    pub config_service: Arc<ConfigService>,
}

/// 认证中间件
pub async fn auth_middleware(
    request: Request<Body>,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_header = request
        .headers()
        .get("Authorization")
        .and_then(|v| v.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    if !auth_header.starts_with("Bearer ") {
        return Err(StatusCode::UNAUTHORIZED);
    }

    let token = &auth_header[7..];
    // TODO: 验证 token
    if token != "test-token" {
        return Err(StatusCode::UNAUTHORIZED);
    }

    Ok(next.run(request).await)
}

/// 创建 API 路由
pub fn create_router(db: Arc<Database>) -> Router {
    // 创建服务实例
    let user_service = Arc::new(UserService::new(db.clone()));
    let node_service = Arc::new(NodeService::new(db.clone()));
    let connection_service = Arc::new(ConnectionService::new(db.clone()));
    let config_service = Arc::new(ConfigService::new(db.clone()));

    // 认证路由（无需认证）
    let auth_routes = Router::new()
        .route("/login", post(users::login))
        .route("/health", get(health_check))
        .with_state(user_service.clone());

    // 用户管理路由
    let user_routes = Router::new()
        .route("/profile/me", get(users::get_profile))
        .route("/users", get(users::list_users))
        .route("/users", post(users::create_user))
        .route("/users/:id", get(users::get_user))
        .route("/users/:id", put(users::update_user))
        .route("/users/:id", delete(users::delete_user))
        .route("/roles", get(users::list_roles))
        .with_state(user_service.clone());

    // 节点管理路由
    let node_routes = Router::new()
        .route("/nodes", get(nodes::list_nodes))
        .route("/nodes", post(nodes::create_node))
        .route("/nodes/:id", get(nodes::get_node))
        .route("/nodes/:id", put(nodes::update_node))
        .route("/nodes/:id", delete(nodes::delete_node))
        .with_state(node_service);

    // 连接管理路由
    let connection_routes = Router::new()
        .route("/connections", get(connections::list_connections))
        .route("/connections/:id", get(connections::get_connection))
        .route("/connections/:id/test", post(connections::test_connection))
        .route("/connections/:id/terminate", post(connections::terminate_connection))
        .with_state(connection_service);

    // NAT 探测路由
    let nat_routes = Router::new()
        .route("/nat/:client_id", get(nodes::get_nat_info))
        .route("/nat/:client_id/detect", post(nodes::detect_nat))
        .with_state(user_service.clone());

    // 配置管理路由
    let config_routes = Router::new()
        .route("/configs/:client_id", get(configs::get_config))
        .route("/configs/:client_id/push", post(configs::push_config))
        .route("/configs/:client_id/history", get(configs::get_config_history))
        .route("/configs/:client_id/rollback", post(configs::rollback_config))
        .with_state(config_service);

    // 监控日志路由
    let monitoring_routes = Router::new()
        .route("/logs", get(get_logs))
        .route("/events", get(get_events))
        .route("/metrics", get(get_metrics))
        .with_state(user_service.clone());

    // 合并所有路由
    Router::new()
        .nest("/api", auth_routes)
        .nest("/api", user_routes.layer(middleware::from_fn(auth_middleware)))
        .nest("/api", node_routes.layer(middleware::from_fn(auth_middleware)))
        .nest("/api", connection_routes.layer(middleware::from_fn(auth_middleware)))
        .nest("/api", nat_routes.layer(middleware::from_fn(auth_middleware)))
        .nest("/api", config_routes.layer(middleware::from_fn(auth_middleware)))
        .nest("/api", monitoring_routes.layer(middleware::from_fn(auth_middleware)))
        .layer(TraceLayer::new_for_http())
        .layer(
            CorsLayer::new()
                .allow_origin(Any)
                .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
                .allow_headers([AUTHORIZATION, CONTENT_TYPE])
        )
}

/// 健康检查
async fn health_check() -> impl IntoResponse {
    (StatusCode::OK, Json(ApiResponse {
        code: 0,
        message: "服务正常".to_string(),
        data: Some("OK"),
    }))
}

/// 获取系统日志
async fn get_logs(
    State(service): State<Arc<UserService>>,
    Query(params): Query<PaginationParams>,
) -> impl IntoResponse {
    // TODO: 实现日志查询逻辑
    (StatusCode::OK, Json(ApiResponse {
        code: 0,
        message: "获取日志成功".to_string(),
        data: Some(Vec::<String>::new()),
    }))
}

/// 获取事件流
async fn get_events(
    State(service): State<Arc<UserService>>,
) -> impl IntoResponse {
    // TODO: 实现事件流逻辑
    (StatusCode::OK, Json(ApiResponse {
        code: 0,
        message: "获取事件成功".to_string(),
        data: Some(Vec::<String>::new()),
    }))
}

/// 获取 Prometheus 指标
async fn get_metrics(
    State(service): State<Arc<UserService>>,
) -> impl IntoResponse {
    // TODO: 实现 Prometheus 指标逻辑
    (StatusCode::OK, "# HELP tinc_vpn_connections_total Total number of VPN connections\n# TYPE tinc_vpn_connections_total counter\ntinc_vpn_connections_total 0\n")
}

// 请求/响应结构
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateNodeRequest {
    pub name: String,
    pub node_type: String,
    pub ip: String,
    pub mac: String,
    pub tunnel_type: String,
}

// 辅助函数
fn success_response<T>(data: T) -> ApiResponse<T> {
    ApiResponse {
        code: 0,
        message: "success".to_string(),
        data: Some(data),
    }
}

fn error_response(code: i32, message: &str) -> ApiResponse<()> {
    ApiResponse {
        code,
        message: message.to_string(),
        data: None,
    }
}

pub use nodes::{create_node, list_nodes, get_node, update_node, delete_node, detect_nat};
pub use users::{list_users, create_user, get_user, update_user, delete_user, set_user_role, list_roles};
pub use connections::{list_connections, get_connection, test_connection, terminate_connection};
pub use configs::{get_config, push_config, get_config_history, rollback_config, pack_config, unpack_config}; 