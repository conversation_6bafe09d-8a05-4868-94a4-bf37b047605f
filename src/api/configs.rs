use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use flate2::{Compress, Decompress, Status, Compression};
use serde::{Deserialize, Serialize};
use tracing::{info, error};
use uuid::Uuid;
use std::io::{<PERSON><PERSON><PERSON>, <PERSON>};
use std::sync::Arc;
use chrono::Utc;
use sha2::{Sha256, Digest};

use crate::{
    db::{Database, Config, ConfigDao},
    api::{success_response, error_response, ApiState},
    db::DbError,
    config::ConfigError,
    models::{Config as ApiConfig, PaginationParams, PaginatedResponse},
};

/// 配置服务
pub struct ConfigService {
    db: Arc<Database>,
}

impl ConfigService {
    /// 创建新的配置服务
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    /// 创建配置
    pub async fn create_config(
        &self,
        name: &str,
        value: &str,
        description: &str,
    ) -> Result<Config, crate::db::DbError> {
        let dao = ConfigDao::new(self.db.clone());
        dao.create(name, value, description).await
    }

    /// 获取配置列表
    pub async fn list_configs(&self) -> Result<Vec<Config>, crate::db::DbError> {
        let dao = ConfigDao::new(self.db.clone());
        dao.list().await
    }

    /// 获取单个配置
    pub async fn get_config(&self, id: Uuid) -> Result<Config, crate::db::DbError> {
        let dao = ConfigDao::new(self.db.clone());
        dao.get(id).await
    }

    /// 更新配置
    pub async fn update_config(
        &self,
        id: Uuid,
        value: &str,
        description: &str,
    ) -> Result<Config, crate::db::DbError> {
        let dao = ConfigDao::new(self.db.clone());
        dao.update(id, value, description).await
    }

    /// 删除配置
    pub async fn delete_config(&self, id: Uuid) -> Result<(), crate::db::DbError> {
        let dao = ConfigDao::new(self.db.clone());
        dao.delete(id).await
    }

    /// 推送配置
    pub async fn push_config(&self, client_id: Uuid, content: String) -> Result<Config, DbError> {
        // 计算配置内容的哈希值
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let hash = format!("{:x}", hasher.finalize());

        // 获取当前版本号
        let version = sqlx::query!(
            r#"
            SELECT COALESCE(MAX(version), 0) + 1 as version
            FROM configs
            WHERE client_id = $1
            "#,
            client_id
        )
        .fetch_one(self.db.pool())
        .await?
        .version
        .unwrap_or(1);

        // 创建新配置
        let config = sqlx::query_as!(
            Config,
            r#"
            INSERT INTO configs (id, client_id, version, hash, created_at, content)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, client_id, version, hash, created_at, content
            "#,
            Uuid::new_v4(),
            client_id,
            version,
            hash,
            Utc::now(),
            content
        )
        .fetch_one(self.db.pool())
        .await?;

        // 保存配置到文件系统
        self.config_manager.save_config().await?;

        Ok(config)
    }

    /// 获取配置历史
    pub async fn get_config_history(
        &self,
        client_id: Uuid,
        params: PaginationParams,
    ) -> Result<PaginatedResponse<Config>, DbError> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        let total = sqlx::query!(
            r#"
            SELECT COUNT(*) as count
            FROM configs
            WHERE client_id = $1
            "#,
            client_id
        )
        .fetch_one(self.db.pool())
        .await?
        .count
        .unwrap_or(0);

        let configs = sqlx::query_as!(
            Config,
            r#"
            SELECT id, client_id, version, hash, created_at, content
            FROM configs
            WHERE client_id = $1
            ORDER BY version DESC
            LIMIT $2 OFFSET $3
            "#,
            client_id,
            page_size,
            offset
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(PaginatedResponse {
            total,
            page,
            page_size,
            data: configs,
        })
    }

    /// 回滚配置
    pub async fn rollback_config(&self, client_id: Uuid, version: i32) -> Result<Config, DbError> {
        // 获取指定版本的配置
        let config = sqlx::query_as!(
            Config,
            r#"
            SELECT id, client_id, version, hash, created_at, content
            FROM configs
            WHERE client_id = $1 AND version = $2
            "#,
            client_id,
            version
        )
        .fetch_one(self.db.pool())
        .await?;

        // 创建新版本
        let new_version = sqlx::query!(
            r#"
            SELECT COALESCE(MAX(version), 0) + 1 as version
            FROM configs
            WHERE client_id = $1
            "#,
            client_id
        )
        .fetch_one(self.db.pool())
        .await?
        .version
        .unwrap_or(1);

        // 创建新配置
        let new_config = sqlx::query_as!(
            Config,
            r#"
            INSERT INTO configs (id, client_id, version, hash, created_at, content)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, client_id, version, hash, created_at, content
            "#,
            Uuid::new_v4(),
            client_id,
            new_version,
            config.hash,
            Utc::now(),
            config.content
        )
        .fetch_one(self.db.pool())
        .await?;

        // 保存配置到文件系统
        self.config_manager.save_config().await?;

        Ok(new_config)
    }

    /// 打包配置
    pub fn pack_config(&self, config: &Config) -> Result<Vec<u8>, ConfigError> {
        let mut compress = Compress::new(Compression::default(), false);
        let mut out = vec![0; 1024];
        let mut compressed = Vec::new();

        // 压缩配置内容
        let status = compress.compress(
            config.content.as_bytes(),
            &mut out,
            flate2::FlushCompress::Finish,
        )?;

        if status == Status::Ok || status == Status::StreamEnd {
            compressed.extend_from_slice(&out[..status as usize]);
        }

        Ok(compressed)
    }

    /// 解包配置
    pub fn unpack_config(&self, data: &[u8]) -> Result<String, ConfigError> {
        let mut decompress = Decompress::new(false);
        let mut out = vec![0; 1024];
        let mut decompressed = Vec::new();

        // 解压配置内容
        let status = decompress.decompress(
            data,
            &mut out,
            flate2::FlushDecompress::Finish,
        )?;

        if status == Status::Ok || status == Status::StreamEnd {
            decompressed.extend_from_slice(&out[..status as usize]);
        }

        Ok(String::from_utf8(decompressed)?)
    }
}

/// 创建配置请求
#[derive(Debug, Deserialize)]
pub struct CreateConfigRequest {
    pub name: String,
    pub value: String,
    pub description: String,
}

/// 更新配置请求
#[derive(Debug, Deserialize)]
pub struct UpdateConfigRequest {
    pub value: String,
    pub description: String,
}

/// 创建配置处理函数
pub async fn create_config(
    State(service): State<Arc<ConfigService>>,
    Json(req): Json<CreateConfigRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let config = service
        .create_config(&req.name, &req.value, &req.description)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::CREATED, Json(config)))
}

/// 获取配置列表处理函数
pub async fn list_configs(
    State(service): State<Arc<ConfigService>>,
) -> Result<impl IntoResponse, StatusCode> {
    let configs = service
        .list_configs()
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(configs)))
}

/// 获取单个配置处理函数
pub async fn get_config(
    State(service): State<Arc<ConfigService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    let config = service
        .get_config(id)
        .await
        .map_err(|_| StatusCode::NOT_FOUND)?;

    Ok((StatusCode::OK, Json(config)))
}

/// 更新配置处理函数
pub async fn update_config(
    State(service): State<Arc<ConfigService>>,
    Path(id): Path<Uuid>,
    Json(req): Json<UpdateConfigRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let config = service
        .update_config(id, &req.value, &req.description)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(config)))
}

/// 删除配置处理函数
pub async fn delete_config(
    State(service): State<Arc<ConfigService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    service
        .delete_config(id)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(StatusCode::NO_CONTENT)
}

/// 推送配置
pub async fn push_config(
    State(state): State<ApiState>,
    Path(client_id): Path<Uuid>,
    Json(payload): Json<PushConfigRequest>,
) -> impl IntoResponse {
    match Arc::clone(&state.config_service).push_config(client_id, payload.content).await {
        Ok(config) => (StatusCode::OK, Json(success_response(config))).into_response(),
        Err(e) => (StatusCode::BAD_REQUEST, Json(error_response(400, &e.to_string()))).into_response(),
    }
}

/// 获取配置历史
pub async fn get_config_history(
    State(state): State<ApiState>,
    Path(client_id): Path<Uuid>,
    Query(params): Query<PaginationParams>,
) -> impl IntoResponse {
    match Arc::clone(&state.config_service).get_config_history(client_id, params).await {
        Ok(history) => (StatusCode::OK, Json(success_response(history))).into_response(),
        Err(e) => (StatusCode::BAD_REQUEST, Json(error_response(400, &e.to_string()))).into_response(),
    }
}

/// 回滚配置
pub async fn rollback_config(
    State(state): State<ApiState>,
    Path(client_id): Path<Uuid>,
    Json(payload): Json<RollbackConfigRequest>,
) -> impl IntoResponse {
    match Arc::clone(&state.config_service).rollback_config(client_id, payload.version).await {
        Ok(config) => (StatusCode::OK, Json(success_response(config))).into_response(),
        Err(e) => (StatusCode::BAD_REQUEST, Json(error_response(400, &e.to_string()))).into_response(),
    }
}

/// 打包配置
pub async fn pack_config(
    State(state): State<ApiState>,
    Path(client_id): Path<Uuid>,
) -> impl IntoResponse {
    match Arc::clone(&state.config_service).get_config(client_id).await {
        Ok(config) => {
            match Arc::clone(&state.config_service).pack_config(&config) {
                Ok(data) => (StatusCode::OK, Json(success_response(data))).into_response(),
                Err(e) => (StatusCode::BAD_REQUEST, Json(error_response(400, &e.to_string()))).into_response(),
            }
        }
        Err(e) => (StatusCode::BAD_REQUEST, Json(error_response(400, &e.to_string()))).into_response(),
    }
}

/// 解包配置
pub async fn unpack_config(
    State(state): State<ApiState>,
    Path(client_id): Path<Uuid>,
    Json(payload): Json<UnpackConfigRequest>,
) -> impl IntoResponse {
    match Arc::clone(&state.config_service).unpack_config(&payload.data) {
        Ok(content) => (StatusCode::OK, Json(success_response(content))).into_response(),
        Err(e) => (StatusCode::BAD_REQUEST, Json(error_response(400, &e.to_string()))).into_response(),
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PushConfigRequest {
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RollbackConfigRequest {
    pub version: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UnpackConfigRequest {
    pub data: Vec<u8>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AppConfig;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_config_operations() {
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let config_service = ConfigService::new(Arc::new(db));

        // 测试配置打包和解包
        let test_config = Config {
            id: Uuid::new_v4(),
            client_id: Uuid::new_v4(),
            version: 1,
            hash: "test".to_string(),
            created_at: chrono::Utc::now(),
            content: r#"
                [network]
                name = "test"
                port = 655
                mode = "switch"
            "#.to_string(),
        };

        let packed = config_service.pack_config(&test_config).unwrap();
        let unpacked = config_service.unpack_config(&packed).unwrap();
        assert_eq!(unpacked.trim(), test_config.content.trim());

        // 测试配置推送
        let client_id = Uuid::new_v4();
        let content = r#"
            [network]
            name = "test"
            port = 655
            mode = "switch"
        "#.to_string();

        let config = config_service.push_config(client_id, content.clone()).await.unwrap();
        assert_eq!(config.content.trim(), content.trim());

        // 测试配置历史
        let history = config_service.get_config_history(client_id, PaginationParams {
            page: Some(1),
            page_size: Some(10),
        }).await.unwrap();
        assert_eq!(history.data.len(), 1);

        // 测试配置回滚
        let rolled_back = config_service.rollback_config(client_id, 1).await.unwrap();
        assert_eq!(rolled_back.content.trim(), content.trim());
    }
} 