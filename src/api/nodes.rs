use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error};
use uuid::Uuid;
use std::sync::Arc;
use chrono::Utc;

use crate::{
    db::Database,
    api::{success_response, error_response, ApiState},
    models::{Node, ApiResponse, PaginationParams, PaginatedResponse},
    db::DbError,
};

/// 创建节点请求
#[derive(Debug, Deserialize)]
pub struct CreateNodeRequest {
    pub name: String,
    pub public_ip: String,
    pub public_port: i32,
    pub private_ip: String,
    pub private_port: i32,
    pub nat_type: String,
}

/// 更新节点请求
#[derive(Debug, Deserialize)]
pub struct UpdateNodeRequest {
    pub name: Option<String>,
    pub address: Option<String>,
    pub port: Option<u16>,
    pub node_type: Option<String>,
    pub ip: Option<String>,
    pub mac: Option<String>,
    pub tunnel_type: Option<String>,
    pub status: Option<String>,
}

/// 节点服务
pub struct NodeService {
    db: Arc<Database>,
}

impl NodeService {
    /// 创建新的节点服务
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    /// 创建节点
    pub async fn create_node(
        &self,
        name: &str,
        public_ip: &str,
        public_port: i32,
        private_ip: &str,
        private_port: i32,
        nat_type: &str,
    ) -> Result<Node, crate::db::DbError> {
        let dao = NodeDao::new(self.db.clone());
        dao.create(name, public_ip, public_port, private_ip, private_port, nat_type).await
    }

    /// 获取节点列表
    pub async fn list_nodes(&self) -> Result<Vec<Node>, crate::db::DbError> {
        let dao = NodeDao::new(self.db.clone());
        dao.list().await
    }

    /// 获取单个节点
    pub async fn get_node(&self, id: Uuid) -> Result<Node, crate::db::DbError> {
        let dao = NodeDao::new(self.db.clone());
        dao.get(id).await
    }

    /// 更新节点状态
    pub async fn update_node_status(&self, id: Uuid, status: &str) -> Result<Node, crate::db::DbError> {
        let dao = NodeDao::new(self.db.clone());
        dao.update_status(id, status).await
    }

    /// 更新节点 NAT 类型
    pub async fn update_node_nat_type(&self, id: Uuid, nat_type: &str) -> Result<Node, crate::db::DbError> {
        let dao = NodeDao::new(self.db.clone());
        dao.update_nat_type(id, nat_type).await
    }

    /// 删除节点
    pub async fn delete_node(&self, id: Uuid) -> Result<(), crate::db::DbError> {
        let dao = NodeDao::new(self.db.clone());
        dao.delete(id).await
    }
}

/// 更新节点状态请求
#[derive(Debug, Deserialize)]
pub struct UpdateNodeStatusRequest {
    pub status: String,
}

/// 更新节点 NAT 类型请求
#[derive(Debug, Deserialize)]
pub struct UpdateNodeNatTypeRequest {
    pub nat_type: String,
}

/// 创建节点处理器
pub async fn create_node(
    State(service): State<Arc<NodeService>>,
    Json(req): Json<CreateNodeRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let node = service
        .create_node(
            &req.name,
            &req.public_ip,
            req.public_port,
            &req.private_ip,
            req.private_port,
            &req.nat_type,
        )
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::CREATED, Json(node)))
}

/// 获取节点列表处理器
pub async fn list_nodes(
    State(service): State<Arc<NodeService>>,
) -> Result<impl IntoResponse, StatusCode> {
    let nodes = service
        .list_nodes()
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(nodes)))
}

/// 获取节点信息处理器
pub async fn get_node(
    State(service): State<Arc<NodeService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    let node = service
        .get_node(id)
        .await
        .map_err(|_| StatusCode::NOT_FOUND)?;

    Ok((StatusCode::OK, Json(node)))
}

/// 更新节点状态处理器
pub async fn update_node_status(
    State(service): State<Arc<NodeService>>,
    Path(id): Path<Uuid>,
    Json(req): Json<UpdateNodeStatusRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let node = service
        .update_node_status(id, &req.status)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(node)))
}

/// 更新节点 NAT 类型处理器
pub async fn update_node_nat_type(
    State(service): State<Arc<NodeService>>,
    Path(id): Path<Uuid>,
    Json(req): Json<UpdateNodeNatTypeRequest>,
) -> Result<impl IntoResponse, StatusCode> {
    let node = service
        .update_node_nat_type(id, &req.nat_type)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok((StatusCode::OK, Json(node)))
}

/// 删除节点处理器
pub async fn delete_node(
    State(service): State<Arc<NodeService>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse, StatusCode> {
    service
        .delete_node(id)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(StatusCode::NO_CONTENT)
}

impl From<CreateNodeRequest> for Node {
    fn from(req: CreateNodeRequest) -> Self {
        Node {
            id: Uuid::new_v4(),
            name: req.name,
            address: req.public_ip,
            port: req.public_port as u16,
            node_type: req.nat_type,
            ip: req.private_ip,
            mac: "00:11:22:33:44:55".to_string(),
            status: "offline".to_string(),
            tunnel_type: "tcp".to_string(),
            last_active: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}

impl From<UpdateNodeRequest> for Node {
    fn from(req: UpdateNodeRequest) -> Self {
        Node {
            id: Uuid::new_v4(),
            name: req.name.unwrap_or_default(),
            address: req.address.unwrap_or_default(),
            port: req.port.unwrap_or_default() as u16,
            node_type: req.node_type.unwrap_or_default(),
            ip: req.ip.unwrap_or_default(),
            mac: req.mac.unwrap_or_default(),
            status: req.status.unwrap_or_default(),
            tunnel_type: req.tunnel_type.unwrap_or_default(),
            last_active: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AppConfig;

    #[tokio::test]
    async fn test_node_operations() {
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let node_service = NodeService::new(Arc::new(db));

        // 测试创建节点
        let create_req = CreateNodeRequest {
            name: "test_node".to_string(),
            public_ip: "*************".to_string(),
            public_port: 8080,
            private_ip: "*************".to_string(),
            private_port: 8080,
            nat_type: "client".to_string(),
        };

        let node = node_service.create_node(
            &create_req.name,
            &create_req.public_ip,
            create_req.public_port,
            &create_req.private_ip,
            create_req.private_port,
            &create_req.nat_type,
        ).await.unwrap();
        assert_eq!(node.name, "test_node");
        assert_eq!(node.address, "*************");
        assert_eq!(node.port, 8080);

        // 测试获取节点
        let fetched_node = node_service.get_node(node.id).await.unwrap();
        assert_eq!(fetched_node.id, node.id);

        // 测试更新节点
        let update_req = UpdateNodeRequest {
            name: Some("new_name".to_string()),
            address: None,
            port: None,
            node_type: None,
            ip: None,
            mac: None,
            status: Some("online".to_string()),
            tunnel_type: None,
        };

        let updated_node = node_service.update_node_status(node.id, &update_req.status).await.unwrap();
        assert_eq!(updated_node.name, "new_name");
        assert_eq!(updated_node.status, "online");

        // 测试删除节点
        node_service.delete_node(node.id).await.unwrap();
    }
} 