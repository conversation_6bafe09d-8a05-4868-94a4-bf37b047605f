use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// 用户模型
#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    #[serde(skip_serializing)]
    pub password: String,
    pub role: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login: Option<DateTime<Utc>>,
}

/// 节点模型
#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Node {
    pub id: Uuid,
    pub name: String,
    pub address: String,
    pub port: u16,
    pub node_type: String, // "client" or "server"
    pub ip: String,
    pub mac: String,
    pub status: String,
    pub tunnel_type: String,
    pub last_active: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 连接模型
#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Connection {
    pub id: Uuid,
    pub client_id: Uuid,
    pub server_id: Uuid,
    pub method_used: String,
    pub latency: Option<i32>,
    pub status: String,
    pub updated_at: DateTime<Utc>,
}

/// 配置模型
#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Config {
    pub id: Uuid,
    pub client_id: Uuid,
    pub version: i32,
    pub hash: String,
    pub created_at: DateTime<Utc>,
    pub content: String,
}

/// 策略模型
#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Policy {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub bandwidth_limit: Option<i32>,
    pub allow_ips: String,
    pub allow_ports: String,
}

/// 日志/事件模型
#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Log {
    pub id: Uuid,
    pub log_type: String,
    pub message: String,
    pub source: String,
    pub created_at: DateTime<Utc>,
}

/// API 响应结构
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub message: String,
    pub data: Option<T>,
}

/// 分页请求参数
#[derive(Debug, Deserialize)]
pub struct PaginationParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
}

/// 分页响应结构
#[derive(Debug, Serialize)]
pub struct PaginatedResponse<T> {
    pub total: i64,
    pub page: i64,
    pub page_size: i64,
    pub data: Vec<T>,
}

/// JWT 声明
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,  // user id
    pub exp: usize,   // expiration time
    pub iat: usize,   // issued at
    pub role: String, // user role
} 