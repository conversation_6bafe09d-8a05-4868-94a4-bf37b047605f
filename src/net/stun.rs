use std::net::{SocketAddr, IpAddr};
use std::time::Duration;
use rand::Rng;
use sha2::{Sha256, Digest};
use tracing::{info, error, warn};

/// STUN 消息类型
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum StunMessageType {
    /// 绑定请求
    BindingRequest = 0x0001,
    /// 绑定响应
    BindingResponse = 0x0101,
    /// 绑定错误响应
    BindingErrorResponse = 0x0111,
}

/// STUN 属性类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum StunAttributeType {
    /// MAPPED-ADDRESS
    MappedAddress = 0x0001,
    /// XOR-MAPPED-ADDRESS
    XorMappedAddress = 0x0020,
    /// USERNAME
    Username = 0x0006,
    /// MESSAGE-INTEGRITY
    MessageIntegrity = 0x0008,
    /// ERROR-CODE
    ErrorCode = 0x0009,
    /// UNKNOWN-ATTRIBUTES
    UnknownAttributes = 0x000A,
    /// REALM
    Realm = 0x0014,
    /// NONCE
    Nonce = 0x0015,
    /// SOFTWARE
    Software = 0x8022,
    /// ALTERNATE-SERVER
    AlternateServer = 0x8023,
    /// FINGERPRINT
    Fingerprint = 0x8028,
}

/// STUN 消息
#[derive(Debug, Clone)]
pub struct StunMessage {
    /// 消息类型
    pub message_type: StunMessageType,
    /// 消息长度
    pub message_length: u16,
    /// 魔术 Cookie
    pub magic_cookie: u32,
    /// 事务 ID
    pub transaction_id: [u8; 12],
    /// 属性列表
    pub attributes: Vec<StunAttribute>,
}

/// STUN 属性
#[derive(Debug, Clone)]
pub struct StunAttribute {
    /// 属性类型
    pub attribute_type: StunAttributeType,
    /// 属性值
    pub value: Vec<u8>,
}

/// STUN 客户端
pub struct StunClient {
    /// 用户名
    username: String,
    /// 密码
    password: String,
    /// 软件信息
    software: String,
}

impl StunClient {
    /// 创建新的 STUN 客户端
    pub fn new(username: String, password: String, software: String) -> Self {
        Self {
            username,
            password,
            software,
        }
    }

    /// 创建绑定请求
    pub fn create_binding_request(&self) -> StunMessage {
        let mut rng = rand::thread_rng();
        let transaction_id = rng.gen();

        StunMessage {
            message_type: StunMessageType::BindingRequest,
            message_length: 0,
            magic_cookie: 0x2112A442,
            transaction_id,
            attributes: vec![
                StunAttribute {
                    attribute_type: StunAttributeType::Software,
                    value: self.software.as_bytes().to_vec(),
                },
            ],
        }
    }

    /// 解析 STUN 响应
    pub fn parse_response(&self, data: &[u8]) -> Result<StunMessage, StunError> {
        if data.len() < 20 {
            return Err(StunError::InvalidMessage);
        }

        let message_type = match u16::from_be_bytes([data[0], data[1]]) {
            0x0101 => StunMessageType::BindingResponse,
            0x0111 => StunMessageType::BindingErrorResponse,
            _ => return Err(StunError::InvalidMessageType),
        };

        let message_length = u16::from_be_bytes([data[2], data[3]]);
        let magic_cookie = u32::from_be_bytes([data[4], data[5], data[6], data[7]]);
        let transaction_id = data[8..20].try_into().unwrap();

        let mut attributes = Vec::new();
        let mut offset = 20;

        while offset + 4 <= data.len() {
            let attribute_type = u16::from_be_bytes([data[offset], data[offset + 1]]);
            let attribute_length = u16::from_be_bytes([data[offset + 2], data[offset + 3]]);
            offset += 4;

            if offset + attribute_length as usize > data.len() {
                return Err(StunError::InvalidMessage);
            }

            let value = data[offset..offset + attribute_length as usize].to_vec();
            offset += attribute_length as usize;

            // 对齐到 4 字节边界
            offset = (offset + 3) & !3;

            attributes.push(StunAttribute {
                attribute_type: match attribute_type {
                    0x0001 => StunAttributeType::MappedAddress,
                    0x0020 => StunAttributeType::XorMappedAddress,
                    0x0006 => StunAttributeType::Username,
                    0x0008 => StunAttributeType::MessageIntegrity,
                    0x0009 => StunAttributeType::ErrorCode,
                    0x000A => StunAttributeType::UnknownAttributes,
                    0x0014 => StunAttributeType::Realm,
                    0x0015 => StunAttributeType::Nonce,
                    0x8022 => StunAttributeType::Software,
                    0x8023 => StunAttributeType::AlternateServer,
                    0x8028 => StunAttributeType::Fingerprint,
                    _ => continue,
                },
                value,
            });
        }

        Ok(StunMessage {
            message_type,
            message_length,
            magic_cookie,
            transaction_id,
            attributes,
        })
    }

    /// 获取映射地址
    pub fn get_mapped_address(&self, message: &StunMessage) -> Result<SocketAddr, StunError> {
        for attribute in &message.attributes {
            match attribute.attribute_type {
                StunAttributeType::MappedAddress => {
                    return self.parse_address(&attribute.value);
                }
                StunAttributeType::XorMappedAddress => {
                    return self.parse_xor_address(&attribute.value, message.magic_cookie, &message.transaction_id);
                }
                _ => continue,
            }
        }

        Err(StunError::NoMappedAddress)
    }

    /// 解析地址
    fn parse_address(&self, data: &[u8]) -> Result<SocketAddr, StunError> {
        if data.len() < 8 {
            return Err(StunError::InvalidAddress);
        }

        let family = data[1];
        let port = u16::from_be_bytes([data[2], data[3]]);

        let ip = match family {
            1 => {
                if data.len() < 8 {
                    return Err(StunError::InvalidAddress);
                }
                IpAddr::V4(std::net::Ipv4Addr::new(data[4], data[5], data[6], data[7]))
            }
            2 => {
                if data.len() < 20 {
                    return Err(StunError::InvalidAddress);
                }
                let mut ipv6 = [0u8; 16];
                ipv6.copy_from_slice(&data[4..20]);
                IpAddr::V6(std::net::Ipv6Addr::from(ipv6))
            }
            _ => return Err(StunError::InvalidAddress),
        };

        Ok(SocketAddr::new(ip, port))
    }

    /// 解析 XOR 地址
    fn parse_xor_address(&self, data: &[u8], magic_cookie: u32, transaction_id: &[u8]) -> Result<SocketAddr, StunError> {
        if data.len() < 8 {
            return Err(StunError::InvalidAddress);
        }

        let family = data[1];
        let port = u16::from_be_bytes([data[2], data[3]]) ^ (magic_cookie >> 16) as u16;

        let ip = match family {
            1 => {
                if data.len() < 8 {
                    return Err(StunError::InvalidAddress);
                }
                let mut ipv4 = [0u8; 4];
                for i in 0..4 {
                    ipv4[i] = data[4 + i] ^ ((magic_cookie >> (24 - i * 8)) & 0xFF) as u8;
                }
                IpAddr::V4(std::net::Ipv4Addr::from(ipv4))
            }
            2 => {
                if data.len() < 20 {
                    return Err(StunError::InvalidAddress);
                }
                let mut ipv6 = [0u8; 16];
                for i in 0..4 {
                    ipv6[i] = data[4 + i] ^ ((magic_cookie >> (24 - i * 8)) & 0xFF) as u8;
                }
                for i in 0..12 {
                    ipv6[4 + i] = data[8 + i] ^ transaction_id[i];
                }
                IpAddr::V6(std::net::Ipv6Addr::from(ipv6))
            }
            _ => return Err(StunError::InvalidAddress),
        };

        Ok(SocketAddr::new(ip, port))
    }

    /// 计算消息完整性
    pub fn calculate_message_integrity(&self, message: &StunMessage) -> Vec<u8> {
        let mut hasher = Sha256::new();
        hasher.update(self.password.as_bytes());
        let key = hasher.finalize();

        // 简化实现，实际应该使用 HMAC-SHA1
        key.to_vec()
    }

    /// 序列化消息
    fn serialize_message(&self, message: &StunMessage) -> Vec<u8> {
        let mut data = Vec::new();

        // 消息头
        data.extend_from_slice(&(message.message_type as u16).to_be_bytes());
        data.extend_from_slice(&message.message_length.to_be_bytes());
        data.extend_from_slice(&message.magic_cookie.to_be_bytes());
        data.extend_from_slice(&message.transaction_id);

        // 属性
        for attribute in &message.attributes {
            data.extend_from_slice(&(attribute.attribute_type as u16).to_be_bytes());
            data.extend_from_slice(&(attribute.value.len() as u16).to_be_bytes());
            data.extend_from_slice(&attribute.value);

            // 对齐到 4 字节边界
            let padding = (4 - (attribute.value.len() % 4)) % 4;
            data.extend_from_slice(&vec![0u8; padding]);
        }

        data
    }
}

/// STUN 错误
#[derive(Debug, thiserror::Error)]
pub enum StunError {
    #[error("无效的消息")]
    InvalidMessage,
    #[error("无效的消息类型")]
    InvalidMessageType,
    #[error("无效的地址")]
    InvalidAddress,
    #[error("没有映射地址")]
    NoMappedAddress,
    #[error("未知错误: {0}")]
    Unknown(String),
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};

    #[test]
    fn test_stun_message() {
        let client = StunClient::new(
            "test".to_string(),
            "test".to_string(),
            "test-client".to_string(),
        );

        // 测试创建绑定请求
        let request = client.create_binding_request();
        assert_eq!(request.message_type, StunMessageType::BindingRequest);
        assert_eq!(request.magic_cookie, 0x2112A442);

        // 测试序列化消息
        let data = client.serialize_message(&request);
        assert!(data.len() >= 20);

        // 测试解析响应（需要构造有效的响应数据）
        // let response = client.parse_response(&data).unwrap();
        // assert_eq!(response.message_type, StunMessageType::BindingResponse);
        // assert_eq!(response.magic_cookie, 0x2112A442);
    }

    #[test]
    fn test_address_parsing() {
        let client = StunClient::new(
            "test".to_string(),
            "test".to_string(),
            "test-client".to_string(),
        );

        // 测试 IPv4 地址解析
        let ipv4_data = vec![
            0x00, 0x01, // 保留, 地址族
            0x00, 0x50, // 端口
            0x01, 0x02, 0x03, 0x04, // IP 地址
        ];
        let addr = client.parse_address(&ipv4_data).unwrap();
        assert_eq!(addr.ip(), IpAddr::V4(Ipv4Addr::new(1, 2, 3, 4)));
        assert_eq!(addr.port(), 80);

        // 测试 XOR 地址解析
        let xor_data = vec![
            0x00, 0x01, // 保留, 地址族
            0x21, 0x12, // 端口 (XOR 后)
            0x21, 0x12, 0xA4, 0x42, // IP 地址 (XOR 后)
        ];
        let addr = client.parse_xor_address(&xor_data, 0x2112A442, &[0u8; 12]).unwrap();
        assert_eq!(addr.ip(), IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0)));
        assert_eq!(addr.port(), 0);
    }
} 