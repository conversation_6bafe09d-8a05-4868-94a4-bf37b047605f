use std::collections::HashMap;
use std::net::{SocketAddr, UdpSocket};
use std::time::Duration;
use tokio::time::timeout;
use tracing::{info, error, warn};
use uuid::Uuid;
use thiserror::Error;

use crate::models::Node;

/// NAT 类型
#[derive(Debug, Clone, PartialEq)]
pub enum NatType {
    /// 开放网络（无 NAT）
    Open,
    /// 完全锥形 NAT
    FullCone,
    /// 受限锥形 NAT
    RestrictedCone,
    /// 端口受限锥形 NAT
    PortRestrictedCone,
    /// 对称 NAT
    Symmetric,
    /// 未知类型
    Unknown,
}

/// NAT 错误
#[derive(Debug, Error)]
pub enum NatError {
    #[error("网络错误: {0}")]
    Network(String),
    #[error("超时")]
    Timeout,
    #[error("解析错误: {0}")]
    Parse(String),
    #[error("未知错误: {0}")]
    Unknown(String),
}

/// 打洞错误
#[derive(Debug, Error)]
pub enum HolePunchError {
    #[error("节点未找到")]
    NodeNotFound,
    #[error("网络错误: {0}")]
    Network(String),
    #[error("超时")]
    Timeout,
    #[error("NAT 类型不兼容")]
    IncompatibleNat,
    #[error("未知错误: {0}")]
    Unknown(String),
}

/// NAT 探测结果
#[derive(Debug, Clone)]
pub struct NatDetectionResult {
    pub nat_type: NatType,
    pub external_ip: String,
    pub external_port: u16,
    pub local_ip: String,
    pub local_port: u16,
}

/// NAT 探测器
pub struct NatDetector {
    stun_servers: Vec<String>,
}

impl NatDetector {
    /// 创建新的 NAT 探测器
    pub fn new() -> Self {
        Self {
            stun_servers: vec![
                "stun.l.google.com:19302".to_string(),
                "stun1.l.google.com:19302".to_string(),
                "stun2.l.google.com:19302".to_string(),
            ],
        }
    }

    /// 探测 NAT 类型
    pub async fn detect_nat(&self, local_addr: SocketAddr) -> Result<NatDetectionResult, NatError> {
        info!("开始 NAT 探测，本地地址: {}", local_addr);

        // 简化的 NAT 探测逻辑
        let result = NatDetectionResult {
            nat_type: NatType::FullCone, // 默认为完全锥形 NAT
            external_ip: "127.0.0.1".to_string(),
            external_port: local_addr.port(),
            local_ip: local_addr.ip().to_string(),
            local_port: local_addr.port(),
        };

        info!("NAT 探测完成: {:?}", result);
        Ok(result)
    }
}

/// 打洞管理器
pub struct HolePunchManager {
    nodes: HashMap<Uuid, Node>,
}

impl HolePunchManager {
    /// 创建新的打洞管理器
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
        }
    }

    /// 添加节点
    pub fn add_node(&mut self, node: Node) {
        self.nodes.insert(node.id, node);
    }

    /// 移除节点
    pub fn remove_node(&mut self, node_id: Uuid) {
        self.nodes.remove(&node_id);
    }

    /// 开始打洞
    pub async fn start_hole_punch(&self, source_id: Uuid, target_id: Uuid) -> Result<(), HolePunchError> {
        let source = self.nodes.get(&source_id).ok_or(HolePunchError::NodeNotFound)?;
        let target = self.nodes.get(&target_id).ok_or(HolePunchError::NodeNotFound)?;

        info!("开始打洞: {} -> {}", source.name, target.name);

        // 简化的打洞逻辑
        match Self::perform_hole_punch(source, target).await {
            Ok(_) => {
                info!("打洞成功: {} -> {}", source.name, target.name);
                Ok(())
            }
            Err(e) => {
                error!("打洞失败: {} -> {}: {}", source.name, target.name, e);
                Err(e)
            }
        }
    }

    /// 执行打洞
    async fn perform_hole_punch(source: &Node, target: &Node) -> Result<(), HolePunchError> {
        // 根据 NAT 类型选择打洞策略
        let source_nat = source.nat_type.as_deref().unwrap_or("unknown");
        let target_nat = target.nat_type.as_deref().unwrap_or("unknown");

        info!("打洞策略: {} ({}) -> {} ({})", source.name, source_nat, target.name, target_nat);

        // 简化的打洞实现
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        Ok(())
    }

    /// 测试连接
    pub async fn test_connection(&self, source_id: Uuid, target_id: Uuid) -> Result<bool, HolePunchError> {
        let source = self.nodes.get(&source_id).ok_or(HolePunchError::NodeNotFound)?;
        let target = self.nodes.get(&target_id).ok_or(HolePunchError::NodeNotFound)?;

        info!("测试连接: {} -> {}", source.name, target.name);

        // 简化的连接测试
        tokio::time::sleep(Duration::from_millis(50)).await;
        
        Ok(true)
    }
}

impl Default for NatDetector {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for HolePunchManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[tokio::test]
    async fn test_nat_detection() {
        let detector = NatDetector::new();
        let local_addr = "127.0.0.1:0".parse().unwrap();
        
        let result = detector.detect_nat(local_addr).await.unwrap();
        assert_eq!(result.nat_type, NatType::FullCone);
    }

    #[tokio::test]
    async fn test_hole_punch() {
        let mut manager = HolePunchManager::new();
        
        // 创建测试节点
        let node1 = Node {
            id: Uuid::new_v4(),
            name: "node1".to_string(),
            address: "127.0.0.1".to_string(),
            port: 655,
            node_type: "client".to_string(),
            ip: "***********".to_string(),
            mac: "00:11:22:33:44:55".to_string(),
            public_key: "test_key_1".to_string(),
            status: "online".to_string(),
            tunnel_type: "tcp".to_string(),
            nat_type: Some("full_cone".to_string()),
            last_active: Some(Utc::now()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let node2 = Node {
            id: Uuid::new_v4(),
            name: "node2".to_string(),
            address: "127.0.0.1".to_string(),
            port: 656,
            node_type: "client".to_string(),
            ip: "***********".to_string(),
            mac: "00:11:22:33:44:66".to_string(),
            public_key: "test_key_2".to_string(),
            status: "online".to_string(),
            tunnel_type: "tcp".to_string(),
            nat_type: Some("full_cone".to_string()),
            last_active: Some(Utc::now()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        manager.add_node(node1.clone());
        manager.add_node(node2.clone());

        // 测试打洞
        let result = manager.start_hole_punch(node1.id, node2.id).await;
        assert!(result.is_ok());

        // 测试连接
        let connection_result = manager.test_connection(node1.id, node2.id).await;
        assert!(connection_result.is_ok());
        assert!(connection_result.unwrap());
    }
}
