use std::net::{SocketAddr, UdpSocket, IpAddr};
use std::time::Duration;
use std::collections::HashMap;
use tokio::sync::mpsc;
use tokio::time::sleep;
use tracing::{info, error, warn};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

use crate::models::Node;

/// NAT 类型
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum NatType {
    /// 开放互联网
    Open,
    /// 全锥形 NAT
    FullCone,
    /// 受限锥形 NAT
    RestrictedCone,
    /// 端口受限锥形 NAT
    PortRestrictedCone,
    /// 对称型 NAT
    Symmetric,
    /// 未知类型
    Unknown,
}

impl NatType {
    /// 转换为字符串
    pub fn to_string(&self) -> &'static str {
        match self {
            NatType::Open => "open",
            NatType::FullCone => "full_cone",
            NatType::RestrictedCone => "restricted_cone",
            NatType::PortRestrictedCone => "port_restricted_cone",
            NatType::Symmetric => "symmetric",
            NatType::Unknown => "unknown",
        }
    }

    /// 从字符串解析
    pub fn from_str(s: &str) -> Self {
        match s {
            "open" => NatType::Open,
            "full_cone" => NatType::FullCone,
            "restricted_cone" => NatType::RestrictedCone,
            "port_restricted_cone" => NatType::PortRestrictedCone,
            "symmetric" => NatType::Symmetric,
            _ => NatType::Unknown,
        }
    }
}

/// NAT 检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NatDetectionResult {
    /// NAT 类型
    pub nat_type: NatType,
    /// 公网 IP
    pub public_ip: IpAddr,
    /// 公网端口
    pub public_port: u16,
    /// 私网 IP
    pub private_ip: IpAddr,
    /// 私网端口
    pub private_port: u16,
}

/// NAT 检测器
pub struct NatDetector {
    /// STUN 服务器地址
    stun_servers: Vec<SocketAddr>,
    /// 本地 UDP 套接字
    socket: UdpSocket,
    /// 超时时间
    timeout: Duration,
}

impl NatDetector {
    /// 创建新的 NAT 检测器
    pub fn new(stun_servers: Vec<SocketAddr>, timeout: Duration) -> Result<Self, std::io::Error> {
        let socket = UdpSocket::bind("0.0.0.0:0")?;
        socket.set_read_timeout(Some(timeout))?;

        Ok(Self {
            stun_servers,
            socket,
            timeout,
        })
    }

    /// 检测 NAT 类型
    pub async fn detect(&self) -> Result<NatDetectionResult, NatError> {
        let mut results = Vec::new();

        // 对每个 STUN 服务器进行测试
        for server in &self.stun_servers {
            match self.test_stun_server(*server).await {
                Ok(result) => results.push(result),
                Err(e) => warn!("STUN 服务器 {} 测试失败: {}", server, e),
            }
        }

        if results.is_empty() {
            return Err(NatError::NoStunServerAvailable);
        }

        // 分析结果
        let nat_type = self.analyze_nat_type(&results)?;
        let result = results[0].clone();

        Ok(NatDetectionResult {
            nat_type,
            public_ip: result.public_ip,
            public_port: result.public_port,
            private_ip: result.private_ip,
            private_port: result.private_port,
        })
    }

    /// 测试单个 STUN 服务器
    async fn test_stun_server(&self, server: SocketAddr) -> Result<NatDetectionResult, NatError> {
        // 发送 STUN 绑定请求
        let request = self.create_stun_request();
        self.socket.send_to(&request, server)?;

        // 接收响应
        let mut buffer = [0u8; 1024];
        let (n, _) = self.socket.recv_from(&mut buffer)?;
        let response = &buffer[..n];

        // 解析 STUN 响应
        self.parse_stun_response(response)
    }

    /// 创建 STUN 请求
    fn create_stun_request(&self) -> Vec<u8> {
        // TODO: 实现 STUN 请求生成
        vec![]
    }

    /// 解析 STUN 响应
    fn parse_stun_response(&self, response: &[u8]) -> Result<NatDetectionResult, NatError> {
        // TODO: 实现 STUN 响应解析
        Err(NatError::InvalidResponse)
    }

    /// 分析 NAT 类型
    fn analyze_nat_type(&self, results: &[NatDetectionResult]) -> Result<NatType, NatError> {
        // TODO: 实现 NAT 类型分析
        Ok(NatType::Unknown)
    }
}

/// NAT 错误
#[derive(Debug, thiserror::Error)]
pub enum NatError {
    #[error("IO 错误: {0}")]
    Io(#[from] std::io::Error),
    #[error("无效的响应")]
    InvalidResponse,
    #[error("没有可用的 STUN 服务器")]
    NoStunServerAvailable,
    #[error("未知错误: {0}")]
    Unknown(String),
}

/// 打洞管理器
pub struct HolePunchManager {
    /// 节点映射表
    nodes: HashMap<Uuid, Node>,
    /// 打洞通道
    channels: HashMap<(Uuid, Uuid), mpsc::Sender<HolePunchMessage>>,
}

/// 打洞消息
#[derive(Debug, Clone)]
pub enum HolePunchMessage {
    /// 开始打洞
    Start {
        source: Node,
        target: Node,
    },
    /// 打洞成功
    Success {
        source: Node,
        target: Node,
    },
    /// 打洞失败
    Failed {
        source: Node,
        target: Node,
        error: String,
    },
}

impl HolePunchManager {
    /// 创建新的打洞管理器
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            channels: HashMap::new(),
        }
    }

    /// 注册节点
    pub fn register_node(&mut self, node: Node) {
        self.nodes.insert(node.id, node);
    }

    /// 注销节点
    pub fn unregister_node(&mut self, node_id: Uuid) {
        self.nodes.remove(&node_id);
        self.channels.retain(|(source, target), _| {
            *source != node_id && *target != node_id
        });
    }

    /// 开始打洞
    pub async fn start_hole_punch(&mut self, source_id: Uuid, target_id: Uuid) -> Result<(), HolePunchError> {
        let source = self.nodes.get(&source_id).ok_or(HolePunchError::NodeNotFound)?;
        let target = self.nodes.get(&target_id).ok_or(HolePunchError::NodeNotFound)?;

        // 创建打洞通道
        let (tx, mut rx) = mpsc::channel(1);
        self.channels.insert((source_id, target_id), tx);

        // 启动打洞任务
        tokio::spawn(async move {
            match Self::perform_hole_punch(source.clone(), target.clone()).await {
                Ok(_) => {
                    if let Err(e) = tx.send(HolePunchMessage::Success {
                        source: source.clone(),
                        target: target.clone(),
                    }).await {
                        error!("发送打洞成功消息失败: {}", e);
                    }
                }
                Err(e) => {
                    if let Err(e) = tx.send(HolePunchMessage::Failed {
                        source: source.clone(),
                        target: target.clone(),
                        error: e.to_string(),
                    }).await {
                        error!("发送打洞失败消息失败: {}", e);
                    }
                }
            }
        });

        Ok(())
    }

    /// 执行打洞
    async fn perform_hole_punch(source: Node, target: Node) -> Result<(), HolePunchError> {
        // 创建 UDP 套接字
        let socket = UdpSocket::bind("0.0.0.0:0")?;
        socket.set_read_timeout(Some(Duration::from_secs(5)))?;

        // 根据 NAT 类型选择打洞策略
        let source_nat = source.nat_type.as_ref().map(|s| NatType::from_str(s)).unwrap_or(NatType::Unknown);
        let target_nat = target.nat_type.as_ref().map(|s| NatType::from_str(s)).unwrap_or(NatType::Unknown);
        match (source_nat, target_nat) {
            // 两个节点都是开放互联网
            (NatType::Open, NatType::Open) => {
                Self::direct_connect(&socket, &source, &target).await?;
            }
            // 一个节点是开放互联网
            (NatType::Open, _) | (_, NatType::Open) => {
                Self::one_side_punch(&socket, &source, &target).await?;
            }
            // 两个节点都是全锥形 NAT
            (NatType::FullCone, NatType::FullCone) => {
                Self::cone_punch(&socket, &source, &target).await?;
            }
            // 其他情况
            _ => {
                Self::symmetric_punch(&socket, &source, &target).await?;
            }
        }

        Ok(())
    }

    /// 直接连接
    async fn direct_connect(socket: &UdpSocket, source: &Node, target: &Node) -> Result<(), HolePunchError> {
        let target_addr = format!("{}:{}", target.ip, target.port);
        socket.connect(target_addr)?;
        socket.send(b"ping")?;
        Ok(())
    }

    /// 单向打洞
    async fn one_side_punch(socket: &UdpSocket, source: &Node, target: &Node) -> Result<(), HolePunchError> {
        let target_addr = format!("{}:{}", target.ip, target.port);
        socket.connect(target_addr)?;
        socket.send(b"ping")?;
        Ok(())
    }

    /// 锥形 NAT 打洞
    async fn cone_punch(socket: &UdpSocket, source: &Node, target: &Node) -> Result<(), HolePunchError> {
        let target_addr = format!("{}:{}", target.ip, target.port);
        socket.connect(target_addr)?;
        socket.send(b"ping")?;
        Ok(())
    }

    /// 对称型 NAT 打洞
    async fn symmetric_punch(socket: &UdpSocket, source: &Node, target: &Node) -> Result<(), HolePunchError> {
        let target_addr = format!("{}:{}", target.ip, target.port);
        socket.connect(target_addr)?;
        socket.send(b"ping")?;
        Ok(())
    }
}

/// 打洞错误
#[derive(Debug, thiserror::Error)]
pub enum HolePunchError {
    #[error("IO 错误: {0}")]
    Io(#[from] std::io::Error),
    #[error("节点未找到")]
    NodeNotFound,
    #[error("打洞超时")]
    Timeout,
    #[error("未知错误: {0}")]
    Unknown(String),
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};

    #[tokio::test]
    async fn test_nat_detection() {
        let stun_servers = vec![
            SocketAddr::new(IpAddr::V4(Ipv4Addr::new(1, 2, 3, 4)), 3478),
        ];
        let detector = NatDetector::new(stun_servers, Duration::from_secs(5)).unwrap();
        let result = detector.detect().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_hole_punch() {
        let mut manager = HolePunchManager::new();

        // 创建测试节点
        let node1 = Node {
            id: Uuid::new_v4(),
            name: "node1".to_string(),
            address: "*******".to_string(),
            port: 655,
            node_type: "client".to_string(),
            ip: "***********".to_string(),
            mac: "00:11:22:33:44:55".to_string(),
            public_key: "test_key_1".to_string(),
            status: "online".to_string(),
            tunnel_type: "tcp".to_string(),
            nat_type: Some("full_cone".to_string()),
            last_active: Some(chrono::Utc::now()),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let node2 = Node {
            id: Uuid::new_v4(),
            name: "node2".to_string(),
            address: "*******".to_string(),
            port: 655,
            node_type: "client".to_string(),
            ip: "***********".to_string(),
            mac: "00:11:22:33:44:66".to_string(),
            public_key: "test_key_2".to_string(),
            status: "online".to_string(),
            tunnel_type: "tcp".to_string(),
            nat_type: Some("full_cone".to_string()),
            last_active: Some(chrono::Utc::now()),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        // 注册节点
        manager.register_node(node1.clone());
        manager.register_node(node2.clone());

        // 测试打洞
        let result = manager.start_hole_punch(node1.id, node2.id).await;
        assert!(result.is_ok());
    }
} 