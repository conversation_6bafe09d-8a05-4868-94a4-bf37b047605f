pub mod nat;
pub mod stun;
pub mod tinc;

use std::net::{IpAddr, SocketAddr};
use tokio::net::{UdpSocket, TcpListener};
use thiserror::Error;
use tracing::{info, error, warn};
use serde::{Deserialize, Serialize};

pub use nat::*;
pub use stun::*;
pub use tinc::*;

/// NAT 类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum NatType {
    /// 开放互联网（无NAT）
    OpenInternet,
    /// 完全锥形NAT
    FullCone,
    /// 限制锥形NAT
    RestrictedCone,
    /// 端口限制锥形NAT
    PortRestrictedCone,
    /// 对称NAT
    Symmetric,
    /// 未知类型
    Unknown,
}

impl std::fmt::Display for NatType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            NatType::OpenInternet => write!(f, "open_internet"),
            NatType::FullCone => write!(f, "full_cone"),
            NatType::RestrictedCone => write!(f, "restricted_cone"),
            NatType::PortRestrictedCone => write!(f, "port_restricted_cone"),
            NatType::Symmetric => write!(f, "symmetric"),
            NatType::Unknown => write!(f, "unknown"),
        }
    }
}

/// NAT 探测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NatDetectionResult {
    pub nat_type: NatType,
    pub public_ip: IpAddr,
    pub public_port: u16,
    pub local_ip: IpAddr,
    pub local_port: u16,
    pub detected_at: chrono::DateTime<chrono::Utc>,
}

/// 打洞策略
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum HolePunchingStrategy {
    /// UDP 打洞
    Udp,
    /// TCP 同时打开
    Tcp,
    /// 中继转发
    Relay,
}

impl std::fmt::Display for HolePunchingStrategy {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HolePunchingStrategy::Udp => write!(f, "udp"),
            HolePunchingStrategy::Tcp => write!(f, "tcp"),
            HolePunchingStrategy::Relay => write!(f, "relay"),
        }
    }
}

/// 网络错误类型
#[derive(Debug, Error)]
pub enum NetworkError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("STUN error: {0}")]
    Stun(String),

    #[error("Connection timeout")]
    Timeout,

    #[error("Invalid address: {0}")]
    InvalidAddress(String),
}

/// NAT 探测器
pub struct NatDetector {
    stun_server: SocketAddr,
}

impl NatDetector {
    /// 创建新的 NAT 探测器
    pub fn new(stun_server: SocketAddr) -> Self {
        Self { stun_server }
    }

    /// 探测 NAT 类型
    pub async fn detect(&self) -> Result<NatDetectionResult, NetworkError> {
        // TODO: 实现 STUN 协议探测
        // 1. 创建 UDP socket
        // 2. 发送 STUN 绑定请求
        // 3. 解析响应获取公网 IP 和端口
        // 4. 根据响应特征判断 NAT 类型
        unimplemented!()
    }
}

/// 打洞器
pub struct HolePuncher {
    local_addr: SocketAddr,
    remote_addr: SocketAddr,
    strategy: HolePunchingStrategy,
}

impl HolePuncher {
    /// 创建新的打洞器
    pub fn new(
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        strategy: HolePunchingStrategy,
    ) -> Self {
        Self {
            local_addr,
            remote_addr,
            strategy,
        }
    }

    /// 执行打洞
    pub async fn punch(&self) -> Result<(), NetworkError> {
        match self.strategy {
            HolePunchingStrategy::Udp => self.udp_punch().await,
            HolePunchingStrategy::Tcp => self.tcp_punch().await,
            HolePunchingStrategy::Relay => self.relay_punch().await,
        }
    }

    /// UDP 打洞
    async fn udp_punch(&self) -> Result<(), NetworkError> {
        let socket = UdpSocket::bind(self.local_addr).await?;
        
        // 发送探测包
        socket.send_to(b"PING", self.remote_addr).await?;
        
        // 等待响应
        let mut buf = [0u8; 1024];
        let (n, _) = socket.recv_from(&mut buf).await?;
        
        if &buf[..n] == b"PONG" {
            info!("UDP hole punching successful");
            Ok(())
        } else {
            error!("UDP hole punching failed");
            Err(NetworkError::Timeout)
        }
    }

    /// TCP 打洞
    async fn tcp_punch(&self) -> Result<(), NetworkError> {
        // TODO: 实现 TCP 同时打开
        unimplemented!()
    }

    /// 中继打洞
    async fn relay_punch(&self) -> Result<(), NetworkError> {
        // TODO: 实现中继服务器转发
        unimplemented!()
    }
}

/// 连接测试器
pub struct ConnectionTester {
    local_addr: SocketAddr,
    remote_addr: SocketAddr,
}

impl ConnectionTester {
    /// 创建新的连接测试器
    pub fn new(local_addr: SocketAddr, remote_addr: SocketAddr) -> Self {
        Self {
            local_addr,
            remote_addr,
        }
    }

    /// 测试连接
    pub async fn test(&self) -> Result<bool, NetworkError> {
        // 尝试 UDP 打洞
        let udp_result = self.test_udp().await;
        if udp_result.is_ok() {
            return Ok(true);
        }

        // 尝试 TCP 打洞
        let tcp_result = self.test_tcp().await;
        if tcp_result.is_ok() {
            return Ok(true);
        }

        // 尝试中继
        let relay_result = self.test_relay().await;
        if relay_result.is_ok() {
            return Ok(true);
        }

        Ok(false)
    }

    /// 测试 UDP 连接
    async fn test_udp(&self) -> Result<(), NetworkError> {
        let puncher = HolePuncher::new(
            self.local_addr,
            self.remote_addr,
            HolePunchingStrategy::Udp,
        );
        puncher.punch().await
    }

    /// 测试 TCP 连接
    async fn test_tcp(&self) -> Result<(), NetworkError> {
        let puncher = HolePuncher::new(
            self.local_addr,
            self.remote_addr,
            HolePunchingStrategy::Tcp,
        );
        puncher.punch().await
    }

    /// 测试中继连接
    async fn test_relay(&self) -> Result<(), NetworkError> {
        let puncher = HolePuncher::new(
            self.local_addr,
            self.remote_addr,
            HolePunchingStrategy::Relay,
        );
        puncher.punch().await
    }
} 