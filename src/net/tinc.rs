use std::net::{SocketAddr, TcpStream, UdpSocket};
use std::time::Duration;
use std::io::{Read, Write};
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::sleep;
use tracing::{info, error, warn};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

use crate::models::Node;

/// Tinc 消息类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TincMessageType {
    /// 握手请求
    HandshakeRequest = 0x01,
    /// 握手响应
    HandshakeResponse = 0x02,
    /// 数据包
    DataPacket = 0x03,
    /// 心跳包
    Heartbeat = 0x04,
    /// 错误消息
    Error = 0xFF,
}

/// Tinc 消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TincMessage {
    /// 消息类型
    pub message_type: TincMessageType,
    /// 消息长度
    pub message_length: u32,
    /// 消息内容
    pub content: Vec<u8>,
}

/// Tinc 连接
pub struct TincConnection {
    /// 本地节点
    local_node: Node,
    /// 远程节点
    remote_node: Node,
    /// TCP 连接
    tcp_stream: Option<TcpStream>,
    /// UDP 套接字
    udp_socket: Option<UdpSocket>,
    /// 连接状态
    status: ConnectionStatus,
    /// 最后心跳时间
    last_heartbeat: Duration,
}

/// 连接状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConnectionStatus {
    /// 未连接
    Disconnected,
    /// 正在连接
    Connecting,
    /// 已连接
    Connected,
    /// 连接失败
    Failed,
}

impl TincConnection {
    /// 创建新的 Tinc 连接
    pub fn new(local_node: Node, remote_node: Node) -> Self {
        Self {
            local_node,
            remote_node,
            tcp_stream: None,
            udp_socket: None,
            status: ConnectionStatus::Disconnected,
            last_heartbeat: Duration::from_secs(0),
        }
    }

    /// 连接到远程节点
    pub async fn connect(&mut self) -> Result<(), TincError> {
        self.status = ConnectionStatus::Connecting;

        // 创建 TCP 连接
        let tcp_addr = format!("{}:{}", self.remote_node.address, self.remote_node.port);
        let tcp_stream = TcpStream::connect(tcp_addr)?;
        tcp_stream.set_read_timeout(Some(Duration::from_secs(5)))?;
        tcp_stream.set_write_timeout(Some(Duration::from_secs(5)))?;
        self.tcp_stream = Some(tcp_stream);

        // 创建 UDP 套接字
        let udp_socket = UdpSocket::bind("0.0.0.0:0")?;
        udp_socket.set_read_timeout(Some(Duration::from_secs(5)))?;
        udp_socket.set_write_timeout(Some(Duration::from_secs(5)))?;
        self.udp_socket = Some(udp_socket);

        // 发送握手请求
        self.send_handshake_request().await?;

        // 等待握手响应
        let response = self.receive_handshake_response().await?;
        if response.message_type != TincMessageType::HandshakeResponse {
            return Err(TincError::InvalidHandshake);
        }

        self.status = ConnectionStatus::Connected;
        self.last_heartbeat = Duration::from_secs(0);

        // 启动心跳任务
        self.start_heartbeat().await?;

        Ok(())
    }

    /// 断开连接
    pub async fn disconnect(&mut self) -> Result<(), TincError> {
        if let Some(stream) = &mut self.tcp_stream {
            stream.shutdown(std::net::Shutdown::Both)?;
        }
        self.tcp_stream = None;
        self.udp_socket = None;
        self.status = ConnectionStatus::Disconnected;
        Ok(())
    }

    /// 发送数据
    pub async fn send_data(&mut self, data: &[u8]) -> Result<(), TincError> {
        if self.status != ConnectionStatus::Connected {
            return Err(TincError::NotConnected);
        }

        let message = TincMessage {
            message_type: TincMessageType::DataPacket,
            message_length: data.len() as u32,
            content: data.to_vec(),
        };

        self.send_message(&message).await
    }

    /// 接收数据
    pub async fn receive_data(&mut self) -> Result<Vec<u8>, TincError> {
        if self.status != ConnectionStatus::Connected {
            return Err(TincError::NotConnected);
        }

        let message = self.receive_message().await?;
        if message.message_type != TincMessageType::DataPacket {
            return Err(TincError::InvalidMessage);
        }

        Ok(message.content)
    }

    /// 发送握手请求
    async fn send_handshake_request(&mut self) -> Result<(), TincError> {
        let message = TincMessage {
            message_type: TincMessageType::HandshakeRequest,
            message_length: 0,
            content: vec![],
        };

        self.send_message(&message).await
    }

    /// 接收握手响应
    async fn receive_handshake_response(&mut self) -> Result<TincMessage, TincError> {
        self.receive_message().await
    }

    /// 发送消息
    async fn send_message(&mut self, message: &TincMessage) -> Result<(), TincError> {
        if let Some(stream) = &mut self.tcp_stream {
            let data = bincode::serialize(message)?;
            stream.write_all(&data)?;
            Ok(())
        } else {
            Err(TincError::NotConnected)
        }
    }

    /// 接收消息
    async fn receive_message(&mut self) -> Result<TincMessage, TincError> {
        if let Some(stream) = &mut self.tcp_stream {
            let mut length_buf = [0u8; 4];
            stream.read_exact(&mut length_buf)?;
            let length = u32::from_be_bytes(length_buf) as usize;

            let mut data = vec![0u8; length];
            stream.read_exact(&mut data)?;

            let message = bincode::deserialize(&data)?;
            Ok(message)
        } else {
            Err(TincError::NotConnected)
        }
    }

    /// 启动心跳任务
    async fn start_heartbeat(&mut self) -> Result<(), TincError> {
        let mut interval = tokio::time::interval(Duration::from_secs(30));
        let mut connection = self.clone();

        tokio::spawn(async move {
            loop {
                interval.tick().await;
                if let Err(e) = connection.send_heartbeat().await {
                    error!("发送心跳包失败: {}", e);
                    break;
                }
            }
        });

        Ok(())
    }

    /// 发送心跳包
    async fn send_heartbeat(&mut self) -> Result<(), TincError> {
        let message = TincMessage {
            message_type: TincMessageType::Heartbeat,
            message_length: 0,
            content: vec![],
        };

        self.send_message(&message).await
    }
}

impl Clone for TincConnection {
    fn clone(&self) -> Self {
        Self {
            local_node: self.local_node.clone(),
            remote_node: self.remote_node.clone(),
            tcp_stream: None,
            udp_socket: None,
            status: self.status,
            last_heartbeat: self.last_heartbeat,
        }
    }
}

/// Tinc 错误
#[derive(Debug, thiserror::Error)]
pub enum TincError {
    #[error("IO 错误: {0}")]
    Io(#[from] std::io::Error),
    #[error("序列化错误: {0}")]
    Serialization(#[from] bincode::Error),
    #[error("未连接")]
    NotConnected,
    #[error("无效的握手")]
    InvalidHandshake,
    #[error("无效的消息")]
    InvalidMessage,
    #[error("未知错误: {0}")]
    Unknown(String),
}

/// Tinc 连接管理器
pub struct TincConnectionManager {
    /// 连接映射表
    connections: Arc<Mutex<HashMap<Uuid, TincConnection>>>,
}

impl TincConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> Self {
        Self {
            connections: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 创建连接
    pub async fn create_connection(&self, local_node: Node, remote_node: Node) -> Result<(), TincError> {
        let mut connection = TincConnection::new(local_node, remote_node);
        connection.connect().await?;

        let mut connections = self.connections.lock().await;
        connections.insert(remote_node.id, connection);

        Ok(())
    }

    /// 关闭连接
    pub async fn close_connection(&self, node_id: Uuid) -> Result<(), TincError> {
        let mut connections = self.connections.lock().await;
        if let Some(mut connection) = connections.remove(&node_id) {
            connection.disconnect().await?;
        }
        Ok(())
    }

    /// 发送数据
    pub async fn send_data(&self, node_id: Uuid, data: &[u8]) -> Result<(), TincError> {
        let mut connections = self.connections.lock().await;
        if let Some(connection) = connections.get_mut(&node_id) {
            connection.send_data(data).await
        } else {
            Err(TincError::NotConnected)
        }
    }

    /// 接收数据
    pub async fn receive_data(&self, node_id: Uuid) -> Result<Vec<u8>, TincError> {
        let mut connections = self.connections.lock().await;
        if let Some(connection) = connections.get_mut(&node_id) {
            connection.receive_data().await
        } else {
            Err(TincError::NotConnected)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};

    #[tokio::test]
    async fn test_tinc_connection() {
        // 创建测试节点
        let local_node = Node {
            id: Uuid::new_v4(),
            name: "local".to_string(),
            address: "127.0.0.1".to_string(),
            port: 655,
            node_type: "client".to_string(),
            ip: "***********".to_string(),
            mac: "00:11:22:33:44:55".to_string(),
            public_key: "test_key_local".to_string(),
            status: "online".to_string(),
            tunnel_type: "tcp".to_string(),
            nat_type: Some("full_cone".to_string()),
            last_active: Some(chrono::Utc::now()),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let remote_node = Node {
            id: Uuid::new_v4(),
            name: "remote".to_string(),
            address: "127.0.0.1".to_string(),
            port: 656,
            node_type: "client".to_string(),
            ip: "***********".to_string(),
            mac: "00:11:22:33:44:66".to_string(),
            public_key: "test_key_remote".to_string(),
            status: "online".to_string(),
            tunnel_type: "tcp".to_string(),
            nat_type: Some("full_cone".to_string()),
            last_active: Some(chrono::Utc::now()),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        // 创建连接管理器
        let manager = TincConnectionManager::new();

        // 测试创建连接
        let result = manager.create_connection(local_node.clone(), remote_node.clone()).await;
        assert!(result.is_ok());

        // 测试发送数据
        let data = b"Hello, Tinc!";
        let result = manager.send_data(remote_node.id, data).await;
        assert!(result.is_ok());

        // 测试接收数据
        let result = manager.receive_data(remote_node.id).await;
        assert!(result.is_ok());

        // 测试关闭连接
        let result = manager.close_connection(remote_node.id).await;
        assert!(result.is_ok());
    }
} 