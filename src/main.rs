mod api;
mod auth;
mod config;
mod db;
mod log;
mod models;
mod net;
mod utils;

use std::sync::Arc;
use axum::{
    Router,
    http::{
        header::{AUTHORIZATION, CONTENT_TYPE},
        Method,
    },
    routing::{get, post, delete},
    Json,
    http::StatusCode,
    routing::IntoMakeService,
};
use tower_http::cors::{CorsLayer, Any};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use std::net::SocketAddr;
use std::path::PathBuf;
use serde_json::json;
use tokio::signal;
use tracing::{info, error, warn};
use tower_http::trace::TraceLayer;
use tower::util::ServiceExt;

use crate::{
    config::{AppConfig, ConfigManager},
    db::Database,
    api::create_router,
    log::LogManager,
    auth::AuthService,
    net::{NatDete<PERSON>, HolePunchManager},
    api::users::UserService,
};

/// 应用状态
#[derive(Clone)]
pub struct AppState {
    pub db: Arc<Database>,
    pub config: ConfigManager,
    pub log: LogManager,
    pub auth: AuthService,
    pub user_service: Arc<UserService>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 初始化配置
    let config = AppConfig::default();
    let config_manager = ConfigManager::new(Some(&config.config_dir()));
    config_manager.init().await?;

    // 初始化日志管理器
    let log_manager = LogManager::new(&config.log_dir, 10 * 1024 * 1024, 10)?;
    log_manager.init()?;

    // 创建数据库连接
    let db = Arc::new(Database::new(&config.database_url())
        .await
        .expect("Failed to connect to database"));

    // 初始化数据库表
    db.init()
        .await
        .expect("Failed to initialize database tables");

    // 创建认证服务
    let auth = AuthService::new();

    // 创建用户服务
    let user_service = Arc::new(UserService::new(db.clone()));

    // 创建应用状态
    let state = AppState {
        db: db.clone(),
        config: config_manager,
        log: log_manager,
        auth,
        user_service: user_service.clone(),
    };

    // 创建应用路由
    let app = create_router(db);

    // 启动服务器
    let addr = config.server_addr();
    info!("Server running on http://{}", addr);

    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal())
        .await?;

    Ok(())
}

/// 健康检查
async fn health_check() -> (StatusCode, Json<serde_json::Value>) {
    (StatusCode::OK, Json(json!({ "status": "ok" })))
}

/// 优雅关闭信号处理
async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("无法监听 Ctrl+C 信号");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("无法监听 SIGTERM 信号")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    info!("正在关闭服务器...");
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
    };
    use tower::ServiceExt;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_server_startup() {
        // 初始化测试配置
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let config_manager = config::ConfigManager::new(config.config_dir());
        config_manager.init().await.unwrap();

        // 创建测试状态
        let state = AppState {
            db: db.clone(),
            config: config_manager.clone(),
            log: LogManager::new(
                config.log_dir.clone(),
                config.log.max_size * 1024 * 1024, // 转换为字节
                config.log.max_files,
            ).expect("Failed to create log manager"),
            network_config: NetworkConfigManager::new(None),
        };

        // 创建测试路由
        let app = Router::new()
            .nest("/api", create_router(Arc::new(db)));

        // 测试健康检查
        let response = app
            .oneshot(Request::builder().uri("/api/health").body(Body::empty()).unwrap())
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_user_flow() {
        // 初始化测试配置
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let config_manager = config::ConfigManager::new(config.config_dir());
        config_manager.init().await.unwrap();

        // 创建测试状态
        let state = AppState {
            db: db.clone(),
            config: config_manager.clone(),
            log: LogManager::new(
                config.log_dir.clone(),
                config.log.max_size * 1024 * 1024, // 转换为字节
                config.log.max_files,
            ).expect("Failed to create log manager"),
            network_config: NetworkConfigManager::new(None),
        };

        // 创建测试路由
        let app = Router::new()
            .nest("/api", create_router(Arc::new(db)));

        // 测试用户注册
        let register_body = serde_json::json!({
            "username": format!("test_user_{}", Uuid::new_v4()),
            "password": "test_password",
            "email": format!("test_{}@example.com", Uuid::new_v4())
        });

        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .method("POST")
                    .uri("/api/users/register")
                    .header("content-type", "application/json")
                    .body(Body::from(register_body.to_string()))
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);

        // 测试用户登录
        let login_body = serde_json::json!({
            "username": "test_user",
            "password": "test_password"
        });

        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .method("POST")
                    .uri("/api/users/login")
                    .header("content-type", "application/json")
                    .body(Body::from(login_body.to_string()))
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_node_flow() {
        // 初始化测试配置
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let config_manager = config::ConfigManager::new(config.config_dir());
        config_manager.init().await.unwrap();

        // 创建测试状态
        let state = AppState {
            db: db.clone(),
            config: config_manager.clone(),
            log: LogManager::new(
                config.log_dir.clone(),
                config.log.max_size * 1024 * 1024, // 转换为字节
                config.log.max_files,
            ).expect("Failed to create log manager"),
            network_config: NetworkConfigManager::new(None),
        };

        // 创建测试路由
        let app = Router::new()
            .nest("/api", create_router(Arc::new(db)));

        // 测试创建节点
        let create_node_body = serde_json::json!({
            "name": format!("test_node_{}", Uuid::new_v4()),
            "ip_address": "127.0.0.1",
            "port": 655,
            "public_key": "test_key"
        });

        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .method("POST")
                    .uri("/api/nodes")
                    .header("content-type", "application/json")
                    .body(Body::from(create_node_body.to_string()))
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);

        // 测试获取节点列表
        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .uri("/api/nodes")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_connection_flow() {
        // 初始化测试配置
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let config_manager = config::ConfigManager::new(config.config_dir());
        config_manager.init().await.unwrap();

        // 创建测试状态
        let state = AppState {
            db: db.clone(),
            config: config_manager.clone(),
            log: LogManager::new(
                config.log_dir.clone(),
                config.log.max_size * 1024 * 1024, // 转换为字节
                config.log.max_files,
            ).expect("Failed to create log manager"),
            network_config: NetworkConfigManager::new(None),
        };

        // 创建测试路由
        let app = Router::new()
            .nest("/api", create_router(Arc::new(db)));

        // 测试创建连接
        let create_connection_body = serde_json::json!({
            "client_id": Uuid::new_v4(),
            "server_id": Uuid::new_v4(),
            "method_used": "tcp"
        });

        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .method("POST")
                    .uri("/api/connections")
                    .header("content-type", "application/json")
                    .body(Body::from(create_connection_body.to_string()))
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);

        // 测试获取连接列表
        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .uri("/api/connections")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_config_flow() {
        // 初始化测试配置
        let config = AppConfig::default();
        let db = Database::new(config.database_url()).await.unwrap();
        let config_manager = config::ConfigManager::new(config.config_dir());
        config_manager.init().await.unwrap();

        // 创建测试状态
        let state = AppState {
            db: db.clone(),
            config: config_manager.clone(),
            log: LogManager::new(),
            network_config: NetworkConfigManager::new(None),
        };

        // 创建测试路由
        let app = Router::new()
            .nest("/api", create_router(Arc::new(db)));

        // 测试推送配置
        let push_config_body = serde_json::json!({
            "content": r#"
                [network]
                name = "test"
                port = 655
                mode = "switch"
            "#
        });

        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .method("POST")
                    .uri(&format!("/api/configs/{}", Uuid::new_v4()))
                    .header("content-type", "application/json")
                    .body(Body::from(push_config_body.to_string()))
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);

        // 测试获取配置
        let response = app
            .clone()
            .oneshot(
                Request::builder()
                    .uri(&format!("/api/configs/{}", Uuid::new_v4()))
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_health_check() {
        let config = AppConfig::default();
        let db = Arc::new(Database::new_in_memory().await.unwrap());
        let config_manager = config::ConfigManager::new();
        let logger = LogManager::new(
            std::path::Path::new("/tmp/tinc-rust-test-logs"),
            1024 * 1024, // 1MB
            5
        ).expect("Failed to create logger");

        let app = api::create_app(
            db,
            config_manager,
            logger,
            config,
        );

        let response = app
            .oneshot(Request::builder().uri("/api/health").body(Body::empty()).unwrap())
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        assert_eq!(&body[..], b"OK");
    }
} 