use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::net::TcpListener;
use tower_http::cors::CorsLayer;
use tracing::{info, warn};
use uuid::Uuid;

/// 简化的用户模型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub role: String,
}

/// 简化的节点模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Node {
    pub id: Uuid,
    pub name: String,
    pub ip: String,
    pub port: u16,
    pub status: String,
    pub nat_type: Option<String>,
}

/// 简化的连接模型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Connection {
    pub id: Uuid,
    pub source_id: Uuid,
    pub target_id: Uuid,
    pub status: String,
}

/// API 响应结构
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub message: String,
    pub data: Option<T>,
}

/// 登录请求
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// 登录响应
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: User,
}

/// 创建节点请求
#[derive(Debug, Deserialize)]
pub struct CreateNodeRequest {
    pub name: String,
    pub ip: String,
    pub port: u16,
}

/// 创建连接请求
#[derive(Debug, Deserialize)]
pub struct CreateConnectionRequest {
    pub source_id: Uuid,
    pub target_id: Uuid,
}

/// 应用状态
#[derive(Clone)]
pub struct AppState {
    pub users: Arc<Mutex<HashMap<Uuid, User>>>,
    pub nodes: Arc<Mutex<HashMap<Uuid, Node>>>,
    pub connections: Arc<Mutex<HashMap<Uuid, Connection>>>,
}

impl AppState {
    pub fn new() -> Self {
        let mut users = HashMap::new();
        
        // 添加默认管理员用户
        let admin_user = User {
            id: Uuid::new_v4(),
            username: "admin".to_string(),
            email: "<EMAIL>".to_string(),
            role: "admin".to_string(),
        };
        users.insert(admin_user.id, admin_user);

        Self {
            users: Arc::new(Mutex::new(users)),
            nodes: Arc::new(Mutex::new(HashMap::new())),
            connections: Arc::new(Mutex::new(HashMap::new())),
        }
    }
}

/// 健康检查
async fn health_check() -> Json<ApiResponse<&'static str>> {
    Json(ApiResponse {
        code: 0,
        message: "服务正常".to_string(),
        data: Some("OK"),
    })
}

/// 用户登录
async fn login(
    State(state): State<AppState>,
    Json(req): Json<LoginRequest>,
) -> Result<Json<ApiResponse<LoginResponse>>, StatusCode> {
    let users = state.users.lock().unwrap();
    
    // 简单的用户验证
    for user in users.values() {
        if user.username == req.username {
            let response = LoginResponse {
                token: format!("token_{}", user.id),
                user: user.clone(),
            };
            
            return Ok(Json(ApiResponse {
                code: 0,
                message: "登录成功".to_string(),
                data: Some(response),
            }));
        }
    }
    
    Err(StatusCode::UNAUTHORIZED)
}

/// 获取用户列表
async fn list_users(State(state): State<AppState>) -> Json<ApiResponse<Vec<User>>> {
    let users = state.users.lock().unwrap();
    let user_list: Vec<User> = users.values().cloned().collect();
    
    Json(ApiResponse {
        code: 0,
        message: "获取用户列表成功".to_string(),
        data: Some(user_list),
    })
}

/// 创建节点
async fn create_node(
    State(state): State<AppState>,
    Json(req): Json<CreateNodeRequest>,
) -> Json<ApiResponse<Node>> {
    let node = Node {
        id: Uuid::new_v4(),
        name: req.name,
        ip: req.ip,
        port: req.port,
        status: "offline".to_string(),
        nat_type: None,
    };
    
    let mut nodes = state.nodes.lock().unwrap();
    nodes.insert(node.id, node.clone());
    
    Json(ApiResponse {
        code: 0,
        message: "节点创建成功".to_string(),
        data: Some(node),
    })
}

/// 获取节点列表
async fn list_nodes(State(state): State<AppState>) -> Json<ApiResponse<Vec<Node>>> {
    let nodes = state.nodes.lock().unwrap();
    let node_list: Vec<Node> = nodes.values().cloned().collect();
    
    Json(ApiResponse {
        code: 0,
        message: "获取节点列表成功".to_string(),
        data: Some(node_list),
    })
}

/// 获取节点详情
async fn get_node(
    State(state): State<AppState>,
    Path(id): Path<Uuid>,
) -> Result<Json<ApiResponse<Node>>, StatusCode> {
    let nodes = state.nodes.lock().unwrap();
    
    if let Some(node) = nodes.get(&id) {
        Ok(Json(ApiResponse {
            code: 0,
            message: "获取节点成功".to_string(),
            data: Some(node.clone()),
        }))
    } else {
        Err(StatusCode::NOT_FOUND)
    }
}

/// 创建连接
async fn create_connection(
    State(state): State<AppState>,
    Json(req): Json<CreateConnectionRequest>,
) -> Json<ApiResponse<Connection>> {
    let connection = Connection {
        id: Uuid::new_v4(),
        source_id: req.source_id,
        target_id: req.target_id,
        status: "connecting".to_string(),
    };
    
    let mut connections = state.connections.lock().unwrap();
    connections.insert(connection.id, connection.clone());
    
    Json(ApiResponse {
        code: 0,
        message: "连接创建成功".to_string(),
        data: Some(connection),
    })
}

/// 获取连接列表
async fn list_connections(State(state): State<AppState>) -> Json<ApiResponse<Vec<Connection>>> {
    let connections = state.connections.lock().unwrap();
    let connection_list: Vec<Connection> = connections.values().cloned().collect();
    
    Json(ApiResponse {
        code: 0,
        message: "获取连接列表成功".to_string(),
        data: Some(connection_list),
    })
}

/// NAT 探测
async fn detect_nat(Path(client_id): Path<Uuid>) -> Json<ApiResponse<String>> {
    // 模拟 NAT 探测
    Json(ApiResponse {
        code: 0,
        message: "NAT 探测完成".to_string(),
        data: Some("full_cone".to_string()),
    })
}

/// 测试连接
async fn test_connection(Path(id): Path<Uuid>) -> Json<ApiResponse<String>> {
    // 模拟连接测试
    Json(ApiResponse {
        code: 0,
        message: "连接测试完成".to_string(),
        data: Some("success".to_string()),
    })
}

/// Prometheus 指标
async fn metrics() -> &'static str {
    r#"# HELP tinc_vpn_connections_total Total number of VPN connections
# TYPE tinc_vpn_connections_total counter
tinc_vpn_connections_total 0

# HELP tinc_vpn_active_connections Active VPN connections
# TYPE tinc_vpn_active_connections gauge
tinc_vpn_active_connections 0
"#
}

/// 创建路由
fn create_router() -> Router {
    let state = AppState::new();
    
    Router::new()
        // 健康检查
        .route("/health", get(health_check))
        // 认证接口
        .route("/api/login", post(login))
        // 用户管理
        .route("/api/users", get(list_users))
        // 节点管理
        .route("/api/nodes", get(list_nodes))
        .route("/api/nodes", post(create_node))
        .route("/api/nodes/:id", get(get_node))
        // 连接管理
        .route("/api/connections", get(list_connections))
        .route("/api/connections", post(create_connection))
        // NAT 探测
        .route("/api/nat/:client_id/detect", post(detect_nat))
        // 连接测试
        .route("/api/connections/:id/test", post(test_connection))
        // 监控指标
        .route("/metrics", get(metrics))
        .layer(CorsLayer::permissive())
        .with_state(state)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    // 创建应用
    let app = create_router();
    
    // 启动服务器
    let addr = "0.0.0.0:3000";
    info!("🚀 Rust Tinc VPN 服务器启动成功！");
    info!("📡 监听地址: http://{}", addr);
    info!("🔍 健康检查: http://{}/health", addr);
    info!("📊 监控指标: http://{}/metrics", addr);
    info!("📚 API 文档: 请查看 README.md");
    
    let listener = TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;
    
    Ok(())
}
