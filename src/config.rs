use std::env;
use serde::Deserialize;
use config::{Config, ConfigError, File};

#[derive(Debug, Deserialize)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub server: ServerConfig,
    pub log: LogConfig,
}

#[derive(Debug, Deserialize)]
pub struct DatabaseConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub database: String,
    pub max_connections: u32,
}

#[derive(Debug, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debug, Deserialize)]
pub struct LogConfig {
    pub level: String,
    pub file: String,
    pub max_size: usize,
    pub max_files: usize,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig {
                host: env::var("DB_HOST").unwrap_or_else(|_| "localhost".to_string()),
                port: env::var("DB_PORT")
                    .unwrap_or_else(|_| "3306".to_string())
                    .parse()
                    .unwrap_or(3306),
                username: env::var("DB_USER").unwrap_or_else(|_| "root".to_string()),
                password: env::var("DB_PASSWORD").unwrap_or_else(|_| "".to_string()),
                database: env::var("DB_NAME").unwrap_or_else(|_| "tinc_rust".to_string()),
                max_connections: env::var("DB_MAX_CONNECTIONS")
                    .unwrap_or_else(|_| "5".to_string())
                    .parse()
                    .unwrap_or(5),
            },
            server: ServerConfig {
                host: env::var("SERVER_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
                port: env::var("SERVER_PORT")
                    .unwrap_or_else(|_| "3000".to_string())
                    .parse()
                    .unwrap_or(3000),
            },
            log: LogConfig {
                level: env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
                file: env::var("LOG_FILE").unwrap_or_else(|_| "logs/app.log".to_string()),
                max_size: env::var("LOG_MAX_SIZE")
                    .unwrap_or_else(|_| "10485760".to_string())
                    .parse()
                    .unwrap_or(10 * 1024 * 1024), // 10MB
                max_files: env::var("LOG_MAX_FILES")
                    .unwrap_or_else(|_| "10".to_string())
                    .parse()
                    .unwrap_or(10),
            },
        }
    }
}

impl AppConfig {
    pub fn database_url(&self) -> String {
        format!(
            "mysql://{}:{}@{}:{}/{}",
            self.database.username,
            self.database.password,
            self.database.host,
            self.database.port,
            self.database.database
        )
    }

    pub fn server_addr(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }

    pub fn load() -> Result<Self, ConfigError> {
        let config = Config::builder()
            .add_source(File::with_name("config/default"))
            .add_source(File::with_name("config/local").required(false))
            .add_source(config::Environment::with_prefix("TINC"))
            .build()?;

        config.try_deserialize()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = AppConfig::default();
        assert_eq!(config.database.host, "localhost");
        assert_eq!(config.database.port, 3306);
        assert_eq!(config.database.username, "root");
        assert_eq!(config.database.database, "tinc_rust");
        assert_eq!(config.database.max_connections, 5);
        assert_eq!(config.server.host, "127.0.0.1");
        assert_eq!(config.server.port, 3000);
        assert_eq!(config.log.level, "info");
        assert_eq!(config.log.file, "logs/app.log");
        assert_eq!(config.log.max_size, 10 * 1024 * 1024);
        assert_eq!(config.log.max_files, 10);
    }

    #[test]
    fn test_database_url() {
        let config = AppConfig::default();
        assert_eq!(
            config.database_url(),
            "mysql://root:@localhost:3306/tinc_rust"
        );
    }

    #[test]
    fn test_server_addr() {
        let config = AppConfig::default();
        assert_eq!(config.server_addr(), "127.0.0.1:3000");
    }
} 