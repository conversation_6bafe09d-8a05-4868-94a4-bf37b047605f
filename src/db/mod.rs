use sqlx::{
    mysql::{MySqlPool, MySqlPoolOptions},
    Error as SqlxError,
};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use tracing::{info, error};
use bcrypt::BcryptError;
use std::sync::Arc;

use crate::config::AppConfig;

/// 数据库错误
#[derive(Debug, thiserror::Error)]
pub enum DbError {
    #[error("数据库错误: {0}")]
    Database(#[from] SqlxError),
    #[error("记录未找到")]
    NotFound,
    #[error("记录已存在")]
    AlreadyExists,
    #[error("无效的输入: {0}")]
    InvalidInput(String),
    #[error("迁移错误: {0}")]
    MigrationError(String),
    #[error("无效的凭据")]
    InvalidCredentials,
    #[error("其他错误: {0}")]
    Other(String),
}

impl From<sqlx::migrate::MigrateError> for DbError {
    fn from(err: sqlx::migrate::MigrateError) -> Self {
        DbError::MigrationError(err.to_string())
    }
}

impl From<BcryptError> for DbError {
    fn from(e: BcryptError) -> Self {
        DbError::Other(e.to_string())
    }
}

/// 数据库连接池
pub struct Database {
    pool: MySqlPool,
}

impl Database {
    /// 创建新的数据库连接
    pub async fn new(database_url: &str) -> Result<Self, DbError> {
        let pool = MySqlPoolOptions::new()
            .max_connections(5)
            .connect(database_url)
            .await?;

        Ok(Self { pool })
    }

    /// 获取连接池
    pub fn pool(&self) -> &MySqlPool {
        &self.pool
    }

    /// 初始化数据库表
    pub async fn init(&self) -> Result<(), DbError> {
        // 创建用户表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS users (
                id CHAR(36) PRIMARY KEY,
                username VARCHAR(255) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(50) NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(self.pool())
        .await?;

        // 创建节点表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS nodes (
                id CHAR(36) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                address VARCHAR(255) NOT NULL,
                port INT NOT NULL,
                node_type VARCHAR(50) NOT NULL,
                ip VARCHAR(255) NOT NULL,
                mac VARCHAR(255) NOT NULL,
                status VARCHAR(50) NOT NULL,
                tunnel_type VARCHAR(50) NOT NULL,
                last_active TIMESTAMP NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(self.pool())
        .await?;

        // 创建连接表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS connections (
                id CHAR(36) PRIMARY KEY,
                source_id CHAR(36) NOT NULL,
                target_id CHAR(36) NOT NULL,
                connection_type VARCHAR(50) NOT NULL,
                status VARCHAR(50) NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (source_id) REFERENCES nodes(id),
                FOREIGN KEY (target_id) REFERENCES nodes(id)
            )
            "#,
        )
        .execute(self.pool())
        .await?;

        // 创建配置表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS configs (
                id CHAR(36) PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                value TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(self.pool())
        .await?;

        // 创建系统日志表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS system_logs (
                id CHAR(36) PRIMARY KEY,
                level VARCHAR(50) NOT NULL,
                message TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(self.pool())
        .await?;

        Ok(())
    }
}

/// 用户相关模型和 DAO
#[derive(Debug, sqlx::FromRow)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub password_hash: String,
    pub role: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

pub struct UserDao {
    db: Arc<Database>,
}

impl UserDao {
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    pub async fn create(&self, username: &str, password_hash: &str, role: &str) -> Result<User, DbError> {
        let id = Uuid::new_v4();
        let user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (id, username, password_hash, role)
            VALUES (?, ?, ?, ?)
            RETURNING id, username, password_hash, role, created_at, updated_at
            "#,
            id.to_string(),
            username,
            password_hash,
            role
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(user)
    }

    pub async fn get(&self, id: Uuid) -> Result<User, DbError> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, password_hash, role, created_at, updated_at
            FROM users
            WHERE id = ?
            "#,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(user)
    }

    pub async fn get_by_username(&self, username: &str) -> Result<User, DbError> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, password_hash, role, created_at, updated_at
            FROM users
            WHERE username = ?
            "#,
            username
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(user)
    }

    pub async fn list(&self) -> Result<Vec<User>, DbError> {
        let users = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, password_hash, role, created_at, updated_at
            FROM users
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(users)
    }

    pub async fn update_role(&self, id: Uuid, role: &str) -> Result<User, DbError> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users
            SET role = ?
            WHERE id = ?
            RETURNING id, username, password_hash, role, created_at, updated_at
            "#,
            role,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(user)
    }

    pub async fn delete(&self, id: Uuid) -> Result<(), DbError> {
        sqlx::query!(
            r#"
            DELETE FROM users
            WHERE id = ?
            "#,
            id.to_string()
        )
        .execute(self.db.pool())
        .await?;

        Ok(())
    }
}

/// 节点相关模型和 DAO
#[derive(Debug, sqlx::FromRow)]
pub struct Node {
    pub id: Uuid,
    pub name: String,
    pub address: String,
    pub port: u16,
    pub node_type: String,
    pub ip: String,
    pub mac: String,
    pub status: String,
    pub tunnel_type: String,
    pub last_active: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

pub struct NodeDao {
    db: Arc<Database>,
}

impl NodeDao {
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    pub async fn create(
        &self,
        name: &str,
        address: &str,
        port: i32,
        node_type: &str,
        ip: &str,
        mac: &str,
        status: &str,
        tunnel_type: &str,
    ) -> Result<Node, DbError> {
        let id = Uuid::new_v4();
        let node = sqlx::query_as!(
            Node,
            r#"
            INSERT INTO nodes (id, name, address, port, node_type, ip, mac, status, tunnel_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id, name, address, port, node_type, ip, mac, status, tunnel_type, last_active, created_at, updated_at
            "#,
            id.to_string(),
            name,
            address,
            port,
            node_type,
            ip,
            mac,
            status,
            tunnel_type
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(node)
    }

    pub async fn get(&self, id: Uuid) -> Result<Node, DbError> {
        let node = sqlx::query_as!(
            Node,
            r#"
            SELECT id, name, address, port, node_type, ip, mac, status, tunnel_type, last_active, created_at, updated_at
            FROM nodes
            WHERE id = ?
            "#,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(node)
    }

    pub async fn list(&self) -> Result<Vec<Node>, DbError> {
        let nodes = sqlx::query_as!(
            Node,
            r#"
            SELECT id, name, address, port, node_type, ip, mac, status, tunnel_type, last_active, created_at, updated_at
            FROM nodes
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(nodes)
    }

    pub async fn update_status(&self, id: Uuid, status: &str) -> Result<Node, DbError> {
        let node = sqlx::query_as!(
            Node,
            r#"
            UPDATE nodes
            SET status = ?
            WHERE id = ?
            RETURNING id, name, address, port, node_type, ip, mac, status, tunnel_type, last_active, created_at, updated_at
            "#,
            status,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(node)
    }

    pub async fn update_nat_type(&self, id: Uuid, nat_type: &str) -> Result<Node, DbError> {
        let node = sqlx::query_as!(
            Node,
            r#"
            UPDATE nodes
            SET node_type = ?
            WHERE id = ?
            RETURNING id, name, address, port, node_type, ip, mac, status, tunnel_type, last_active, created_at, updated_at
            "#,
            nat_type,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(node)
    }

    pub async fn delete(&self, id: Uuid) -> Result<(), DbError> {
        sqlx::query!(
            r#"
            DELETE FROM nodes
            WHERE id = ?
            "#,
            id.to_string()
        )
        .execute(self.db.pool())
        .await?;

        Ok(())
    }
}

/// 连接相关模型和 DAO
#[derive(Debug, sqlx::FromRow)]
pub struct Connection {
    pub id: Uuid,
    pub source_id: Uuid,
    pub target_id: Uuid,
    pub connection_type: String,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

pub struct ConnectionDao {
    db: Arc<Database>,
}

impl ConnectionDao {
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    pub async fn create(
        &self,
        source_id: Uuid,
        target_id: Uuid,
        connection_type: &str,
        status: &str,
    ) -> Result<Connection, DbError> {
        let id = Uuid::new_v4();
        let connection = sqlx::query_as!(
            Connection,
            r#"
            INSERT INTO connections (id, source_id, target_id, connection_type, status)
            VALUES (?, ?, ?, ?, ?)
            RETURNING id, source_id, target_id, connection_type, status, created_at, updated_at
            "#,
            id.to_string(),
            source_id.to_string(),
            target_id.to_string(),
            connection_type,
            status
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(connection)
    }

    pub async fn get(&self, id: Uuid) -> Result<Connection, DbError> {
        let connection = sqlx::query_as!(
            Connection,
            r#"
            SELECT id, source_id, target_id, connection_type, status, created_at, updated_at
            FROM connections
            WHERE id = ?
            "#,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(connection)
    }

    pub async fn list(&self) -> Result<Vec<Connection>, DbError> {
        let connections = sqlx::query_as!(
            Connection,
            r#"
            SELECT id, source_id, target_id, connection_type, status, created_at, updated_at
            FROM connections
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(connections)
    }

    pub async fn update_status(&self, id: Uuid, status: &str) -> Result<Connection, DbError> {
        let connection = sqlx::query_as!(
            Connection,
            r#"
            UPDATE connections
            SET status = ?
            WHERE id = ?
            RETURNING id, source_id, target_id, connection_type, status, created_at, updated_at
            "#,
            status,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(connection)
    }

    pub async fn delete(&self, id: Uuid) -> Result<(), DbError> {
        sqlx::query!(
            r#"
            DELETE FROM connections
            WHERE id = ?
            "#,
            id.to_string()
        )
        .execute(self.db.pool())
        .await?;

        Ok(())
    }
}

/// 配置相关模型和 DAO
#[derive(Debug, sqlx::FromRow)]
pub struct Config {
    pub id: Uuid,
    pub name: String,
    pub value: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

pub struct ConfigDao {
    db: Arc<Database>,
}

impl ConfigDao {
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    pub async fn create(&self, name: &str, value: &str, description: &str) -> Result<Config, DbError> {
        let id = Uuid::new_v4();
        let config = sqlx::query_as!(
            Config,
            r#"
            INSERT INTO configs (id, name, value, description)
            VALUES (?, ?, ?, ?)
            RETURNING id, name, value, description, created_at, updated_at
            "#,
            id.to_string(),
            name,
            value,
            description
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(config)
    }

    pub async fn get(&self, id: Uuid) -> Result<Config, DbError> {
        let config = sqlx::query_as!(
            Config,
            r#"
            SELECT id, name, value, description, created_at, updated_at
            FROM configs
            WHERE id = ?
            "#,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(config)
    }

    pub async fn list(&self) -> Result<Vec<Config>, DbError> {
        let configs = sqlx::query_as!(
            Config,
            r#"
            SELECT id, name, value, description, created_at, updated_at
            FROM configs
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(configs)
    }

    pub async fn update(&self, id: Uuid, value: &str, description: &str) -> Result<Config, DbError> {
        let config = sqlx::query_as!(
            Config,
            r#"
            UPDATE configs
            SET value = ?, description = ?
            WHERE id = ?
            RETURNING id, name, value, description, created_at, updated_at
            "#,
            value,
            description,
            id.to_string()
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(config)
    }

    pub async fn delete(&self, id: Uuid) -> Result<(), DbError> {
        sqlx::query!(
            r#"
            DELETE FROM configs
            WHERE id = ?
            "#,
            id.to_string()
        )
        .execute(self.db.pool())
        .await?;

        Ok(())
    }
}

/// 系统日志相关模型和 DAO
#[derive(Debug, sqlx::FromRow)]
pub struct SystemLog {
    pub id: Uuid,
    pub level: String,
    pub message: String,
    pub created_at: DateTime<Utc>,
}

pub struct SystemLogDao {
    db: Arc<Database>,
}

impl SystemLogDao {
    pub fn new(db: Arc<Database>) -> Self {
        Self { db }
    }

    pub async fn create(&self, level: &str, message: &str) -> Result<SystemLog, DbError> {
        let id = Uuid::new_v4();
        let log = sqlx::query_as!(
            SystemLog,
            r#"
            INSERT INTO system_logs (id, level, message)
            VALUES (?, ?, ?)
            RETURNING id, level, message, created_at
            "#,
            id.to_string(),
            level,
            message
        )
        .fetch_one(self.db.pool())
        .await?;

        Ok(log)
    }

    pub async fn list(&self) -> Result<Vec<SystemLog>, DbError> {
        let logs = sqlx::query_as!(
            SystemLog,
            r#"
            SELECT id, level, message, created_at
            FROM system_logs
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(logs)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AppConfig;

    async fn setup_test_db() -> Arc<Database> {
        let config = AppConfig::default();
        let db = Database::new(&config.database_url()).await.unwrap();
        db.init().await.unwrap();
        Arc::new(db)
    }

    #[tokio::test]
    async fn test_user_operations() {
        let db = setup_test_db().await;
        let user_dao = UserDao::new(db.clone());

        // 测试创建用户
        let user = user_dao.create("test_user", "password_hash", "admin").await.unwrap();
        assert_eq!(user.username, "test_user");
        assert_eq!(user.role, "admin");

        // 测试查找用户
        let found_user = user_dao.get_by_username("test_user").await.unwrap();
        assert_eq!(found_user.id, user.id);

        // 测试更新角色
        let updated_user = user_dao.update_role(user.id, "user").await.unwrap();
        assert_eq!(updated_user.role, "user");

        // 测试删除用户
        user_dao.delete(user.id).await.unwrap();
    }

    #[tokio::test]
    async fn test_node_operations() {
        let db = setup_test_db().await;
        let node_dao = NodeDao::new(db.clone());

        // 测试创建节点
        let node = node_dao.create(
            "test_node",
            "1.2.3.4",
            655,
            "full_cone",
            "192.168.1.1",
            "00:11:22:33:44:55",
            "inactive",
            "full_cone",
        ).await.unwrap();
        assert_eq!(node.name, "test_node");
        assert_eq!(node.node_type, "full_cone");

        // 测试获取节点
        let found_node = node_dao.get(node.id).await.unwrap();
        assert_eq!(found_node.id, node.id);

        // 测试更新状态
        let updated_node = node_dao.update_status(node.id, "inactive").await.unwrap();
        assert_eq!(updated_node.status, "inactive");

        // 测试删除节点
        node_dao.delete(node.id).await.unwrap();
    }

    #[tokio::test]
    async fn test_node_connection_operations() {
        let db = setup_test_db().await;
        let node_dao = NodeDao::new(db.clone());
        let connection_dao = ConnectionDao::new(db.clone());

        // 创建测试节点
        let node1 = node_dao.create(
            "node1",
            "1.2.3.4",
            655,
            "full_cone",
            "192.168.1.1",
            "00:11:22:33:44:55",
            "inactive",
            "full_cone",
        ).await.unwrap();

        let node2 = node_dao.create(
            "node2",
            "5.6.7.8",
            655,
            "full_cone",
            "192.168.1.2",
            "00:11:22:33:44:56",
            "inactive",
            "full_cone",
        ).await.unwrap();

        // 测试创建连接
        let connection = connection_dao.create(node1.id, node2.id, "full_cone", "active").await.unwrap();
        assert_eq!(connection.source_id, node1.id);
        assert_eq!(connection.target_id, node2.id);

        // 测试获取连接
        let found_connection = connection_dao.get(connection.id).await.unwrap();
        assert_eq!(found_connection.id, connection.id);

        // 测试更新状态
        let updated_connection = connection_dao.update_status(connection.id, "active").await.unwrap();
        assert_eq!(updated_connection.status, "active");

        // 测试删除连接
        connection_dao.delete(connection.id).await.unwrap();

        // 清理测试节点
        node_dao.delete(node1.id).await.unwrap();
        node_dao.delete(node2.id).await.unwrap();
    }

    #[tokio::test]
    async fn test_node_config_operations() {
        let db = setup_test_db().await;
        let node_dao = NodeDao::new(db.clone());
        let config_dao = ConfigDao::new(db.clone());

        // 创建测试节点
        let node = node_dao.create(
            "test_node",
            "1.2.3.4",
            655,
            "full_cone",
            "192.168.1.1",
            "00:11:22:33:44:55",
            "inactive",
            "full_cone",
        ).await.unwrap();

        // 测试创建配置
        let content = r#"
            [network]
            name = "test"
            port = 655
            mode = "switch"
        "#;
        let config = config_dao.create("test", content, "Test configuration").await.unwrap();
        assert_eq!(config.name, "test");

        // 测试获取配置
        let found_config = config_dao.get(config.id).await.unwrap();
        assert_eq!(found_config.id, config.id);

        // 测试获取配置历史
        let configs = config_dao.list().await.unwrap();
        assert_eq!(configs.len(), 1);
        assert_eq!(configs[0].id, config.id);

        // 清理测试节点
        node_dao.delete(node.id).await.unwrap();
    }

    #[tokio::test]
    async fn test_system_log_operations() {
        let db = setup_test_db().await;
        let log_dao = SystemLogDao::new(db.clone());

        // 测试创建日志
        let log = log_dao.create("info", "Test log message").await.unwrap();
        assert_eq!(log.level, "info");
        assert_eq!(log.message, "Test log message");

        // 测试获取日志列表
        let logs = log_dao.list().await.unwrap();
        assert_eq!(logs.len(), 1);
        assert_eq!(logs[0].id, log.id);
    }
} 