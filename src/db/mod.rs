use sqlx::{
    mysql::{MySqlPool, MySqlPoolOptions},
    Error as SqlxError,
    Row,
};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use tracing::{info, error};
use bcrypt::BcryptError;

use crate::{config::AppConfig, models::{User, Node, Connection, Config, Log}};

/// 数据库错误
#[derive(Debug, thiserror::Error)]
pub enum DbError {
    #[error("数据库错误: {0}")]
    Database(#[from] SqlxError),
    #[error("记录未找到")]
    NotFound,
    #[error("记录已存在")]
    AlreadyExists,
    #[error("无效的输入: {0}")]
    InvalidInput(String),
    #[error("迁移错误: {0}")]
    MigrationError(String),
    #[error("无效的凭据")]
    InvalidCredentials,
    #[error("其他错误: {0}")]
    Other(String),
}

impl From<sqlx::migrate::MigrateError> for DbError {
    fn from(err: sqlx::migrate::MigrateError) -> Self {
        DbError::MigrationError(err.to_string())
    }
}

impl From<BcryptError> for DbError {
    fn from(e: BcryptError) -> Self {
        DbError::Other(e.to_string())
    }
}

/// 数据库连接池
pub struct Database {
    pool: MySqlPool,
}

impl Database {
    /// 创建新的数据库连接
    pub async fn new(database_url: &str) -> Result<Self, DbError> {
        let pool = MySqlPoolOptions::new()
            .max_connections(10)
            .connect(database_url)
            .await?;

        Ok(Self { pool })
    }

    /// 创建内存数据库（用于测试）
    pub async fn new_in_memory() -> Result<Self, DbError> {
        // MySQL 不支持内存数据库，使用测试数据库
        let database_url = "mysql://root:password@localhost:3306/tinc_vpn_test";
        Self::new(database_url).await
    }

    /// 获取连接池
    pub fn pool(&self) -> &MySqlPool {
        &self.pool
    }

    /// 初始化数据库表
    pub async fn init(&self) -> Result<(), DbError> {
        // 运行迁移
        sqlx::migrate!("./migrations")
            .run(&self.pool)
            .await?;
        
        info!("数据库初始化完成");
        Ok(())
    }

    // 用户相关操作
    pub async fn create_user(&self, user: &User) -> Result<(), DbError> {
        sqlx::query(
            r#"
            INSERT INTO users (id, username, email, password, role, created_at, updated_at, last_login)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(user.id.to_string())
        .bind(&user.username)
        .bind(&user.email)
        .bind(&user.password)
        .bind(&user.role)
        .bind(user.created_at)
        .bind(user.updated_at)
        .bind(user.last_login)
        .execute(&self.pool)
        .await?;
        Ok(())
    }

    pub async fn get_user(&self, id: Uuid) -> Result<User, DbError> {
        let row = sqlx::query(
            r#"
            SELECT id, username, email, password, role, created_at, updated_at, last_login
            FROM users WHERE id = ?
            "#
        )
        .bind(id.to_string())
        .fetch_one(&self.pool)
        .await?;

        Ok(User {
            id: Uuid::parse_str(row.get("id")).map_err(|_| DbError::InvalidInput("Invalid UUID".to_string()))?,
            username: row.get("username"),
            email: row.get("email"),
            password: row.get("password"),
            role: row.get("role"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
            last_login: row.get("last_login"),
        })
    }

    pub async fn get_user_by_username(&self, username: &str) -> Result<User, DbError> {
        let row = sqlx::query!(
            r#"
            SELECT id, username, email, password, role, created_at, updated_at, last_login
            FROM users WHERE username = ?
            "#,
            username
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(User {
            id: Uuid::parse_str(&row.id).map_err(|_| DbError::InvalidInput("Invalid UUID".to_string()))?,
            username: row.username,
            email: row.email,
            password: row.password,
            role: row.role,
            created_at: row.created_at,
            updated_at: row.updated_at,
            last_login: row.last_login,
        })
    }

    pub async fn list_users(&self, limit: i64, offset: i64) -> Result<Vec<User>, DbError> {
        let rows = sqlx::query!(
            r#"
            SELECT id, username, email, password, role, created_at, updated_at, last_login
            FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?
            "#,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        let mut users = Vec::new();
        for row in rows {
            users.push(User {
                id: Uuid::parse_str(&row.id).map_err(|_| DbError::InvalidInput("Invalid UUID".to_string()))?,
                username: row.username,
                email: row.email,
                password: row.password,
                role: row.role,
                created_at: row.created_at,
                updated_at: row.updated_at,
                last_login: row.last_login,
            });
        }
        Ok(users)
    }

    pub async fn count_users(&self) -> Result<i64, DbError> {
        let row = sqlx::query!("SELECT COUNT(*) as count FROM users")
            .fetch_one(&self.pool)
            .await?;
        Ok(row.count)
    }

    pub async fn update_user(&self, user: &User) -> Result<(), DbError> {
        sqlx::query!(
            r#"
            UPDATE users SET username = ?, email = ?, password = ?, role = ?, updated_at = ?
            WHERE id = ?
            "#,
            user.username,
            user.email,
            user.password,
            user.role,
            user.updated_at,
            user.id.to_string()
        )
        .execute(&self.pool)
        .await?;
        Ok(())
    }

    pub async fn update_user_last_login(&self, id: Uuid) -> Result<(), DbError> {
        let now = Utc::now();
        sqlx::query!(
            r#"
            UPDATE users SET last_login = ? WHERE id = ?
            "#,
            now,
            id.to_string()
        )
        .execute(&self.pool)
        .await?;
        Ok(())
    }

    pub async fn delete_user(&self, id: Uuid) -> Result<(), DbError> {
        sqlx::query!("DELETE FROM users WHERE id = ?", id.to_string())
            .execute(&self.pool)
            .await?;
        Ok(())
    }
}
