use jsonwebtoken::{encode, decode, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Val<PERSON><PERSON>, errors::Error as JwtError};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Utc, Duration};
use serde::{Serialize, Deserialize};
use thiserror::Error;
use uuid::Uuid;

use crate::models::Claims;

/// 认证错误类型
#[derive(Debug, Error)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,

    #[error("JWT error: {0}")]
    Jwt(#[from] JwtError),

    #[error("Password hash error: {0}")]
    PasswordHash(#[from] bcrypt::BcryptError),

    #[error("Token expired")]
    TokenExpired,

    #[error("Invalid token")]
    InvalidToken,

    #[error("User not found")]
    UserNotFound,

    #[error("Invalid password")]
    InvalidPassword,
}

/// 认证工具
#[derive(<PERSON>lone)]
pub struct Auth {
    secret: String,
    expires_in: i64,
}

impl Auth {
    /// 创建新的认证工具
    pub fn new(secret: String, expires_in: i64) -> Self {
        Self {
            secret,
            expires_in,
        }
    }

    /// 生成 JWT token
    pub fn generate_token(&self, user_id: Uuid, role: &str) -> Result<String, AuthError> {
        let now = Utc::now();
        let exp = (now + Duration::seconds(self.expires_in)).timestamp() as usize;
        let iat = now.timestamp() as usize;

        let claims = Claims {
            sub: user_id.to_string(),
            exp,
            iat,
            role: role.to_string(),
        };

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_bytes()),
        )?;

        Ok(token)
    }

    /// 验证 JWT token
    pub fn verify_token(&self, token: &str) -> Result<Claims, AuthError> {
        let validation = Validation::default();
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.secret.as_bytes()),
            &validation,
        )?;

        Ok(token_data.claims)
    }

    /// 加密密码
    pub fn hash_password(&self, password: &str) -> Result<String, AuthError> {
        let hashed = hash(password, DEFAULT_COST)?;
        Ok(hashed)
    }

    /// 验证密码
    pub fn verify_password(&self, password: &str, hash: &str) -> Result<bool, AuthError> {
        let valid = verify(password, hash)?;
        Ok(valid)
    }
}

/// 认证服务
#[derive(Clone)]
pub struct AuthService {
    auth: Auth,
}

impl AuthService {
    /// 创建新的认证服务
    pub fn new() -> Self {
        let auth = Auth::new("default-secret".to_string(), 3600);
        Self { auth }
    }

    /// 生成 JWT token
    pub fn generate_token(&self, user: &crate::models::User) -> Result<String, AuthError> {
        self.auth.generate_token(user.id, &user.role)
    }

    /// 验证 JWT token
    pub fn verify_token(&self, token: &str) -> Result<Claims, AuthError> {
        self.auth.verify_token(token)
    }

    /// 加密密码
    pub fn hash_password(&self, password: &str) -> Result<String, AuthError> {
        self.auth.hash_password(password)
    }

    /// 验证密码
    pub fn verify_password(&self, password: &str, hash: &str) -> Result<bool, AuthError> {
        self.auth.verify_password(password, hash)
    }
}

/// 用户信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: Uuid,
    pub username: String,
    pub role: String,
}

/// 登录请求
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// 登录响应
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserInfo,
    pub expires_at: i64,
}

impl From<crate::models::User> for UserInfo {
    fn from(user: crate::models::User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            role: user.role,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_auth_flow() {
        let auth = Auth::new("test-secret".to_string(), 3600);
        let user_id = Uuid::new_v4();
        let role = "admin";

        // 测试 token 生成和验证
        let token = auth.generate_token(user_id, role).unwrap();
        let claims = auth.verify_token(&token).unwrap();
        assert_eq!(claims.sub, user_id.to_string());
        assert_eq!(claims.role, role);

        // 测试密码加密和验证
        let password = "test-password";
        let hashed = auth.hash_password(password).unwrap();
        assert!(auth.verify_password(password, &hashed).unwrap());
        assert!(!auth.verify_password("wrong-password", &hashed).unwrap());
    }
} 