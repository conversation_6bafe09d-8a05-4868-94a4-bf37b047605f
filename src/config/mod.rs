use std::path::{Path, PathBuf};
use std::fs::File;
use serde::{Deserialize, Serialize};
use tracing::{info, error};
use uuid::Uuid;
use std::fs;
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use std::io::Read;
use tracing_subscriber::util::SubscriberInitExt;

use crate::{
    error::{AppError, ConfigError},
    utils,
};

/// 应用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 数据库配置
    pub database: DatabaseConfig,
    /// 服务器配置
    pub server: ServerConfig,
    /// 日志配置
    pub log: LogConfig,
    /// 网络配置
    pub network: NetworkConfig,
    pub database_url: String,
    pub log_dir: PathBuf,
    pub config_dir: PathBuf,
}

/// 数据库配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库URL
    pub url: String,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时（秒）
    pub connect_timeout: u64,
}

/// 服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// 监听地址
    pub host: String,
    /// 监听端口
    pub port: u16,
    /// 工作线程数
    pub workers: usize,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogConfig {
    /// 日志级别
    pub level: String,
    /// 日志文件路径
    pub file: String,
    /// 最大文件大小（MB）
    pub max_size: u64,
    /// 最大文件数
    pub max_files: usize,
}

/// 网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// 监听地址
    pub listen_addr: String,
    /// 接口名称
    pub interface_name: String,
    /// 虚拟网络
    pub virtual_network: String,
    /// MTU
    pub mtu: u16,
    /// 启用NAT穿透
    pub enable_nat: bool,
    /// 启用压缩
    pub enable_compression: bool,
    /// 启用加密
    pub enable_encryption: bool,
    /// 连接超时（秒）
    pub connection_timeout: u64,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
}

impl AppConfig {
    /// 获取数据库 URL
    pub fn database_url(&self) -> &str {
        &self.database.url
    }

    /// 获取服务器地址
    pub fn server_addr(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }

    pub fn config_dir(&self) -> PathBuf {
        self.config_dir.clone()
    }
}

/// 默认配置
impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig {
                url: "mysql://root:password@localhost:3306/tinc_vpn".to_string(),
                max_connections: 10,
                connect_timeout: 30,
            },
            server: ServerConfig {
                host: "0.0.0.0".to_string(),
                port: 3000,
                workers: 4,
            },
            log: LogConfig {
                level: "info".to_string(),
                file: "logs/app.log".to_string(),
                max_size: 100,
                max_files: 5,
            },
            network: NetworkConfig {
                listen_addr: "0.0.0.0:655".to_string(),
                interface_name: "tinc0".to_string(),
                virtual_network: "10.0.0.0/24".to_string(),
                mtu: 1500,
                enable_nat: true,
                enable_compression: true,
                enable_encryption: true,
                connection_timeout: 30,
                heartbeat_interval: 5,
            },
            database_url: "mysql://root:password@localhost:3306/tinc_vpn".to_string(),
            log_dir: PathBuf::from("/var/log/tinc-rust"),
            config_dir: PathBuf::from("/etc/tinc-rust"),
        }
    }
}

/// 配置管理器
#[derive(Debug, Clone)]
pub struct ConfigManager {
    config_dir: PathBuf,
    config: AppConfig,
}

impl ConfigManager {
    /// 创建新的配置管理器
    pub fn new(config_dir: Option<impl AsRef<Path>>) -> Self {
        let config_dir = config_dir
            .map(|p| p.as_ref().to_path_buf())
            .unwrap_or_else(|| PathBuf::from("config"));
        
        Self {
            config_dir,
            config: AppConfig::default(),
        }
    }

    pub async fn init(&self) -> Result<(), std::io::Error> {
        if !self.config_dir.exists() {
            std::fs::create_dir_all(&self.config_dir)?;
        }
        Ok(())
    }

    /// 加载配置
    pub async fn load_config(&mut self) -> Result<(), AppError> {
        let config_file = self.config_dir.join("config.toml");
        if !config_file.exists() {
            info!("配置文件不存在，使用默认配置");
            return Ok(());
        }

        let content = std::fs::read_to_string(config_file)?;
        self.config = toml::from_str(&content)?;
        info!("配置加载成功");
        Ok(())
    }

    /// 保存配置
    pub async fn save_config(&self) -> Result<(), AppError> {
        let config_file = self.config_dir.join("config.toml");
        let content = toml::to_string_pretty(&self.config)?;
        std::fs::write(config_file, content)?;
        info!("配置保存成功");
        Ok(())
    }

    /// 获取配置
    pub fn get_config(&self) -> &AppConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: AppConfig) {
        self.config = config;
    }

    /// 验证配置
    pub fn validate_config(&self) -> Result<(), ConfigError> {
        // 验证数据库配置
        if self.config.database.url.is_empty() {
            return Err(ConfigError::Validation("数据库URL不能为空".to_string()));
        }
        if self.config.database.max_connections == 0 {
            return Err(ConfigError::Validation("最大连接数必须大于0".to_string()));
        }
        if self.config.database.connect_timeout == 0 {
            return Err(ConfigError::Validation("连接超时必须大于0".to_string()));
        }

        // 验证服务器配置
        if !utils::is_valid_ip(&self.config.server.host) {
            return Err(ConfigError::Validation("无效的服务器地址".to_string()));
        }
        if !utils::is_valid_port(self.config.server.port.into()) {
            return Err(ConfigError::Validation("无效的服务器端口".to_string()));
        }
        if self.config.server.workers == 0 {
            return Err(ConfigError::Validation("工作线程数必须大于0".to_string()));
        }

        // 验证日志配置
        if self.config.log.level.is_empty() {
            return Err(ConfigError::Validation("日志级别不能为空".to_string()));
        }
        if self.config.log.file.is_empty() {
            return Err(ConfigError::Validation("日志文件路径不能为空".to_string()));
        }
        if self.config.log.max_size == 0 {
            return Err(ConfigError::Validation("最大文件大小必须大于0".to_string()));
        }
        if self.config.log.max_files == 0 {
            return Err(ConfigError::Validation("最大文件数必须大于0".to_string()));
        }

        // 验证网络配置
        if !utils::is_valid_ip(&self.config.network.listen_addr.split(':').next().unwrap_or("")) {
            return Err(ConfigError::Validation("无效的监听地址".to_string()));
        }
        if !utils::is_valid_port(self.config.network.listen_addr.split(':').nth(1).unwrap_or("0").parse::<u16>().unwrap_or(0).into()) {
            return Err(ConfigError::Validation("无效的监听端口".to_string()));
        }
        if self.config.network.interface_name.is_empty() {
            return Err(ConfigError::Validation("接口名称不能为空".to_string()));
        }
        if !utils::is_valid_cidr(&self.config.network.virtual_network) {
            return Err(ConfigError::Validation("无效的虚拟网络".to_string()));
        }
        if !utils::is_valid_mtu(self.config.network.mtu) {
            return Err(ConfigError::Validation("无效的MTU".to_string()));
        }
        if !utils::is_valid_timeout(self.config.network.connection_timeout) {
            return Err(ConfigError::Validation("无效的连接超时".to_string()));
        }
        if !utils::is_valid_heartbeat(self.config.network.heartbeat_interval) {
            return Err(ConfigError::Validation("无效的心跳间隔".to_string()));
        }

        Ok(())
    }

    /// 获取节点配置
    pub async fn get_node_config(&self, node_id: Uuid) -> Result<NetworkConfig, ConfigError> {
        let config_path = self.config_dir.join(format!("{}.json", node_id));
        if !config_path.exists() {
            return Err(ConfigError::NotFound);
        }

        let content = std::fs::read_to_string(config_path)?;
        let config: NetworkConfig = serde_json::from_str(&content)?;
        Ok(config)
    }

    /// 保存节点配置
    pub async fn save_node_config(&self, node_id: Uuid, config: &NetworkConfig) -> Result<(), ConfigError> {
        let config_path = self.config_dir.join(format!("{}.json", node_id));
        let content = serde_json::to_string_pretty(config)?;
        std::fs::write(config_path, content)?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_config_manager() {
        let temp_dir = tempdir().unwrap();
        let config_dir = temp_dir.path().to_path_buf();
        let mut manager = ConfigManager {
            config_dir,
            config: AppConfig::default(),
        };

        // 测试默认配置
        assert_eq!(manager.config.database.url, "postgres://postgres:postgres@localhost:5432/tinc");
        assert_eq!(manager.config.server.port, 3000);
        assert_eq!(manager.config.log.level, "info");
        assert_eq!(manager.config.network.mtu, 1500);

        // 测试配置验证
        assert!(manager.validate_config().is_ok());

        // 测试保存和加载配置
        manager.save_config().await.unwrap();
        manager.load_config().await.unwrap();

        // 测试节点配置
        let node_id = Uuid::new_v4();
        let node_config = NetworkConfig {
            listen_addr: "0.0.0.0:655".to_string(),
            interface_name: "tinc0".to_string(),
            virtual_network: "10.0.0.0/24".to_string(),
            mtu: 1500,
            enable_nat: true,
            enable_compression: true,
            enable_encryption: true,
            connection_timeout: 30,
            heartbeat_interval: 5,
        };

        manager.save_node_config(node_id, &node_config).await.unwrap();
        let loaded_config = manager.get_node_config(node_id).await.unwrap();
        assert_eq!(loaded_config.mtu, node_config.mtu);
        assert_eq!(loaded_config.enable_nat, node_config.enable_nat);

        // 测试获取不存在的节点配置
        assert!(manager.get_node_config(Uuid::new_v4()).await.is_err());
    }
} 