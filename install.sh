#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印带颜色的消息
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    error "请使用 root 用户运行此脚本"
fi

# 检查系统要求
if [ -f /etc/os-release ]; then
    . /etc/os-release
    if [ "$ID" != "debian" ] && [ "$ID" != "ubuntu" ]; then
        warn "此脚本仅支持 Debian/Ubuntu 系统"
    fi
else
    warn "无法确定操作系统类型"
fi

# 安装依赖
log "安装依赖..."
apt-get update
apt-get install -y \
    curl \
    build-essential \
    pkg-config \
    libssl-dev \
    postgresql \
    postgresql-contrib

# 安装 Rust
log "安装 Rust..."
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source $HOME/.cargo/env

# 创建系统用户
log "创建系统用户..."
if ! id "tinc" &>/dev/null; then
    useradd -r -s /bin/false tinc
fi

# 创建应用目录
log "创建应用目录..."
mkdir -p /opt/tinc-rust
mkdir -p /var/lib/tinc-rust/{configs,logs}

# 设置权限
chown -R tinc:tinc /opt/tinc-rust
chown -R tinc:tinc /var/lib/tinc-rust

# 复制文件
log "复制文件..."
cp target/release/tinc_rust /opt/tinc-rust/
cp tinc-rust.service /etc/systemd/system/

# 创建数据库
log "创建数据库..."
sudo -u postgres psql -c "CREATE DATABASE tinc;"
sudo -u postgres psql -c "CREATE USER tinc WITH PASSWORD 'tinc';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE tinc TO tinc;"

# 运行数据库迁移
log "运行数据库迁移..."
cd /opt/tinc-rust
./tinc_rust migrate

# 启动服务
log "启动服务..."
systemctl daemon-reload
systemctl enable tinc-rust
systemctl start tinc-rust

log "安装完成！"
log "服务状态: systemctl status tinc-rust"
log "查看日志: journalctl -u tinc-rust" 