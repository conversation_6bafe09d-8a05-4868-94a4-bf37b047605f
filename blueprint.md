# Rust Tinc VPN 系统蓝图

## 数据库表结构

### users 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | CHAR(36) | 用户唯一标识 | PRIMARY KEY |
| username | VARCHAR(255) | 用户名 | NOT NULL, UNIQUE |
| password_hash | VARCHAR(255) | 密码哈希 | NOT NULL |
| email | VARCHAR(255) | 邮箱地址 | NOT NULL, UNIQUE |
| role | VARCHAR(50) | 用户角色 | NOT NULL, DEFAULT 'user' |
| created_at | TIMESTAMP | 创建时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | 更新时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE |
| last_login | TIMESTAMP | 最后登录时间 | NULL |

### nodes 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | CHAR(36) | 节点唯一标识 | PRIMARY KEY |
| name | VARCHAR(255) | 节点名称 | NOT NULL |
| node_type | VARCHAR(50) | 节点类型 | NOT NULL, DEFAULT 'client' |
| ip | VARCHAR(45) | IP 地址 | NOT NULL |
| mac | VARCHAR(17) | MAC 地址 | NOT NULL |
| port | INT | 端口号 | NOT NULL, DEFAULT 655 |
| public_key | TEXT | 公钥 | NOT NULL |
| status | VARCHAR(50) | 状态 | NOT NULL, DEFAULT 'offline' |
| tunnel_type | VARCHAR(50) | 隧道类型 | NOT NULL, DEFAULT 'tcp' |
| nat_type | VARCHAR(50) | NAT 类型 | NULL |
| last_active | TIMESTAMP | 最后活跃时间 | NULL |
| created_at | TIMESTAMP | 创建时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | 更新时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE |

### connections 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | CHAR(36) | 连接唯一标识 | PRIMARY KEY |
| client_id | CHAR(36) | 客户端节点ID | NOT NULL, FOREIGN KEY |
| server_id | CHAR(36) | 服务端节点ID | NOT NULL, FOREIGN KEY |
| method_used | VARCHAR(50) | 连接方法 | NOT NULL |
| latency | INT | 延迟(ms) | NULL |
| status | VARCHAR(50) | 连接状态 | NOT NULL, DEFAULT 'connecting' |
| created_at | TIMESTAMP | 创建时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | 更新时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE |

### configs 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | CHAR(36) | 配置唯一标识 | PRIMARY KEY |
| client_id | CHAR(36) | 客户端节点ID | NOT NULL, FOREIGN KEY |
| version | INT | 配置版本号 | NOT NULL |
| hash | VARCHAR(64) | 配置哈希值 | NOT NULL |
| content | TEXT | 配置内容 | NOT NULL |
| created_at | TIMESTAMP | 创建时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

### policies 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | CHAR(36) | 策略唯一标识 | PRIMARY KEY |
| name | VARCHAR(100) | 策略名称 | NOT NULL, UNIQUE |
| description | TEXT | 策略描述 | NULL |
| bandwidth_limit | INT | 带宽限制(Mbps) | NULL |
| allow_ips | TEXT | 允许的IP列表 | NOT NULL |
| allow_ports | TEXT | 允许的端口列表 | NOT NULL |
| created_at | TIMESTAMP | 创建时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | 更新时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE |

### logs 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | CHAR(36) | 日志唯一标识 | PRIMARY KEY |
| log_type | VARCHAR(50) | 日志类型 | NOT NULL |
| message | TEXT | 日志消息 | NOT NULL |
| source | VARCHAR(100) | 日志来源 | NOT NULL |
| created_at | TIMESTAMP | 创建时间 | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

## 枚举值定义

### 用户角色 (role)
- `admin`: 管理员
- `user`: 普通用户
- `guest`: 访客

### 节点类型 (node_type)
- `client`: 客户端节点
- `server`: 服务端节点

### 节点状态 (status)
- `online`: 在线
- `offline`: 离线
- `connecting`: 连接中
- `error`: 错误状态

### 隧道类型 (tunnel_type)
- `tcp`: TCP 隧道
- `udp`: UDP 隧道
- `relay`: 中继隧道

### NAT 类型 (nat_type)
- `full_cone`: 完全锥形NAT
- `restricted_cone`: 限制锥形NAT
- `port_restricted_cone`: 端口限制锥形NAT
- `symmetric`: 对称NAT

### 连接方法 (method_used)
- `udp_hole_punching`: UDP 打洞
- `tcp_simultaneous_open`: TCP 同时打开
- `relay`: 中继连接

### 连接状态 (connection status)
- `connecting`: 连接中
- `connected`: 已连接
- `disconnected`: 已断开
- `failed`: 连接失败

### 日志类型 (log_type)
- `auth`: 认证日志
- `connection`: 连接日志
- `config`: 配置日志
- `nat`: NAT 探测日志
- `error`: 错误日志
- `system`: 系统日志

## API 接口路径

### 认证接口
- `POST /api/login`: 用户登录
- `GET /api/profile/me`: 获取当前用户信息

### 节点管理
- `GET /api/nodes`: 获取节点列表
- `POST /api/nodes`: 创建节点
- `GET /api/nodes/{id}`: 获取节点详情
- `PUT /api/nodes/{id}`: 更新节点
- `DELETE /api/nodes/{id}`: 删除节点

### 连接管理
- `GET /api/connections`: 获取连接列表
- `POST /api/connections/{id}/test`: 测试连接
- `POST /api/connections/{id}/terminate`: 断开连接

### NAT 探测
- `GET /api/nat/{client_id}`: 获取NAT类型
- `POST /api/nat/{client_id}/detect`: 重新探测NAT类型

### 配置管理
- `GET /api/configs/{client_id}`: 下载配置
- `POST /api/configs/{client_id}/push`: 推送配置
- `GET /api/configs/{client_id}/history`: 配置历史
- `POST /api/configs/{client_id}/rollback`: 回滚配置

### 用户权限
- `GET /api/users`: 用户列表
- `POST /api/users`: 创建用户
- `PUT /api/users/{id}`: 更新用户
- `DELETE /api/users/{id}`: 删除用户
- `POST /api/users/{id}/roles`: 设置角色
- `GET /api/roles`: 角色列表

### 监控日志
- `GET /api/logs`: 系统日志
- `GET /api/events`: 事件流
- `GET /metrics`: Prometheus 监控

## 配置文件结构

### 主配置文件 (config.toml)
```toml
[database]
url = "mysql://root:password@localhost:3306/tinc_vpn"
max_connections = 10
connect_timeout = 30

[server]
host = "0.0.0.0"
port = 3000
workers = 4

[log]
level = "info"
file = "logs/app.log"
max_size = 100
max_files = 5

[network]
listen_addr = "0.0.0.0:655"
interface_name = "tinc0"
virtual_network = "10.0.0.0/24"
mtu = 1500
enable_nat = true
enable_compression = true
enable_encryption = true
connection_timeout = 30
heartbeat_interval = 5
```

### 节点配置文件结构
```toml
[node]
name = "client-001"
type = "client"
ip = "********"
mac = "00:11:22:33:44:55"
port = 655

[crypto]
public_key = "..."
private_key = "..."

[network]
virtual_ip = "********/24"
gateway = "********"
dns = ["*******", "*******"]

[tunnel]
type = "tcp"
compression = true
encryption = true
```
