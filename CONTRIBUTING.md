# 贡献指南

感谢您对 Tinc Rust VPN 项目的关注！我们欢迎任何形式的贡献，包括但不限于：

- 报告 Bug
- 提出新功能建议
- 改进文档
- 提交代码修复
- 添加新功能

## 开发环境设置

1. 确保您已安装以下工具：
   - Rust 1.70.0 或更高版本
   - PostgreSQL 13 或更高版本
   - Git

2. 克隆仓库：
```bash
git clone https://github.com/your-username/tinc-rust.git
cd tinc-rust
```

3. 安装依赖：
```bash
make deps
```

4. 运行测试：
```bash
make test
```

## 代码规范

### 代码风格

- 使用 `rustfmt` 格式化代码
- 遵循 Rust 官方风格指南
- 使用 `clippy` 进行代码检查

### 提交规范

提交信息格式：
```
<类型>: <描述>

[可选的详细描述]

[可选的关闭 issue 信息]
```

类型包括：
- `feat`: 新功能
- `fix`: 修复 Bug
- `docs`: 文档更新
- `style`: 代码风格修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 分支管理

- `main`: 主分支，保持稳定
- `develop`: 开发分支
- `feature/*`: 功能分支
- `bugfix/*`: 修复分支
- `release/*`: 发布分支

## 提交流程

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

### Pull Request 要求

- 标题清晰描述改动内容
- 详细描述改动原因和影响
- 确保所有测试通过
- 更新相关文档
- 遵循代码规范

## 问题反馈

- 使用 GitHub Issues 报告问题
- 提供详细的问题描述
- 包含复现步骤
- 提供环境信息
- 添加相关日志

## 行为准则

- 尊重所有贡献者
- 接受建设性批评
- 关注问题本身
- 保持专业和友善

## 发布流程

1. 更新版本号
2. 更新 CHANGELOG.md
3. 创建发布分支
4. 进行测试
5. 合并到主分支
6. 创建发布标签

## 联系方式

- 项目维护者：[Your Name](mailto:<EMAIL>)
- 项目主页：[GitHub Repository](https://github.com/your-username/tinc-rust)

## 致谢

感谢所有为项目做出贡献的开发者！ 