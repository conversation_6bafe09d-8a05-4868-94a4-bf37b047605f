# Rust Tinc VPN 系统开发计划

## 项目概述
基于 spec-rust-tinc-vpn.md 规范，使用 Rust + Axum + MySQL 构建商业化 VPN 系统

## 技术架构
- **后端框架**: Axum + Tokio + SQLx + Tracing
- **数据库**: MySQL
- **认证**: JWT + RBAC
- **配置**: TOML + 加密 tar.gz
- **监控**: Prometheus metrics

## 模块结构
```
rust-tinc-vpn/
├── src/
│   ├── main.rs                 # 主入口
│   ├── config/                 # 配置管理模块
│   ├── crypto/                 # 加密与密钥交换
│   ├── net/                    # NAT 打洞与网络通信
│   ├── tunnel/                 # 隧道连接管理
│   ├── api/                    # REST API 控制层
│   ├── db/                     # 数据库模型与操作
│   ├── auth/                   # JWT 认证与 RBAC
│   ├── log/                    # 日志与事件系统
│   └── daemon/                 # 守护进程管理
├── migrations/                 # 数据库迁移文件
├── configs/                    # 配置文件模板
└── tests/                      # 测试用例
```

## 开发阶段

### 阶段 1: 项目基础设施
- [x] 项目结构创建
- [ ] Cargo.toml 依赖配置
- [ ] 基础配置文件模板
- [ ] 数据库连接与迁移

### 阶段 2: 核心模块实现
- [ ] config 模块：TOML 解析与加密配置
- [ ] crypto 模块：密钥交换与加密算法
- [ ] db 模块：数据库模型与操作
- [ ] auth 模块：JWT 认证与 RBAC

### 阶段 3: 网络与连接
- [ ] net 模块：NAT 探测与打洞策略
- [ ] tunnel 模块：连接生命周期管理
- [ ] WebSocket 通信支持

### 阶段 4: API 控制层
- [ ] 用户认证接口 (/api/login, /api/profile)
- [ ] 节点管理接口 (/api/nodes)
- [ ] 连接状态接口 (/api/connections)
- [ ] NAT 探测接口 (/api/nat)
- [ ] 配置管理接口 (/api/configs)
- [ ] 用户权限接口 (/api/users, /api/roles)

### 阶段 5: 监控与日志
- [ ] log 模块：统一日志系统
- [ ] 事件流接口 (/api/events)
- [ ] Prometheus metrics (/metrics)

### 阶段 6: 部署与测试
- [ ] Dockerfile 与部署脚本
- [ ] 单元测试与集成测试
- [ ] 性能测试与 NAT 模拟测试

## 数据库设计

### 核心表结构
1. **users**: 用户管理与权限
2. **nodes**: 客户端/服务端节点信息
3. **connections**: 连接会话状态
4. **configs**: 配置版本管理
5. **policies**: 访问策略与权限
6. **logs**: 系统日志与事件

## API 接口规范

### 认证接口
- POST /api/login
- GET /api/profile/me

### 节点管理
- GET /api/nodes
- POST /api/nodes
- PUT /api/nodes/{id}
- DELETE /api/nodes/{id}

### 连接管理
- GET /api/connections
- POST /api/connections/{id}/test
- POST /api/connections/{id}/terminate

### NAT 探测
- GET /api/nat/{client_id}
- POST /api/nat/{client_id}/detect

### 配置管理
- GET /api/configs/{client_id}
- POST /api/configs/{client_id}/push
- GET /api/configs/{client_id}/history
- POST /api/configs/{client_id}/rollback

### 用户权限
- GET /api/users
- POST /api/users
- PUT /api/users/{id}
- DELETE /api/users/{id}
- POST /api/users/{id}/roles
- GET /api/roles

### 监控日志
- GET /api/logs
- GET /api/events
- GET /metrics

## 安全策略
- 配置文件对称加密
- JWT + RBAC 访问控制
- WebSocket 信道签名验证
- 关键行为日志记录

## 部署要求
- 支持 cross 编译多平台
- systemd 守护进程支持
- dev/test/prod 环境配置
- 一键部署脚本
