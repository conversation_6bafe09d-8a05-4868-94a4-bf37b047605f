# 🎨 UI 样式指南：XXX 管理系统（ui-style-guide.md）

本指南用于规范 XXX 管理系统所有前端页面的样式、布局与组件一致性，确保开发与迭代过程可维护、可统一、响应式体验一致。

---

## 📐 一、通用布局规范

### 页面整体结构

- 页面整体采用居中布局，无论浏览器窗口大小如何，**左侧菜单栏（Sidebar）** 和 **右侧内容区域（Main Content）** 始终整体水平居中显示；
- 推荐结构：使用 `flex justify-center` 的容器包裹菜单栏和内容区域；
- 左侧菜单栏宽度固定，例如 `w-[240px]`；
- 右侧内容区域宽度自适应，最大宽度建议为 `1440px`，最小宽度适配移动端；
- 菜单栏和内容区域之间保持固定间距 `10px`，使用 `gap-[10px]` 控制；
- 页面应具有响应式设计，缩小时菜单与内容不重叠；
- 所有样式请使用 **Tailwind CSS 原子类** 实现；
- **不要更改任何现有功能结构，仅进行布局调整**；
- 必须确保 **兼容深色模式显示**（使用 `dark:` 前缀的 Tailwind 类支持暗色样式）；

### 内容区域布局

- 页面内容区域与左侧菜单保持固定间距：`20px`（如与上方 `10px` 冲突，以实际 UI 层级为准）；
- 内容区域内部采用 `flex` + `min-h-screen` 垂直居中；
- 所有内容区域顶部留出 `24px` 边距，内容模块之间垂直间距统一为 `16px`；

---

## 🧩 二、页面风格统一要求

适用于首页、域名规则库、IP规则库、出口管理、日志与监控、用户与权限等所有页面。

### 整体风格

- 科技感配色：深紫、科技蓝、蓝灰为主色系；
- 背景建议渐变色或蓝灰背景，内容区域白色卡片化；
- 所有页面元素风格统一：标题、卡片、表格、按钮使用统一风格；
- 内容居中对称，表格、图表、卡片布局不偏左右；
- 所有空表格显示统一灰色文字“暂无数据”，字体 `text-gray-400`，居中显示；

---

## 🔐 三、登录页面设计标准

### 背景与整体结构

- 背景使用渐变（如：`from-[#1a1f71] to-[#1e3c72]`），科技感强；
- 内容区域使用白色卡片（`rounded-2xl`，`shadow-lg`）；
- 登录框垂直水平居中，全屏响应式；
- Logo 或图标区域位于卡片顶部中央；

### 登录卡片内容

- 标题：`text-2xl`、`font-bold`、`text-center`，颜色 `#1f1f1f`；
- 子标题：`text-sm`、`text-gray-500`，简洁提示“管理员后台登录”；
- 输入框：`rounded-md`，左侧带图标（如 `user`、`lock`），hover 边框高亮；
- 登录按钮：
  - 渐变背景（如 `from-blue-500 to-purple-500`）；
  - hover 效果：`scale-105`、`shadow-lg`；
- 可选功能：记住密码、忘记密码、注册链接，使用 `text-xs` 排布在按钮下方；

---

## 🧾 四、表格样式

### 表头

- 字体大小：`14px`，字体加粗；
- 表头背景色可选：`#fafafa` 或透明；
- 表头与标题间距统一为 `16px`；

### 表格内容

- 字体大小统一为 `14px`；
- 列宽根据内容自动缩放，禁止横向滚动条；
- 内容垂直居中、行高统一；
- 表格底部分页组件统一右对齐，间距为 `16px`；
- 空表格显示：“暂无数据”，灰色、居中；

---

## 🧭 五、菜单与导航栏

- 左侧菜单栏固定宽度 `240px`；
- 选中菜单项高亮，背景色 `#2f54eb`，文字白色；
- 菜单字体大小：`14px`，行高：`44px`；
- 图标左侧显示，统一采用 Iconify 图标库；
- 所有菜单项之间垂直间距为 `8px`；
- 支持展开/折叠，折叠后图标居中显示；

---

## 📱 六、响应式与组件风格建议

- 所有布局使用 `flex` + `gap` + `grid`，保持自适应；
- 在 `1440px`、`1280px`、`768px` 下测试显示一致性；
- 使用 `TailwindCSS` 或 `Element Plus` 组件；
- 所有卡片组件使用 `rounded-xl` + `shadow-md` 组合；
- 状态卡片建议图标 + 数字 + 标签形式，提升辨识度；

---

## ✅ 七、按钮与表单控件

### 按钮

- 主按钮：蓝色渐变、hover 时放大、阴影；
- 字体大小：`14px`，高度：`36px`，按钮之间 `mr-3`（即 `margin-right: 12px`）；

### 输入框

- 圆角：`rounded-md`；
- placeholder 使用灰色提示字体；
- 支持 error 状态标红、提示信息统一字体与间距；

---

## 🧪 八、开发建议

- 所有页面组件使用统一 layout 模板；
- 所有样式变量抽离至 `variables.scss`；
- 所有自定义组件使用统一命名、结构清晰、props 明确；
- 推荐使用 Tailwind + SCSS 模块化、BEM 命名法；
